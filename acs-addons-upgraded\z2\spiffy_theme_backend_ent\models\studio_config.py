# -*- coding: utf-8 -*-
# See LICENSE file for full copyright and licensing details.
# Developed by Bizople Solutions Pvt. Ltd.

import logging
from lxml import etree
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class StudioConfig(models.Model):
    _name = 'studio.config'
    _description = 'Studio Configuration'
    _order = 'name, id'

    name = fields.Char(string='Name', required=True)
    is_active = fields.Boolean(string='Active', default=True)
    customization_type = fields.Selection([
        ('view', 'View Customization'),
        ('model', 'Model Customization'),
        ('action', 'Action Customization')
    ], string='Customization Type', required=True)
    
    view_id = fields.Many2one('ir.ui.view', string='View')
    model_id = fields.Many2one('ir.model', string='Model')
    action_id = fields.Many2one('ir.actions.actions', string='Action')
    
    studio_xml = fields.Text(string='Studio XML', help='Custom view architecture')
    notes = fields.Text(string='Notes')
    
    _sql_constraints = [
        ('name_uniq', 'unique(name)', 'Configuration name must be unique!')
    ]

    @api.constrains('studio_xml')
    def _check_studio_xml(self):
        """Validate studio XML architecture"""
        for record in self:
            if record.studio_xml:
                try:
                    etree.fromstring(record.studio_xml)
                except Exception as e:
                    raise ValidationError(_('Invalid XML Architecture: %s') % str(e))

    @api.constrains('customization_type', 'view_id', 'model_id', 'action_id')
    def _check_required_fields(self):
        """Ensure required fields based on customization type"""
        for record in self:
            if record.customization_type == 'view' and not record.view_id:
                raise ValidationError(_('View is required for view customization'))
            elif record.customization_type == 'model' and not record.model_id:
                raise ValidationError(_('Model is required for model customization'))
            elif record.customization_type == 'action' and not record.action_id:
                raise ValidationError(_('Action is required for action customization'))

    @api.model
    def get_studio_features(self):
        """Get studio features configuration"""
        return {
            'customization_enabled': True,
            'sidebar_mode': self.env['backend.config'].search([], limit=1).enterprise_sidebar_mode or 'expanded'
        }

    def apply_studio_customization(self):
        """Apply studio customizations"""
        self.ensure_one()
        if not self.is_active:
            return False

        if self.customization_type == 'view' and self.view_id and self.studio_xml:
            try:
                self.view_id.write({'arch': self.studio_xml})
                _logger.info('Successfully applied studio customization for view %s', self.view_id.name)
            except Exception as e:
                _logger.error('Failed to apply studio customization: %s', str(e))
                raise ValidationError(_('Failed to apply customization: %s') % str(e))
        return True 
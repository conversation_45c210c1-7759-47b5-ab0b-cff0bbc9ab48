// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
body.o_web_client {
    &.top_menu_vertical,&.top_menu_vertical_mini,&.top_menu_vertical_mini_2{
        &:not(.o_website_fullscreen){
            .o_ace_view_editor:not(.oe_form_field){
                top: 0 !important;
            }
        }
        .o_main_navbar{
            .dropdown-toggle{
                padding-left: 8px;
                padding-right: 8px;
            }
            .form-switch .form-check-input:checked~.form-check-label::before{
                background-color: #28a745 !important;
            }
            .form-switch .form-check-label::before {
                background-color: #e6586c !important;
            }
            .form-switch .form-check-label::after {
                background-color: var(--biz-theme-primary-text-color) !important;
            }
        }
        &.o_website_fullscreen, &.editor_has_snippets, &.editor_has_dummy_snippets {
            header {
                .header_menu_right_content, .header_menu_right_content_toggler {
                    transform: translate(100%, -50%) !important;
                }
                .o_main_navbar {
                    transform: translate(-100%, 0) !important;
                    height: 0 !important;
                    display: none !important;
                }
                .fav_app_island {
                    bottom: 0;
                    transform: translate(-50%,100%);
                }
            }
            .o_action_manager {
                transition: 0.3s;
                padding: 0px !important;
            }
        }
    }
    &.top_menu_vertical {
        header {
            .o_menu_systray_item:not(.o_edit_website_container) {	
                margin-left: auto;
            }
        }
    }
    &.top_menu_vertical_mini_2 {
        header {
            .o_menu_systray_item:not(.o_edit_website_container) {	
                margin-left: 25px;
            }
        }
    }
    &.top_menu_vertical_mini {
        &.o_website_fullscreen, &.editor_has_snippets, &.editor_has_dummy_snippets {
            header {
                .new_systray{
                    transform: translate(0%, -100%) !important;
                    height: 0 !important;
                    display: none !important;
                }
            }
        }
        header {
            .o_user_menu .dropdown-toggle{
                flex-direction: row-reverse;
                color: var(--header-vertical-mini-text-color) !important;
                .user-info{
                    display: flex;
                    flex-direction: column-reverse; 
                    padding: 0 20px;
                    gap: 2px;
                    .oe_topbar_name{
                        display: flex;
                        flex-direction: column;
                        width: min-content;
                        text-align: left;
                        gap: 2px;
                        .user_name{
                            font-weight: 500;
                            font-size: 13px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        .database_name{
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            font-size: 12px;
                            max-width: 120px;
                        }
                    }
                    .greeting{
                        text-align: left;
                        width: fit-content;
                    }
                }
            }
            .debug_activator .activate_debug,.theme_selector .theme-edit,.header_to_do_list .to_do_list,.dark_mode .dark-light-mode-button-design label{
                min-width: 38px;
                width: auto;
                height: 38px;
                line-height: 38px;
                text-align: center;
                display: block;
                border: 0;
                box-shadow: 0 2px 4px 0 rgba(79, 131, 183, 0.1);
                cursor: pointer;
                position: relative;
                background-color: rgba(255, 255, 255,0.14) !important;
                color: var(--header-vertical-mini-text-color) !important;
                border-radius: 50%;
                padding: 0 10px;
            }
            .dark-light-mode-button-design label{
                cursor: pointer !important;
            }
            .debug_activator {
                .activate_debug {
                    &.toggle {
                        .ri::before{
                            content: "\eba6";
                        }
                    }
                }
            }
            .o_menu_systray_item, .o_website_switcher_container, .o_edit_website_container {
                background-color: transparent;
                margin-left: auto;

                .o_switch_danger_success{
                    display: block !important;
                    & + span{
                        display: none !important;
                    }
                }

                > a, > .dropdown-toggle{
                    min-width: 38px;
                    width: auto;
                    height: 38px;
                    line-height: 38px !important;
                    text-align: center;
                    display: block;
                    border: 0;
                    box-shadow: 0 2px 4px 0 rgba(79, 131, 183, 0.1);
                    border: radius 50%;
                    margin-right: 16px;
                    cursor: pointer;
                    position: relative;
                    background-color: rgba(255, 255, 255,0.14) !important;
                    color: var(--header-vertical-mini-text-color) !important;
                    padding: 0 10px;
                    &.rounded-0{
                        border-radius: 0 !important;
                    }
                }
                
            }
            #o_new_content_menu_choices{
                .o_new_content_element{
                    .fa{
                        font-size: inherit !important;
                    }
                }
            }
        }
    }
    &.top_menu_horizontal {
        &:not(.o_website_fullscreen){
            .o_ace_view_editor:not(.oe_form_field){
                top: var(--horizontal-menu-height) !important;
            }
        }
        header {
            .o_menu_systray_item, .o_website_edit_in_backend {
                > a {
                    display: flex !important;
                }
            }
            .o_edit_website_container{
                .btn{
                    border-radius: 0 !important;
                }
            }
        }
        .o_main_navbar{
            .dropdown-toggle{
                padding-left: 10px;
                padding-right: 10px;
                @media (max-width: 992px){
                    padding-left: 8px;
                    padding-right: 8px;
                    margin: 0 !important;
                }
            }
            button .dropdown-toggle{
                padding-left: 10px;
                padding-right: 10px;
                @media (max-width: 992px){
                    padding-left: 8px;
                    padding-right: 8px;
                    margin: 0 !important;
                    margin-top: 50px !important;
                    
                }
            }
        }
        &.o_website_fullscreen, &.editor_has_snippets, &.editor_has_dummy_snippets {
            header {
                .header_menu_right_content, .header_menu_right_content_toggler {
                    transform: translate(100%, -50%) !important;
                }
                .o_main_navbar {
                    height: 0;
                    transform: translate(0, -100%) !important;
                    display: none !important;
                }
                .fav_app_island {
                    bottom: 0;
                    transform: translate(-50%,100%);
                }
            }
            .o_action_manager {
                transition: 0.3s;
                padding: 0px 0px 0px 0px;
            }
        }
        &:not(.o_website_fullscreen) .o_ace_view_editor:not(.oe_form_field){
            top: var(--horizontal-menu-height);
        }
    }

    .o_we_website_top_actions {
        .btn-secondary {
            color: #FFFFFF !important;
            background-color: #141217 !important;
            border-color: #141217 !important;
        }
    }
    .oe_menu_editor{
        .input-group{
            .input-group-prepend{
                .input-group-text{
                    height: 100%;
                }
            }
        }
    }
    .oe_seo_configuration{
        .input-group{
            .btn-outline-primary{
                border-top-left-radius: 0 !important;
                border-bottom-left-radius: 0 !important;
            }
        }
        .o_seo_og_image{
            .o_meta_img_upload, .o_meta_img{
                &:hover{
                    border-color: var(--biz-theme-primary-color);
                    color: var(--biz-theme-primary-color);
                }
            }
            .o_meta_img.o_active_image{
                border-color: var(--biz-theme-primary-color);
            }
        }
    }
    #oe_snippets {
        .o_we_website_top_actions{
            .btn{
                padding: 0.375rem 0.75rem !important;
            }
        }
        input, textarea{
            color: inherit !important;
        }
        .btn {
            padding: 2.64px 4px !important;
            border-radius: 2px !important;
        }
        .dropdown-menu {
            background: #222222 !important;
            border-color: #dee2e6 !important;
            border-radius: 0.25rem !important;
            color: #495057 !important;

            .dropdown-item {
                color: #ffffff !important;
            }
        }
    }
    .oe-toolbar .dropdown-item.active, .oe-toolbar .dropdown-item:active{
        background: var(--biz-theme-primary-color);
    }

    .o_configurator_container{
        background-color: var(--biz-theme-body-color) !important;
        color: var(--biz-theme-body-text-color) !important;

        .o_configurator_screen{
            &.o_description_screen{
                .o_configurator_industry_dropdown{
                    background: var(--biz-theme-body-color) !important;
                }
                input {
                    border-bottom: 3px solid var(--biz-theme-primary-color) !important;
                    color: var(--biz-theme-primary-color) !important;
                }
                .dropdown {
                    box-shadow: inset 0 -3px 0 var(--biz-theme-primary-color);
                }
            }
            &.o_palette_selection_screen {
                .palette_card {
                    &:hover, &.selected {
                        box-shadow: 0 0 0 1px #FFF, 0 0 0 3px var(--biz-theme-primary-color);
                    }
                }
            }
            &.o_theme_selection_screen{
                .theme_preview {
                    &:hover {
                        border-color: var(--biz-theme-primary-color) !important;
                    }
                }
            }
        }
    }
}
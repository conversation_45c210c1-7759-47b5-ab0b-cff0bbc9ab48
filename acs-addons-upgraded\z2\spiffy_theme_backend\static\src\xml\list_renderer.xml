<!-- # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
# See LICENSE file for full copyright and licensing details. -->
<templates id="template" xml:space="preserve">
    <t t-inherit="web.ListRenderer.RecordRow" t-inherit-mode="extension" >
        <xpath expr="//tr[hasclass('o_data_row')]" position="attributes">
            <attribute name="t-att-resId">record.resId</attribute>
        </xpath>
    </t>

    <t t-inherit="web.ListRenderer" t-name="spiffy_split_view.ListRenderer" t-inherit-mode="extension">
        <xpath expr="//div[contains(@class, 'o_list_renderer')]" position="replace">
            <div class="spiffy_list_view">
                <div
                    class="o_list_renderer o_renderer table-responsive"
                    tabindex="-1"
                    t-ref="root"
                >
                    <t t-if="showNoContentHelper" t-call="web.ActionHelper">
                        <t t-set="noContentHelp" t-value="props.noContentHelp"/>
                    </t>
                    <table t-attf-class="o_list_table table table-sm table-hover position-relative mb-0 {{props.list.isGrouped ? 'o_list_table_grouped' : 'o_list_table_ungrouped table-striped'}}" t-ref="table">
                        <thead>
                            <tr>
                                <th t-if="hasSelectors" class="o_list_record_selector o_list_controller align-middle pe-1 cursor-pointer" tabindex="-1" t-on-keydown.synthetic="(ev) => this.onCellKeydown(ev)" t-on-click.stop="toggleSelection">
                                    <CheckBox disabled="!_canSelectRecord" value="selectAll" className="'d-flex m-0'" onChange.bind="toggleSelection"/>
                                </th>
                                <t t-foreach="columns" t-as="column" t-key="column.id">
                                    <th t-if="column.type === 'field'"
                                        t-att-data-name="column.name"
                                        t-att-class="getColumnClass(column) + ' opacity-trigger-hover w-print-auto'"
                                        t-on-pointerup="onColumnTitleMouseUp"
                                        t-on-click="() => this.onClickSortColumn(column)"
                                        t-on-keydown="(ev) => this.onCellKeydown(ev)"
                                        data-tooltip-delay="1000"
                                        tabindex="-1">
                                        <t t-if="column.hasLabel and column.widget !== 'handle'">
                                        <t t-set="isNumeric" t-value="isNumericColumn(column)"/>
                                            <div class="d-flex"
                                                t-att-data-tooltip-template="isDebugMode ? 'web.FieldTooltip' : 'web.ListHeaderTooltip'"
                                                t-att-data-tooltip-info="makeTooltip(column)">
                                                <span class="d-block min-w-0 text-truncate flex-grow-1 flex-shrink-1" t-att-class="isNumeric ? 'o_list_number_th' : ''"
                                                    t-esc="column.label"/>
                                                <div class="o_list_header_label_spacer"/>
                                                <i class="o_list_sortable_caret" t-att-class="getSortableIconClass(column)"/>
                                            </div>
                                            <span
                                                class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-1"
                                                t-on-pointerdown.stop.prevent="this.columnWidths.onStartResize"/>
                                        </t>
                                    </th>
                                    <th t-else="" t-on-keydown="(ev) => this.onCellKeydown(ev)" t-att-class="{'o_list_button w-print-0 p-print-0': column.type === 'button_group'}"/>
                                </t>
                                <th t-if="hasOpenFormViewColumn" t-on-keydown="(ev) => this.onCellKeydown(ev)" class="o_list_open_form_view w-print-0 p-print-0"/>
                                <th t-if="hasActionsColumn" t-on-keydown="(ev) => this.onCellKeydown(ev)" class="o_list_controller o_list_actions_header w-print-0 p-print-0 position-sticky end-0">
                                    <div t-if="displayOptionalFields or hasOptionalOpenFormViewColumn" class="o_optional_columns_dropdown d-print-none text-center border-top-0">
                                        <Dropdown position="'bottom-end'">
                                            <button class="btn p-0" tabindex="-1">
                                                <i class="o_optional_columns_dropdown_toggle oi oi-fw oi-settings-adjust"/>
                                            </button>
                                            <t t-set-slot="content">
                                                <t t-foreach="optionalFieldGroups" t-as="group" t-key="group_index">
                                                    <div t-if="!group_first" role="separator" class="dropdown-divider"/>
                                                    <DropdownItem t-if="group.displayName" closingMode="'none'" onSelected="() => this.toggleOptionalFieldGroup(group.id)">
                                                        <div class="fw-bold" t-esc="group.displayName"/>
                                                    </DropdownItem>
                                                    <t t-foreach="group.optionalFields" t-as="field" t-key="field_index">
                                                        <DropdownItem closingMode="'none'" onSelected="() => this.toggleOptionalField(field.name)">
                                                            <CheckBox
                                                                onChange="() => this.toggleOptionalField(field.name)"
                                                                value="field.value"
                                                                name="field.name"
                                                            >
                                                                <span class="d-flex align-items-center"><span class="text-truncate" t-esc="field.label"/><span class="ps-1" t-if="env.debug" t-esc="' (' + field.name + ')'" /></span>
                                                            </CheckBox>
                                                        </DropdownItem>
                                                    </t>
                                                </t>
                                                <div t-if="hasOptionalOpenFormViewColumn" role="separator" class="dropdown-divider"/>
                                                <DropdownItem t-if="hasOptionalOpenFormViewColumn" closingMode="'none'" onSelected="() => this.toggleDebugOpenView()">
                                                    <CheckBox
                                                        onChange="() => this.toggleDebugOpenView()"
                                                        value="this.debugOpenView"
                                                        name="'View Button'"
                                                    >
                                                        <span class="d-flex align-items-center"><span class="text-truncate">View Button</span></span>
                                                    </CheckBox>
                                                </DropdownItem>
                                            </t>

                                        </Dropdown>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="ui-sortable">
                            <t t-call="{{ constructor.rowsTemplate }}">
                                <t t-set="list" t-value="props.list"/>
                            </t>
                        </tbody>
                        <tfoot t-on-click="() => props.list.leaveEditMode()" class="o_list_footer cursor-default" t-att-class="{o_sample_data_disabled: props.list.model.useSampleModel}">
                            <tr>
                                <td t-if="hasSelectors"/>
                                <t t-foreach="columns" t-as="column" t-key="column.id">
                                    <t t-set="aggregate" t-value="aggregates[column.name]"/>
                                    <td t-if="aggregate" class="o_list_number" >
                                        <span t-esc="aggregate.value" t-att-data-tooltip="aggregate.help"/>
                                    </td>
                                    <td t-else=""/>
                                </t>
                                <td t-if="hasOpenFormViewColumn" class="w-print-0 p-print-0"/>
                                <td t-if="displayOptionalFields or activeActions.onDelete" />
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <SplitviewContainer t-if="SplitViewForm.show" t-props="getSplitviewContainerProps()"/>
        </xpath>
    </t>
</templates>
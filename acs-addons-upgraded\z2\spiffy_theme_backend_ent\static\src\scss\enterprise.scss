// Import Enterprise variables
@import 'enterprise/variables';

// Enterprise Base Styles
.o_web_client {
    font-family: $o-enterprise-font-family;
    font-size: $o-enterprise-font-size-base;
    background-color: $o-enterprise-color-bg;
    color: $o-enterprise-color-text;

    // Enterprise Menu Styles
    .o_main_navbar {
        height: $o-enterprise-menu-height;
        background-color: $o-enterprise-color-main;
        border-bottom: none;
        box-shadow: $o-enterprise-box-shadow;

        .o_menu_brand {
            font-size: 1.2em;
            font-weight: 500;
            color: $o-enterprise-color-text;
        }

        .o_menu_sections {
            > li {
                > a {
                    padding: $o-enterprise-menu-padding;
                    color: $o-enterprise-color-text;
                    
                    &:hover, &:focus {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                }
            }
        }
    }

    // Enterprise Sidebar Styles
    .o_enterprise_sidebar {
        width: $o-enterprise-sidebar-width;
        background-color: $o-enterprise-sidebar-bg;
        color: $o-enterprise-sidebar-text;
        transition: width $o-enterprise-transition-time $o-enterprise-transition-ease;

        &.o_sidebar_collapsed {
            width: $o-enterprise-sidebar-min-width;
        }

        .o_sidebar_item {
            padding: $o-enterprise-menu-padding;
            cursor: pointer;
            transition: background-color $o-enterprise-transition-time $o-enterprise-transition-ease;

            &:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }

            i {
                font-size: $o-enterprise-menu-icon-size;
                margin-right: 8px;
            }
        }
    }

    // Enterprise Control Panel Styles
    .o_control_panel {
        height: $o-enterprise-control-panel-height;
        background-color: $o-enterprise-control-panel-bg;
        color: $o-enterprise-control-panel-text;
        padding: 8px 16px;
        border-bottom: none;
        box-shadow: $o-enterprise-box-shadow;

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;

            .breadcrumb-item {
                color: $o-enterprise-control-panel-text;

                &.active {
                    font-weight: 500;
                }
            }
        }

        .o_cp_buttons {
            .btn {
                margin-right: 8px;
                border-radius: $o-enterprise-border-radius;
            }
        }
    }

    // Enterprise Form View Styles
    .o_form_view {
        .o_form_sheet {
            max-width: 1200px;
            margin: 16px auto;
            padding: 24px;
            background-color: white;
            border-radius: $o-enterprise-border-radius;
            box-shadow: $o-enterprise-box-shadow;

            .o_form_label {
                font-weight: 500;
                margin-bottom: 4px;
            }

            .o_input {
                height: $o-enterprise-form-input-height;
                border: $o-enterprise-form-input-border;
                border-radius: $o-enterprise-border-radius;
                padding: 8px 12px;
            }
        }
    }

    // Enterprise List View Styles
    .o_list_view {
        .o_list_table {
            thead {
                background-color: $o-enterprise-list-header-bg;
                color: $o-enterprise-list-header-text;

                th {
                    height: $o-enterprise-list-row-height;
                    padding: 8px 12px;
                    border-bottom: none;
                }
            }

            tbody {
                tr {
                    height: $o-enterprise-list-row-height;

                    td {
                        padding: 8px 12px;
                        vertical-align: middle;
                    }

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.02);
                    }
                }
            }
        }
    }

    // Enterprise Kanban View Styles
    .o_kanban_view {
        background-color: $o-enterprise-kanban-bg;
        padding: 16px;

        .o_kanban_record {
            margin: $o-enterprise-kanban-card-margin;
            border-radius: $o-enterprise-border-radius;
            box-shadow: $o-enterprise-box-shadow;
            background-color: white;
            
            &:hover {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }
        }

        .o_kanban_group {
            background-color: rgba(0, 0, 0, 0.03);
            border-radius: $o-enterprise-border-radius;
            padding: 8px;
            margin: 0 4px;
            min-width: $o-enterprise-kanban-column-width;
        }
    }

    // Enterprise Studio Integration Styles
    &.o_studio_enabled {
        .o_studio_navbar {
            background-color: $o-enterprise-studio-bg;
            color: $o-enterprise-studio-text;
            border-bottom: $o-enterprise-studio-border;
        }

        .o_studio_sidebar {
            background-color: $o-enterprise-studio-bg;
            color: $o-enterprise-studio-text;
            border-left: $o-enterprise-studio-border;
        }
    }
} 
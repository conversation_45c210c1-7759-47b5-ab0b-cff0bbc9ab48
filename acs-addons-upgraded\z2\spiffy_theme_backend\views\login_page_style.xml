<?xml version="1.0" encoding="UTF-8"?>
<!-- # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
# See LICENSE file for full copyright and licensing details. -->
<odoo>
    <template id="spiffy_login_values_template" name="Spiffy Login Values">
        <t t-set="company" t-value="request.env.company.sudo()"></t>
        <t t-set="backend_theme_data" t-value="company.get_login_page_data()"/>

        <t t-set="use_custom_colors" t-value="backend_theme_data['config_vals'].use_custom_colors"/>
        <t t-set="light_primary_bg_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_bg_color"/>
        <t t-set="light_primary_text_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_text_color"/>
        <t t-set="login_page_text_color_3_4_style" t-value="company.login_page_text_color if company.login_page_style == 'login_style_3' or company.login_page_style == 'login_style_4' else ''"/>
        <t t-set="login_page_text_color_not_2_style" t-value="company.login_page_text_color if company.login_page_style != 'login_style_2' else ''"/>
    </template>

    <template id="web_login_layout_inherit" name="Web Login Layout Inherit" inherit_id="web.login_layout">
        <xpath expr="//t[@t-set='html_data']" position="before">
            <t t-set="company" t-value="request.env.company.sudo()"></t>
            <t t-set="backend_theme_data" t-value="company.get_login_page_data()"/>

            <t t-if="company.show_bg_image">
                <div class="login-page-background" t-attf-style="background-color: #{company.login_page_background_color}; background-image: url(/web/image/res.company/#{company.id}/login_page_background_img);"></div>
            </t>
            <t t-else="">
                <div class="login-page-background" t-attf-style="background-color: #{company.login_page_background_color};"></div>
            </t>
        </xpath>
       
        <xpath expr="//t[@t-set='body_classname']" position="attributes">
            <attribute name="t-value">'bg-100 backend-login-page background-image'</attribute>
        </xpath>
        
        <xpath expr="//div[hasclass('card-body')]" position="attributes">
            <attribute name="t-attf-class">card-body #{company.login_page_style} #{backend_theme_data['config_vals'].color_pallet if not backend_theme_data['config_vals'].use_custom_colors else ''}</attribute>
        </xpath>

        <!-- <xpath expr="//div[hasclass('card-body')]//div[hasclass('text-center pb-3 border-bottom mb-4')]/img" position="attributes">
            <attribute name="style"></attribute>
            <attribute name="class" add="img img-fluid w-50 mx-auto" separator=" "></attribute>
            <attribute name="t-attf-src">/web/image/res.company/#{company.id}/logo</attribute>
        </xpath> -->

        <xpath expr="//t[@t-if='not disable_database_manager']//a" position="attributes">
            <attribute name="t-attf-style">color: #{login_page_text_color_not_2_style};</attribute>
        </xpath>

        <xpath expr="//div[@t-if='not disable_footer']/a" position="attributes">
            <attribute name="t-attf-style">color: #{login_page_text_color_not_2_style};</attribute>
        </xpath>
    </template>

    <template id="web_login_inherit" name="Web Login Inherit" inherit_id="web.login">
        <xpath expr="//form" position="before">
            <t t-set="company" t-value="request.env.company.sudo()"></t>
            <t t-set="backend_theme_data" t-value="company.get_login_page_data()"/>

            <t t-set="use_custom_colors" t-value="backend_theme_data['config_vals'].use_custom_colors"/>
            <t t-set="light_primary_bg_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_bg_color"/>
            <t t-set="light_primary_text_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_text_color"/>
            <t t-set="login_page_text_color_3_4_style" t-value="company.login_page_text_color if company.login_page_style == 'login_style_3' or company.login_page_style == 'login_style_4' else ''"/>
            <t t-set="login_page_text_color_not_2_style" t-value="company.login_page_text_color if company.login_page_style != 'login_style_2' else ''"/>
        </xpath>

        <!-- LOGIN FORM BUTTONS CHANGES -->
        <xpath expr="//div[contains(@class, 'input-group')]//a" position="attributes">
            <attribute name="t-att-style">use_custom_colors and 'border-color:'+light_primary_bg_color+'; background-color:'+light_primary_bg_color+'; color:'+light_primary_text_color+';'</attribute>
        </xpath>

        <!-- FIELD-LOGIN INPUT CHANGES -->
        <!-- <xpath expr="//div[hasclass('field-login')]" position="attributes">
            <attribute name="class" remove="mb-3" add="form-group d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-login')]/input" position="before">
            <i class="login-icon ms-2 fa fa-envelope fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"/>
        </xpath> -->


        <!-- FIELD-PASSWORD INPUT CHANGES -->
        <!-- <xpath expr="//input[@type='password']/.." position="attributes">
            <attribute name="class" remove="mb-3" add="form-group field-password d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//label[@for='password']" position="after">
            <i class="login-icon ms-2 fa fa-lock fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"></i>
        </xpath> -->

        <!-- LOGIN FORM BUTTONS CHANGES -->
        <xpath expr="//div[contains(@class, 'oe_login_buttons')]" position="attributes">
            <attribute name="t-attf-style">color: #{login_page_text_color_3_4_style};</attribute>
        </xpath>
        <xpath expr="//div[contains(@class, 'oe_login_buttons')]//button[hasclass('btn-primary')]" position="attributes">
            <attribute name="t-att-style">use_custom_colors and 'background-color:'+light_primary_bg_color+'; color:'+light_primary_text_color+';'</attribute>
            <attribute name="class" separator=" " add="border-0"/>
        </xpath>
        <xpath expr="//t[@t-if='debug']//button[hasclass('btn-link')]" position="attributes">
            <attribute name="t-attf-style">color: #{login_page_text_color_3_4_style};</attribute>
        </xpath>
    </template>

    <template id="auth_signup_btn_inherit" name="Auth Signup Buttons Inherit" inherit_id="auth_signup.signup">
        <xpath expr="//form[hasclass('oe_signup_form')]" position="before">
            <t t-set="company" t-value="request.env.company.sudo()"></t>
            <t t-set="backend_theme_data" t-value="company.get_login_page_data()"/>

            <t t-set="use_custom_colors" t-value="backend_theme_data['config_vals'].use_custom_colors"/>
            <t t-set="light_primary_bg_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_bg_color"/>
            <t t-set="light_primary_text_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_text_color"/>
            <t t-set="login_page_text_color_3_4_style" t-value="company.login_page_text_color if company.login_page_style == 'login_style_3' or company.login_page_style == 'login_style_4' else ''"/>
            <t t-set="login_page_text_color_not_2_style" t-value="company.login_page_text_color if company.login_page_style != 'login_style_2' else ''"/>
        </xpath>
        <!-- SIGNUP FORM BUTTONS CHANGES -->
        <xpath expr="//div[hasclass('oe_login_buttons')]//button[hasclass('btn-primary')]" position="attributes">
            <attribute name="t-att-style">use_custom_colors and 'background-color:'+light_primary_bg_color+'; color:'+light_primary_text_color+';'</attribute>
            <attribute name="class" separator=" " add="border-0"/>
        </xpath>
        <xpath expr="//div[hasclass('oe_login_buttons')]//a[hasclass('btn-link')]" position="attributes">
            <attribute name="t-attf-style">color: #{login_page_text_color_3_4_style};</attribute>
        </xpath>
    </template>

    <template id="web_signup_inherit" name="Web Signup Inherit" inherit_id="auth_signup.fields">
        <xpath expr="//div[hasclass('field-login')]" position="before">
            <t t-set="company" t-value="request.env.company.sudo()"></t>
            <t t-set="backend_theme_data" t-value="company.get_login_page_data()"/>

            <t t-set="use_custom_colors" t-value="backend_theme_data['config_vals'].use_custom_colors"/>
            <t t-set="light_primary_bg_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_bg_color"/>
            <t t-set="light_primary_text_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_text_color"/>
            <t t-set="login_page_text_color_3_4_style" t-value="company.login_page_text_color if company.login_page_style == 'login_style_3' or company.login_page_style == 'login_style_4' else ''"/>
            <t t-set="login_page_text_color_not_2_style" t-value="company.login_page_text_color if company.login_page_style != 'login_style_2' else ''"/>
        </xpath>

        <!-- FIELD-LOGIN INPUT CHANGES -->
        <xpath expr="//div[hasclass('field-login')]" position="attributes">
            <attribute name="class" remove="mb-3" add="form-group d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-login')]/input" position="before">
            <i class="login-icon ms-2 fa fa-envelope fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"/>
        </xpath>
        <xpath expr="//div[hasclass('field-login')]/input" position="attributes">
            <attribute name="placeholder" add="Email" separator=" "></attribute>
        </xpath>

        <!-- FIELD-NAME INPUT CHANGES -->
        <xpath expr="//div[hasclass('field-name')]" position="attributes">
            <attribute name="class" remove="mb-3" add="form-group d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-name')]/input" position="before">
            <i class="login-icon ms-2 fa fa-user fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"/>
        </xpath>

        <!-- FIELD-PASSWORD INPUT CHANGES -->
        <xpath expr="//div[hasclass('field-password')]" position="attributes">
            <attribute name="class" remove="mb-3" add="form-group d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-password')]/input" position="before">
            <i class="login-icon ms-2 fa fa-lock fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"/>
        </xpath>
        <xpath expr="//div[hasclass('field-password')]/input" position="attributes">
            <attribute name="placeholder" add="Password" separator=" "></attribute>
        </xpath>

        <!-- FIELD-CONFIRM-PASSWORD INPUT CHANGES -->
        <xpath expr="//div[hasclass('field-confirm_password')]" position="attributes">
            <attribute name="class" remove="mb-3" add="form-group d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-confirm_password')]/input" position="before">
            <i class="login-icon ms-2 fa fa-unlock-alt fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"/>
        </xpath>
        <xpath expr="//div[hasclass('field-confirm_password')]/input" position="attributes">
            <attribute name="placeholder" add="Confirm Password" separator=" "></attribute>
        </xpath>
    </template>

    <template id="web_reset_inherit" name="Web Reset Inherit" inherit_id="auth_signup.reset_password">
        <xpath expr="//form[hasclass('oe_reset_password_form')]" position="before">
            <t t-set="company" t-value="request.env.company.sudo()"></t>
            <t t-set="backend_theme_data" t-value="company.get_login_page_data()"/>

            <t t-set="use_custom_colors" t-value="backend_theme_data['config_vals'].use_custom_colors"/>
            <t t-set="light_primary_bg_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_bg_color"/>
            <t t-set="light_primary_text_color" t-value="backend_theme_data['config_vals'].use_custom_colors and backend_theme_data['config_vals'].light_primary_text_color"/>
            <t t-set="login_page_text_color_3_4_style" t-value="company.login_page_text_color if company.login_page_style == 'login_style_3' or company.login_page_style == 'login_style_4' else ''"/>
            <t t-set="login_page_text_color_not_2_style" t-value="company.login_page_text_color if company.login_page_style != 'login_style_2' else ''"/>
        </xpath>

        <!-- FIELD-LOGIN INPUT CHANGES -->
        <xpath expr="//div[hasclass('field-login')]" position="attributes">
            <attribute name="class" remove="mb-3" add="form-group d-flex align-items-center" separator=" "></attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-login')]/input" position="before">
            <i class="login-icon ms-2 fa fa-envelope fa-lg" t-att-style="use_custom_colors and 'color:'+light_primary_bg_color+';'"/>
        </xpath>
        <xpath expr="//div[hasclass('field-login')]/input" position="attributes">
            <attribute name="placeholder" add="Email" separator=" "></attribute>
        </xpath>
      
        <!-- RESET FORM BUTTONS CHANGES -->
        <xpath expr="//div[hasclass('oe_login_buttons')]" position="attributes">
            <attribute name="class" separator=" " remove="mt-3"/>
        </xpath>
        <xpath expr="//div[hasclass('oe_login_buttons')]//button[hasclass('btn-primary')]" position="attributes">
            <attribute name="t-att-style">use_custom_colors and 'background-color:'+light_primary_bg_color+'; color:'+light_primary_text_color+';'</attribute>
            <attribute name="class" separator=" " add="border-0"/>
        </xpath>
    </template>
</odoo>
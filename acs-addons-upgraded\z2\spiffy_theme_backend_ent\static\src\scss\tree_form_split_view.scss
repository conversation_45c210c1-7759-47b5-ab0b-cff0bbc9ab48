// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
body.tree_form_split_view {
    .o_action_manager.tree_form_split > .o_view_controller.split-screen-tree-viewer > .o_content {
        display: flex;
        > .spiffy_list_view {
            flex-shrink: 0;
            position: sticky;
            top: 0;
            left: 0;
            overflow: auto;
            min-width: 35%;
            max-width: 55%;
            // height: fit-content;
            .o_list_actions_header{
                .o-dropdown--menu{
                    position: absolute !important;
                    top: 100% !important;
                    right: 0 !important;
                    left: auto !important;
                }
            }
        }

        > .formview-panel {
            position: static !important;
            min-width: 35%;
            flex-grow: 1;
            overflow: auto;
            padding-right: 10px;

            &.tree-form-viewer{
                .o_control_panel_main{
                    .o_control_panel_actions,.o_control_panel_navigation{
                        display: none !important;
                        margin: 0 !important;
                    }
                }
                .o_content{
                    margin-bottom: 1rem;
                    .o_form_sheet_bg{
                        .o_form_sheet{
                            min-width: 100%;
                            max-width: 100%;
                            .oe_button_box{
                                .oe_stat_button{
                                    width: 20%;
                                }
                            }
                        }
                    }
                    .o_group{
                        .o_group_col_6{
                            width: auto;
                        }
                    }
                    .o_form_statusbar {
                        .o_field_statusbar {
                            align-self: auto;
                        }
                        align-items: center;
                    }
                    .o_form_editable, .flex-nowrap {
                        flex-direction: column;
                    }
                }
                .o_form_view {
                    .o-mail-Form-chatter {
                        width: 100%;
                    }
                }
            }
        }
    }
    .o_list_view .o_field_translate{
        margin-left: 0 !important;
        width: unset !important;
    }
    .o_action_manager.tree_form_split {
        .table-hover tbody tr.o_data_row{
            &.side-selected {
                background-color: #d1ecf1 !important;
                color: #1b1b1b !important;
            }
        }
    }

    .close_form_view {
        position: absolute;
        top: 0;
        right: 10px;
        text-align: center;
        cursor: pointer;
        border-radius: var(--border-radius-md);
        width: 35px;
        height: 35px;
        line-height: 35px;
        background-color: var(--biz-theme-primary-color);
        color: var(--biz-theme-primary-text-color);
        z-index: 1;
    }
}

.split_view_separator {
    cursor: col-resize;
    width: 6px;
    margin: 0 10px;
    background: #b8b8b847 url(/spiffy_theme_backend/static/src/image/pallet_1.png) no-repeat center !important;
    background-size: 9px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: stretch;
    flex-shrink: 0;
    height: auto;
    position: sticky;
    top: 0;
    opacity: 1;
}
.top_menu_horizontal, .top_menu_vertical_mini {
    .o_list_view {
        .spiffy_list_view {
            padding-bottom: 80px;
        }
    }
}
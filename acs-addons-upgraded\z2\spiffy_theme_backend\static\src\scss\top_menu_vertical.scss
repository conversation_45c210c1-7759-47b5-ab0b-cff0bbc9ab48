// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
.top_menu_vertical, .top_menu_vertical_mini_2 {
    position: relative; 
    .menu-link-for-apps {
        position: relative;
        .app-image {
            display: none;
        }
    }
    .menu-link-for-apps {
        display: flex;
        align-items: center;
        justify-content: space-between;
        img {
            height: 24px;
            object-fit: cover;
            margin-right: 10px;
        }
        .group_menu_icon {
            display: none;
        }
        .group-count {
            color: var(--biz-theme-primary-color);
            background-color: var(--biz-theme-secondary-color);
            font-size: 10px;
            font-weight: bold;
            border-radius: 10px;
            position: absolute;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: -20px;
        }
    }

    .menu-name {
        color: var(--biz-theme-primary-text-color);
    }

    .submenu-link {
        display: flex;
        align-items: center;
        position: relative;
        border-left: 2px solid transparent;
        border-radius: var(--border-radius-lg) !important;
        padding: 0.75rem 1rem !important;

        &.active::before, &.open::before {
            background-color: var(--biz-theme-primary-color);
        }

        span {
            color: white !important;
        }

        .dropdown_icon {
            margin-left: auto;
            padding-right: 10px;
        }
    }

    .submenu-group {
        padding-left: 20px;
        position: relative; 
        .header-sub-menus-group {
            margin-left: 10px;
            position: relative;
            list-style: none;
            padding: 0;

            .nav-item {
                position: relative;
                padding-left: 30px;
            }

            .nav-item {
                position: relative;
                padding-left: 5px;
            
                // &::before {
                //     content: '';
                //     position: absolute;
                //     left: 0;
                //     top: 15px;
                //     transform: translateY(-50%);
                //     width: 4px;
                //     height: 4px;
                //     opacity: 0.5;
                //     background-color: #D9D9D9;
                //     border-radius: 50%;
                // }
            
                // &:hover{
                //     &::before {
                //         background-color: #FFFFFF;
                //         opacity: 1;
                //     }
                // }
            }
        }
    }

    .break-line {
        border: none;
        border-top: 1px solid;
        margin-top: 5px;
        margin-bottom: 5px;
    }
}

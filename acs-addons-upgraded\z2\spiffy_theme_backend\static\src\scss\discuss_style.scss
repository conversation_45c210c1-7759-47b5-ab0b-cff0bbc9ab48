// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
body.o_web_client {
    .o-mail-ChatHub-bubbles {
        margin-right: 60px !important;
    }
    .o-mail-DiscussSidebar{
        .btn:not([class*="btn-"]){
            padding: 6px !important;
        }
    }
    .o-mail-Discuss-content {
        .o-mail-Thread {
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
        }
    }
    .o-mail-Composer-actions{
        margin-right: 10px;
    }
    .o-mail-Composer-inputStyle{
        margin-top: 2px;
        margin-bottom: 2px;  
    }
    .o-mail-discussSidebarBgColor {
        background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
    }
    .o-mail-ChatWindow-header{
        .border{
            border: none !important;
        }
    }
    .o-mail-MessagingMenu{
        .bg-light{
            background-color: var(--biz-theme-secondary-color) !important;
        }
    }
    .o-mail-MessagingMenu-tab{
        transition: 0.2s;
        &:not(.o-active){
            color: var(--biz-theme-primary-color);
        }
        &.o-active, &:hover{
            background-color: var(--biz-theme-body-color);
            // color: var(--biz-theme-primary-color);
        }
    }
    .o-mail-Discuss{
        .o-mail-MessagingMenu-navbar{
            > .btn {
                border-radius: 0 !important;
            }
        }
        .btn-group{
            .btn-secondary{
                &:active,&.active{
                    background-color: var(--biz-theme-primary-color) !important;
                    color: var(--biz-theme-primary-text-color) !important;
                    border-color: var(--biz-theme-primary-color) !important;
                }
            }
        }
    }
    .o-mail-Composer{
        .o-mail-Composer-coreMain{
            .o-mail-Composer-actions{
                .btn{
                    font-size: 18px !important;
                    color: var(--biz-theme-primary-color);
                }
            }
            .o-mail-Composer-send{
                background-color: var(--biz-theme-primary-color) !important;
                color: var(--biz-theme-primary-text-color) !important;
                border-radius: 0 !important;
            }
        }
    }
        
    .o_MobileMessagingNavbar{
        background-color: var(--biz-theme-body-color) !important;
    }
    .o_ThreadViewTopbar{
        background-color: var(--biz-theme-body-color) !important;
    }
    .o_MessageActionList{
        background-color: var(--biz-theme-body-color) !important;
        color: var(--biz-theme-body-text-color) !important;

        .o_MessageActionView{
            &:hover{
                background-color: var(--biz-theme-secondary-color) !important;
                color: var(--biz-theme-secondary-text-color) !important;
            }
        }
    }
    .o-mail-Discuss {
        .o_ThreadPreview{
            background-color: var(--biz-theme-secondary-color) !important;
        }

        .o_Discuss{
            &.o-mobile{
                background-color: var(--biz-theme-body-color) !important;
                border-color: var(--biz-theme-secondary-color) !important;

                .o_DiscussMobileMailboxSelection{
                    padding: 0 15px;
                    gap: 5px;
                    .o_DiscussMobileMailboxSelection_button{
                        box-shadow: none !important;
                    }
                }
            }
        }
       
        .o_Discuss_content,
        .o_MessageList_empty,
        .o_Message,
        .o_MessageList,
        .o_MessageList_separatorLabel, .o_MessageActionList {
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
            .o_ThreadViewTopbar_threadName, .o_ThreadViewTopbar_markAllReadButton{
                border: none !important;
            }
            .o_ThreadViewTopbar{
                color: var(--biz-theme-body-text-color) !important;
            }
            .o_MessageActionView:hover{
                background-color: var(--biz-theme-secondary-color) !important;
            }
        }

        .o_DiscussSidebar {
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
            .o_DiscussSidebarCategory_title, .o_DiscussSidebarCategory_command, .o_DiscussSidebarCategoryItem_command {
                border: none;
            }
        }

        .o_DiscussSidebarItem:hover {
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
        }

        .o_DiscussSidebarItem_activeIndicator.o-item-active {
            background-color: var(--biz-theme-primary-color) !important;
        }

        .o_Composer {
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;

            .o_Composer_coreMain {

                .o_ComposerTextInput {
                    background-color: var(--biz-theme-secondary-color) !important;
                    border-color: #414141 !important;
                    textarea {
                        background-color: var(--biz-theme-secondary-color) !important;
                        color: var(--biz-theme-secondary-text-color) !important;
                    }
                }

                .o_Composer_buttons {
                    .o_Composer_button:not(.btn-primary){
                        background-color: transparent !important;
                        color: var(--biz-theme-secondary-text-color) !important;
                        box-shadow: none !important;
                        outline: none !important;
                    }
                    .o_Composer_toolButtons {
                        background-color: var(--biz-theme-secondary-color) !important;
                        color: var(--biz-theme-secondary-text-color) !important;
                    }

                    .o_Composer_actionButtons {
                        .btn {
                            border-bottom-left-radius: 0 !important;
                            border-top-left-radius: 0 !important;
                        }
                    }
                }
            }
        }
        .o_ThreadViewTopbar_threadName.o-threadNameEditable, .o_ThreadViewTopbar_threadDescription.o-threadDescriptionEditable {
            background-color: var(--biz-theme-secondary-color) !important;
        }
        .o_ThreadViewTopbar_threadDescription {
            border-color: #414141 !important;
        }
        .o-mail-DiscussSidebarChannel .o_DiscussSidebarCategoryItem_item .o-mail-DiscussSidebarChannel-commands {
            padding: 0 !important;
        } 
    }
    .o-EmojiPicker{
        .o-EmojiPicker-search{
            --o-emoji-picker-active: var(--biz-theme-secondary-color) !important;
            color: var(--biz-theme-secondary-text-color) !important;
            border-width: 1px !important;
            border-style: solid !important;
        }
    }
    &.dark_mode{

        .o-mail-Message.o-card{
            color: var(--biz-theme-body-text-color) !important;
            outline: 1px solid #414141;
            background-color: var(--biz-theme-body-color) !important;


        }
        .o-mail-Message {
            .text-muted{
                color:#ffffff !important;
            }
            .o-mail-Message-body{
                    color:#ffffff !important;
            }
            a {
               color: var(--biz-theme-primary-color) !important
            }
        }
        .o-mail-DiscussSidebar-item:hover .o-mail-DiscussSidebarChannel-commands .btn-group .btn:hover {
            background-color: transparent !important;
        }
        .o-mail-Composer-bg{
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
        }
        // .o-mail-Message-body{
        //     color: #1b1b1b !important;
        // }
        .o-mail-Message-body.rounded-end-3.rounded-bottom-3 {
            color: #1b1b1b !important;
        }
        .o-mail-Discuss-core {
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
        }
       
        .o-mail-Discuss-header{
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;
            .btn{
                color: var(--biz-theme-body-text-color);

                &:hover{
                    color: #1b1b1b;
                }
            }
            .text-dark{
                color: white !important;
            }
        }
        .o_notification_content{
            color:black !important;
        }
        .o_notification_close.btn-close{
            color:black !important;
            filter: contrast(0.5);
        }
        .o-mail-Discuss-headerActionsGroup {
            background-color: transparent !important;
        }
        .o-mail-DiscussSidebar-item.o-active, .o-mail-DiscussSidebarChannel.o-active, .o-mail-DiscussSidebar-item:hover, .o-mail-DiscussSidebarChannel:hover, .o_ChannelInvitationForm_selectablePartner:hover{
            background-color: var(--biz-theme-body-color) !important;
            border: 1px solid #6a6a6a !important;
        }
        .o-mail-DiscussSidebar-item, 
        
        .o-mail-DiscussSidebarChannel{
            background-color: var(--biz-theme-secondary-color) !important;
            color: var(--biz-theme-secondary-text-color) !important;
        }
        .o-mail-DiscussSidebarChannel-commands{
            .btn{
                color: #ffffff;
            }
        }
    }

    
}
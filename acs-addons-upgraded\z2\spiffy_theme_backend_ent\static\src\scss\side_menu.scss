// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
body.o_web_client {
    .o_command_palette {
        top: 0;
        position: relative;
    }
    // common css for both vertical and horizontal start
    &.custom_drawer_color{
        .o_main_navbar {
            &.appdrawer-toggle{
                color: var(--app-drawer-custom-text-color) !important;
                .o_menu_systray{
                    .o_user_menu {
                        .o_user_menu {
                            .user-info {
                                span{
                                    color: var(--app-drawer-custom-text-color) !important;
                                }
                            }
                        }
                    }
                    a:not(.dropdown-item), button, .dark_mode label{
                        color: var(--app-drawer-custom-text-color) !important;
                    }
                }
            }
        }
    }
    &:not([headerMode='visible']){
        .o_main_navbar {
            display: none !important;
        }
    }

    .o_main_navbar {
        .dropdown-menu{
            max-height: 65vh;
            transition: 0s !important;
        }
        @include media-breakpoint-down(md){
            .o_menu_sections{
                display: block !important;
            }
        }
        .o_menu_systray{
            .o_mobile_menu_toggle{
                color: var(--biz-theme-primary-text-color);
            }
            .o_mail_systray_item, .o-mail-DiscussSystray-class, .dropdown-toggle {
                .o_notification_counter, .o-mail-MessagingMenu-counter, .o-mail-ActivityMenu-counter {
                    margin-top: 0 !important;
                    background: red !important;
                    color: white !important;
                    position: absolute !important;
                    transform: unset !important;
                    margin-left: 0 !important;
                    top: 0;
                    right: 0;
                    height: 0.875rem;
                    min-width: fit-content;
                    font-size: 0.7rem;
                    line-height: 0.6rem;
                }
            }
            .o_user_menu {
                .o_user_menu {
                    align-items: center;
                    margin: auto;
                    border-radius: var(--border-radius-md) !important;
                    transition: 0.3s;
                    width: fit-content;
                    padding: 10px;
                    margin-bottom: 10px;

                    .oe_topbar_avatar {
                        width: 50px !important;
                        height: 50px !important;
                        margin-bottom: 5px;
                        order: initial !important;
                        border-radius: var(--border-radius-xl);
                        margin: auto;
                        display: block;
                    }
                    .dropdown-toggle{
                        border: none !important;
                        &::after{
                            display: none !important;
                        }
                    }
                    .user-info {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        line-height: 1.5;
                        // margin-bottom: 15px;
    
                        span {
                            color: var(--biz-theme-primary-text-color) !important;
                        }
    
                        .greeting {
                            font-size: 10px;
                        }
    
                    }
    
                    .dropdown-item {
                        transition: 0.2s;
    
                        &:hover {
                            color: var(--biz-theme-primary-color) !important;
                            background-color: transparent !important;
                        }
                    }
    
                    .dropdown-menu.show {
                        max-height: unset !important;
                        min-width: auto;
                        overflow: auto;
                        width: 18rem;
                        padding: 12px 20px 15px 15px;
                        border-radius: var(--border-radius-md);
                        // transform: translate(-35px, -10px);
                        top: auto;
                        border: 0 !important;
                    }
                }
            }
            .o_switch_company_menu {
                display: flex !important;
                justify-content: center !important;
                margin-bottom: 10px;
                .o_user_lang {
                    .dropdown-toggle {
                        background-color: transparent !important;
                        &::after {
                            display: none !important;
                        }
                    }
                    .lang_selector {
                        cursor: pointer;
                        margin-right: 1rem !important;
                    }
                    .dropdown-menu.show {
                        max-height: unset !important;
                        min-width: max-content;
                        overflow: auto;
                        padding: 0px;
                        border-radius: var(--border-radius-md);
                        border: 0 !important;
                        right: 0 !important;

                        .active_lang{
                            padding-top: 5px;
                            padding-bottom: 5px;
                            
                            .dispalay_lang{
                                padding: 5px 15px;
                                &:hover{
                                    filter: brightness(0.8);
                                }
                                .lang-name{
                                    font-size: 14px;
                                }
                            }
                        }
                    }
                    .dropdown-menu {
                        color: #7c7c7c !important;
                        cursor: pointer;
                    }
                    .lang-name {
                        margin-bottom: 2px !important;
    
                        &.active {
                            color: var(--biz-theme-primary-color) !important;
                        }
                    }
                }
                .company_selections {
                    .company_label.text-900{
                        color: var(--biz-theme-secondary-text-color) !important;
                    }
                    .dropdown-toggle {
                        background-color: transparent !important;
                    }
                    .current_company {
                        padding: 10px;
                        color: var(--biz-theme-primary-color) !important;
                    }
                    .dropdown-menu.show {
                        max-height: 190px !important;
                        min-width: auto;
                        overflow: auto;
                        border-radius: var(--border-radius-md);
                        border: 0 !important;
                        .text-primary {
                            color: var(--biz-theme-primary-color) !important;
                        }
                        .border-primary {
                            border-color: var(--biz-theme-primary-color) !important;
                        }
                    }
                    .dropdown-item {
                        padding: 0 !important;
                        background-color: transparent !important;
                        .o_py:hover {
                            background-color: transparent !important;
                        }
                        div {
                            padding-left: unset !important;
                            padding-right: 0.5rem !important;
                        }
                        span {
                            margin-right: unset !important;
                            margin-left: 0.5rem !important;
                        }
                        &:hover {
                            color: var(--biz-theme-primary-color) !important;
        
                        }
                        &.o_py:hover {
                            background-color: transparent !important;
                        }
                    }
                    .log_into {
                        background-color: transparent !important;
                    }
                }
                .debug_activator {
                    margin-left: 1rem !important;
                    line-height: 19px;
                    cursor: pointer;
                }
                a {
                    color: var(--biz-theme-primary-text-color) !important;
                    padding: 0;
                    line-height: unset;
                    height: auto;
                    cursor: pointer;
                }
            }
            .o_mail_systray_dropdown {
                .o_mail_preview {
                    background-color: transparent !important;
                    transition: 0.3s;

                    .o_mail_preview_app{
                        > img{
                            border-radius: var(--border-radius-md);
                        }
                    }
    
                    .o_preview_title {
                        &:hover {
                            color: var(--biz-theme-primary-color);
                        }
                    }
                }
            }
            .o_NotificationList {
                .o_NotificationGroup_date {
                    color: var(--biz-theme-primary-color);
                }
                &>div {
                    background-color: transparent !important;
                    transition: 0.3s;
    
                    &:hover {
                        color: var(--biz-theme-primary-color);
                    }
                }
            }
            .o_mail_systray_dropdown, .o_MessagingMenu_dropdownMenu, .o_debug_manager > .dropdown-menu {
                &.dropdown-menu-right {
                    max-height: unset !important;
                    min-width: auto;
                    overflow: auto;
                    padding: 10px !important;
                    border-radius: var(--border-radius-md);
                    right: unset !important;
                    top: 30px;
                    border: 0 !important;
                }
                &.show .dropdown-toggle {
                    background-color: transparent !important;
                }
            }
            .o-mail-DiscussSystray-class.show {
                background-color: transparent !important;
                .dropdown-toggle {
                    background: transparent;
                }
                .o-mail-MessagingMenu-header {
                    border: 0 !important;
                    .o_MessagingMenu_tabButton{
                        &:not(:last-child) {
                            margin-right: 10px;
                        }
                    }
                }
            }
            .o_debug_manager > .dropdown-menu {
                .dropdown-item {
                    transition: 0.2s;
                    &:hover {
                        background-color: transparent !important;
                        color: var(--biz-theme-primary-color) !important;
                    }
                }
            }
            .dark-light-mode-button-design {
                .label {
                    cursor: pointer;
                    color: var(--biz-theme-primary-text-color);
                }
            }
            .pin_sidebar {
                > * {
                    pointer-events: none;
                }
                .ri {
                    font-size: 18px;
                }
                &.pinned {
                    .ri {   
                        &:before {
                            content: "\eece";
                        }
                    }
                }
            }
            .debug_activator {
                .activate_debug {
                    &.toggle {
                        .ri::before{
                            content: "\eba6";
                        }
                    }
                }
            }
            display: flex;
        }
    }
    &.top_menu_vertical_mini{
        .o_main_navbar{
            .o_app_drawer,.o_company_logo,.o_menu_systray{
                display: none !important;
                margin: 0 !important;
            }
        }
        nav.o_main_navbar{
            border-right: solid 1px #c4dbeb;
            border-bottom: none;
            .o_navbar_apps_menu{
                background-color: var(--header-vertical-mini-bg-color);
                &::after {
                    content: "";
                    position: fixed;
                    top: 70px;
                    left: 0;
                    width: var(--header-vertical-mini-menu-width);
                    height: 100%;
                    z-index: -1;
                    border-top-left-radius: 0 !important;
                    border-bottom-left-radius: 0 !important;
                    border-top-right-radius: var(--border-radius-lg) !important;
                    border-bottom-right-radius: var(--border-radius-lg) !important;
                }

                #accordion{
                    .header-sub-menus{
                        position: absolute;
                        height: calc(100vh - 70px);
                        overflow: auto;
                        background-color: #fff;
                        color: #1b1b1b;
                        top: 0;
                        width: 260px;
                        box-shadow: 0 13px 30px 0 rgba(0, 0, 0, 0.19);
                        clip-path: inset(-20px -30px -20px 0px);
                        left: 0;
                        padding: 2.25rem;
                        z-index: -1;
                        opacity: 0;
                        transition: left 0.5s !important;
                        border-top-left-radius: 0 !important;
                        border-bottom-left-radius: 0 !important;
                        border-top-right-radius: var(--border-radius-lg) !important;
                        border-bottom-right-radius: var(--border-radius-lg) !important;
                        border: solid 1px #c4dbeb;
                        border-left: none;
                        display: flex !important;
                        flex-direction: column;
                        &.show {
                            left: 100%;
                            opacity: 1;
                        }
                        p.bg-muted{
                            font-size: 13px !important;
                            padding: 6px 16px !important;
                            background-color:var(--header-vertical-mini-bg-color) !important;
                            color:var(--header-vertical-mini-text-color) !important;
                            a{
                                color:var(--header-vertical-mini-text-color) !important;
                                *{
                                    color:var(--header-vertical-mini-text-color) !important;
                                }
                            }
                            > a:not(.collapsed) {
                                .ri::before {
                                    content: "\ea4e" !important;
                                }
                            }
                        }
                        // .collapse.show{
                        //     padding-left: 8px;
                        // }
                        *{
                            color: #1b1b1b;
                        }

                        .submenu_active{
                            color: var(--biz-theme-primary-color) !important;
                        }

                        // &.show {
                        //     left: var(--header-vertical-mini-menu-width) !important;
                        //     opacity: 1 !important;
                        // }
                    }
                }
            }
        }
        .new_systray {
            display: flex !important;
            align-items: center;
            background-repeat: no-repeat;
            border-bottom: solid 1px #c4dbeb !important;
            padding: 15px 22px;
            height: 70px;
            background-color: var(--header-vertical-mini-bg-color) !important;
            .company_logo_brand{
                gap: 0.7rem;
                .o_brand_name{
                    text-align: center;
                    font-size: 18px;
                    padding-left: 0.7rem;
                    border-left: 1px solid;
                    border-color: var(--header-vertical-mini-text-color) !important;;
    
                    .o_menu_brand{
                        padding: 0;
                        color: var(--header-vertical-mini-text-color) !important;
                        font-weight: 800;
                        background-color: transparent;
                        cursor: pointer;
                    }
                }
            }
    
            .o_company_logo{
                img{
                    max-width: 140px;
                    max-height: 70px;
                }
            }
            .o_menu_systray{
                display: flex;
                align-items: center;
                margin-left: auto;
                .o-dropdown--no-caret:not(.o-mail-DiscussSystray-class):not(.o_website_switcher_container) button.dropdown-toggle,button.o_nav_entry:not([aria-label="Toggle Studio"]){
                    background: transparent !important;
                    border: transparent !important;
                    margin: 0 4px !important;
                    color: var(--header-vertical-mini-text-color) !important;
                    box-shadow: none !important;
                }
                .bg-black-15{
                    background-color: transparent !important;
                }
                .o_user_menu{
                    order: 1;
                }
                .o_website_switcher_container{
                    .dropdown-toggle{
                        display: block;
                        text-align: center;
                        border: 0;
                        box-shadow: 0 2px 4px 0 rgba(79, 131, 183, 0.1);
                        border-radius: 0;
                        cursor: pointer;
                        position: relative;
                        background-color: rgba(255, 255, 255,0.14);
                        color: var(--header-vertical-mini-text-color) !important;
                        @media (min-width: 768px) {
                            width: 100px;
                            height: 38px;
                            margin-right: 16px;
                        }
                    }
                }
                .o_debug_manager,.MessagingMenuContainer,.o-mail-DiscussSystray-class,.o_switch_company_menu{
                    background-color: transparent !important;
                    .dropdown-toggle, .sysbutton{
                        display: block;
                        text-align: center;
                        border: 0;
                        box-shadow: 0 2px 4px 0 rgba(79, 131, 183, 0.1);
                        border-radius: 50%;
                        cursor: pointer;
                        position: relative;
                        background-color: rgba(255, 255, 255,0.14);
                        color: var(--header-vertical-mini-text-color) !important;
    
                        @media (max-width: 767.98px) {
                            width: 32px;
                            height: 32px;
                            line-height: 32px;
                            margin-right: 8px;
                        }
                        @media (min-width: 768px) {
                            width: 38px;
                            height: 38px;
                            line-height: 38px;
                            margin-right: 16px;
                        }
    
                        .badge{
                            position: absolute !important;
                            top: -3px !important;
                            right: 0;
                            font-size: 10px;
                            font-weight: 700;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: normal;
                            letter-spacing: 0.05px;
                            background-color: #5AC89D;
                            color: #fff;
                            top: -3px;
                            left: 67%;
                            min-width: 18px;
                            padding: 2px;
                            height: 17px;
                        }
                    }
                }
                background-color: transparent !important;
                button,.dropdown-toggle{
                    display: block;
                    text-align: center;
                    border: 0;
                    box-shadow: 0 2px 4px 0 rgba(79, 131, 183, 0.1);
                    border-radius: 50%;
                    cursor: pointer;
                    position: relative;
                    background-color: rgba(255, 255, 255,0.14);
                    color: var(--header-vertical-mini-text-color) !important;
                    @media (max-width: 767.98px) {
                        width: 32px;
                        height: 32px;
                        line-height: 32px;
                        margin-right: 8px;
                    }
                    @media (min-width: 768px) {
                        width: 38px;
                        height: 38px;
                        margin-right: 16px;
                    }
                    .badge{
                        position: absolute !important;
                        top: -3px !important;
                        right: 0;
                        font-size: 10px;
                        font-weight: 700;
                        font-stretch: normal;
                        font-style: normal;
                        line-height: normal;
                        letter-spacing: 0.05px;
                        background-color: #5AC89D;
                        color: #fff;
                        top: -3px;
                        left: 67%;
                        min-width: 18px;
                        padding: 2px;
                        height: 17px;
                    }
                }
    
    
                .o_switch_company_menu{
                    display: flex;
                    align-items: center;
                    .o_user_lang {
                        position: relative;
                        .dropdown-toggle::after {
                            display: none !important;
                        }
                        .dropdown-menu.show {
                            max-height: unset !important;
                            min-width: max-content;
                            overflow: auto;
                            padding: 0px;
                            border-radius: var(--border-radius-md);
                            border: 0 !important;
                            right: 0 !important;
    
                            .active_lang{
                                padding-top: 5px;
                                padding-bottom: 5px;
                                
                                .dispalay_lang{
                                    padding: 5px 15px;
                                    &:hover{
                                        filter: brightness(0.8);
                                    }
                                    .lang-name{
                                        font-size: 14px;
                                        cursor: pointer;
                                        &.active{
                                            color: var(--biz-theme-primary-color);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .o_user_menu{
                    .dropdown-menu{
                        padding: 20px !important;
                    }
                    .dropdown-toggle{
                        display: flex;
                        align-items: center;
                        background-color: transparent;
                        appearance: none !important;
                        border: 0;
                        --webkit-appearance: none;
                        align-items: center;
                        padding-left: 170px;
                        
                        img{
                            max-width: 42px;
                        }
                        .oe_topbar_name{
                            font-size: 12px;
                            line-height: 12px;
                            font-weight: 500;
                        }
                    }
                }
            }
            @media (max-width: 1199.98px) {
                background-position: 15% center;
            }
    
            @media (min-width: 1200px) {
                background-position: center;
                background-size: 100%;
            }
            .dropdown-menu{
                max-height: 65vh;
                transition: 0s !important;
            }
            @include media-breakpoint-down(md){
                .o_menu_sections{
                    display: block !important;
                }
            }
            .o_menu_systray{
                .o_mail_systray_dropdown {
                    .o_mail_preview {
                        background-color: transparent !important;
                        transition: 0.3s;
    
                        .o_mail_preview_app{
                            > img{
                                border-radius: var(--border-radius-md);
                            }
                        }
        
                        .o_preview_title {
                            &:hover {
                                color: var(--biz-theme-primary-color);
                            }
                        }
                    }
                }
                .o_NotificationList {
                    .o_NotificationGroup_date {
                        color: var(--biz-theme-primary-color);
                    }
                    &>div {
                        background-color: transparent !important;
                        transition: 0.3s;
        
                        &:hover {
                            color: var(--biz-theme-primary-color);
                        }
                    }
                }
                .o_mail_systray_dropdown, .o_MessagingMenu_dropdownMenu, .o_debug_manager > .dropdown-menu {
                    &.dropdown-menu-right {
                        max-height: unset !important;
                        min-width: auto;
                        overflow: auto;
                        padding: 10px !important;
                        border-radius: var(--border-radius-md);
                        right: unset !important;
                        top: 30px;
                        border: 0 !important;
                    }
                    &.show .dropdown-toggle {
                        background-color: transparent !important;
                    }
                }
                .o-mail-DiscussSystray-class.show {
                    background-color: transparent !important;
                    .dropdown-toggle {
                        background: rgba(255, 255, 255,0.14);
                    }
                    .o-mail-MessagingMenu-header {
                        .o_MessagingMenu_tabButton{
                            &:not(:last-child) {
                                margin-right: 10px;
                            }
                        }
                    }
                }
                .o_debug_manager > .dropdown-menu {
                    .dropdown-item {
                        transition: 0.2s;
                        &:hover {
                            background-color: transparent !important;
                            color: var(--biz-theme-primary-color) !important;
                        }
                    }
                }
                display: flex;
            }
        }
    }
    // common css for both vertical and horizontal end

    // FAVOURITE APPS ISLAND STYLE SCSS
    .fav_app_island {
        .fav_app_island_btn {
            font-size: 25px;
            width: 25px;
            height: 25px;
            line-height: 25px;
            color: white;
            vertical-align: middle;
            cursor: pointer;
            transition: 0.2s;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .fav_apps {
            .app-box {
                .app-image {
                    .ri{
                        font-size: 30px;
                        color: white;
                    }
                }
                width: 30px;
                height: 30px;
                line-height: 30px;
                margin: 0 10px;
                img{
                    width: 30px;
                    height: 30px;
                    line-height: 30px;   
                }
            }
            transition: 0.2s;
            transform: scale(0);
            overflow: hidden;
        }
        position: fixed;
        bottom: 10px;
        left: 50%;
        z-index: 80;
        padding: 10px;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.35);
        box-shadow: var(--box-shadow-common) !important;
        border-radius: var(--border-radius-lg);
        backdrop-filter: blur(2px);
        text-align: center;
        min-height: 45px;
        min-width: 45px;
        max-height: 45px;
        max-width: 45px;
        &:hover {
            .fav_app_island_btn {
                transform: scale(0);
            }
            .fav_apps {
                transform: scale(1);
            }
            max-width: 100%;
            max-height: 100%;
        }
        @include media-breakpoint-down(md){
            .fav_apps {
                flex-wrap: wrap;
            }
            display: none !important;
        }
    }

    &.top_menu_vertical{
        .o_website_publish_container{
            margin-left: 0 !important;
            order: -1;
        }
        .o_mobile_preview{
            order: -1;
        }
        &:not(.editor_has_snippets_hide_backend_navbar){
            header + .o_action_manager{
                padding: 0px 0rem 0px 19rem;
                @include media-breakpoint-down(lg){
                    padding: 0px 0rem 0px 19rem;
                }
                .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                    // padding-left: 4.375rem !important;
                    padding-right: 4.375rem !important;
                    transition: 0.2s;

                    @include media-breakpoint-down(lg){
                        padding-left: 4.8125rem !important;
                    }
                }
                // > .o_action:not(.o_view_controller):not(.o_spreadsheet_dashboard_action){
                    // > .o_control_panel{
                        // padding-left: 4.375rem !important;
                    // }

                    // > .o_content{
                        // iframe{
                            // padding-left: 4.375rem !important;
                        // }
                    // }
                // }
                .o_action.o_view_controller.o_kanban_view{
                    padding: unset;
                }
            }
        }
        &:not(.bookmark_panel_show) {
            header + .o_action_manager{
                .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                    padding-right: 0rem !important;
                }
            }
        }
        .o_action_manager {
            .o_action.o_view_controller.o_kanban_view{
                padding: 0 5rem;
            }
        }
        .o_main_navbar {
            display: flex;
            flex-direction: column;
            * {
                transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
            }
            .o_company_logo {
                .company_logo {
                    max-height: 60px;
                }
                max-height: 60px;
            }
            transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
        }
        &:not(.pinned) {
            .o_main_navbar:not(:hover) { //:not(:hover)
                .o_menu_systray {
                    .o_user_menu {
                        .user-info {
                            opacity: 0;
                            visibility: hidden;
                            width: 0;
                            height: 0;
                        }
                        .o_user_menu {
                            padding: 10px 0 !important;
                        }
                        .o-dropdown--menu.dropdown-menu{
                            opacity: 0;
                            visibility: hidden;
                        }
                        margin-left: 0px;
                    }
                    > div:not(.o_user_menu) {
                        opacity: 0;
                        visibility: hidden;
                        width: 0;
                    }

                    > button:not(.o_user_menu) {
                        opacity: 0;
                        visibility: hidden;
                        width: 0;
                    }
                }
                .o_navbar_apps_menu {
                    > ul > li {
                        > a {
                            > span:not(.app_icon) {
                                opacity: 0;
                                visibility: hidden;
                                width: 0;
                                margin: 0 !important;
                                height: 0;
                            }
                            > span.app_icon {  
                                margin: 0 !important;
                            }
                            justify-content: center;
                        }
                        > ul {
                            display: none;
                        }
                    }
                    padding: 0 12px !important;
                }
                .o_company_logo {
                    .company_logo_icon {
                        display: block !important;
                        margin: auto;
                    }
                    .company_logo {
                        display: none !important;
                    }
                    .img{
                        max-height: 50px;
                    }
                    margin-left: 15px;
                    margin-right: 15px;
                    text-align: left;
                    position: relative;
                    overflow: hidden;
                }
                width: 80px !important;
                transition: 0.3s;
            }
            header + .o_action_manager{
                @include media-breakpoint-up(lg){
                    padding: 0px 0rem 0px 5.5rem;
                }
                @include media-breakpoint-down(md){
                    padding: 0px 0rem 0px 1rem;
                }
                .o_action.o_view_controller.o_kanban_view{
                    padding: unset;
                }
            }
        }
    }
    &.top_menu_vertical{
        .o_main_navbar {
            .o_app_drawer {
                display: none;
            }
            .o_menu_systray {
                > li {
                    order: 3;
                }
                .o_switch_company_menu {
                    margin-bottom: 0;
                    width: 100%;
                    order: -1;
                }
                .o_user_menu{
                    width: 100%;
                    width: -moz-available;
                    width: -webkit-fill-available;
                    order: -2;
                    margin-left: 0;

                    .o_user_menu{
                        width: 100%;
                        width: -moz-available;
                        width: -webkit-fill-available;
                        .dropdown-toggle{
                            display: block;
                            height: unset;
                            width: 100%;
                            padding: 0;
                        }
                        .oe_topbar_name{
                            max-width: -webkit-fill-available;
                            max-width: -moz-available;
                            max-width: calc(var(--vertical-menu-width) - 30px);
                        }
                    }
                }
                .vertical_sidemenu_behaviour {
                    display: block !important;
                }
                margin-bottom: 20px !important;
                padding: 0 15px;
                flex-wrap: wrap;
                justify-content: center;
                align-items: center;
            }
            .o_mail_systray_item {
                .dropdown-toggle {
                    background-color: transparent !important;
                }
            }
            .MessagingMenuContainer, .o-mail-DiscussSystray-class {
                .o_MessagingMenu_dropdownMenu, .o_ActivityMenuView_dropdownMenu {
                    left: 0;
                    padding: 10px !important;
                }
            }
            .o_debug_manager, .o_mail_systray_item, .o-mail-DiscussSystray-class, .o-mail-DiscussSystray-class{
                .dropdown-toggle, .o_MessagingMenu_toggler {
                    .badge-pill ,.rounded-pill ,.badge{
                        transform: translateY(-100%) !important;
                        top: 50%;
                    }
                }
            }
            .dropdown-toggle {
                padding: 0 10px;
                .badge{
                    top: 50%;
                    transform: translateY(-100%) !important;
                    text-shadow: revert;
                }
            }
            .o_company_logo {
                text-align: center;
                margin-top: 20px;
                margin-bottom: 20px;
                img{
                    max-width: 180px;
                }
            }
            .o_menu_brand, .o_menu_sections {
                display: none !important;
            }
            // app menu drop-down designs 
            .o_navbar_apps_menu {
                padding: 0 5px;
                display: block;
                height: unset;
                grid-area: none;
                list-style: none;
                > ul {
                    > li {
                        ul {
                            p {
                                font-size: 12px;
                                background-color: rgba(255, 255, 255, 0.1) !important;
                                border-radius: var(--border-radius-lg) !important;
                                text-align: center;
                                margin-bottom: 0.5rem !important;
                                margin-top: 0.5rem !important;
                                padding: 5px 15px;
                                color: var(--biz-theme-primary-text-color);
                                > a[aria-expanded=true] {
                                    .ri::before {
                                        content: "\ea4e";
                                    }
                                }
                            }
                            .child_menus {
                                span{
                                    pointer-events: none;
                                }
                                cursor: pointer;
                                padding: 0.5rem 1rem !important;
                                color: var(--biz-theme-primary-text-color) !important;
                                display: inline-block;
                                margin-bottom: 0.1rem !important;
                                margin-top: 0.1rem !important;
                                width: 100%;
                                border-radius: var(--border-radius-lg) !important;
                                transition: background-color 0.3s ease;
                                &.active{
                                    background-color: rgba(255, 255, 255, 0.1) !important;
                                }
                            }
                            display: none;
                            list-style: unset;
                            padding-left: 15px;
                            &.show{
                                display: block;
                            }
                        }
                        > a {
                            color: var(--biz-theme-primary-text-color) !important;
                            padding: 0.75rem 1rem !important;
                            display: inline-flex !important;
                            margin-bottom: 0.1rem !important;
                            margin-top: 0.1rem !important;
                            width: 100%;
                            border-radius: var(--border-radius-lg) !important;
                            transition: background-color 0.3s ease;
                            &:hover {
                                background-color: rgba(255, 255, 255, 0.1) !important;
                            }
                        }
                    }
                    max-height: calc(100vh - 104px - 240px) !important;
                    list-style: unset !important;
                    overflow: hidden !important;
                    overflow-y: auto !important;
                    padding: 0;
                    padding-bottom: 15px !important;
                    scrollbar-width: none; // hide scrollbar in firefox
                }
                .app_icon {
                    img {
                        max-width: 20px;
                        min-width: 20px;
                    }
                    .ri {
                        font-size: 20px;
                        vertical-align: middle;
                    }
                    margin-right: 5px;
                }
                .main_link {
                    display: flex;
                    width: 100%;
                    align-items: center;
                    > * {
                        pointer-events: none;
                    }
                    &.active{
                        .ri.ri-arrow-right-s-line::before {
                            content: "\ea4e";
                        }
                        background-color: rgba(255, 255, 255, 0.1) !important;
                    }
                    &:hover {
                        background-color: rgba(255, 255, 255, 0.1) !important;
                    }
                }
            }
            > div {
                > div {
                    > a, > div > a, div > label {
                        // line-height: 30px !important;
                        height: inherit !important;
                        cursor: pointer;
                        &:hover {
                            background-color: transparent !important;
                        }
                    }
                    float: unset;
                }
                float: unset;
            }
            // background: linear-gradient(
            //     to bottom, 
            //     rgba(255, 255, 255, 0) 0%, 
            //     rgba(255, 255, 255, 0.6) 70%, 
            //     rgba(255, 255, 255, 1) 100%
            //   ),
            //   url('/spiffy_theme_backend/static/src/image/background.jpeg') no-repeat center center;
            // background: linear-gradient(
            //     to bottom, 
            //     rgba(var(--biz-theme-primary-color-rgb), 0) 0%,
            //     rgba(var(--biz-theme-primary-color-rgb), 0.6) 60%,
            //     rgba(var(--biz-theme-primary-color-rgb), 1) 100% 
            //   ),
            // background-size: cover;
            background-color: var(--biz-theme-primary-color) !important;
            height: 100vh !important;
            position: fixed;
            width: var( --vertical-menu-width);
            min-width: unset;
            z-index: 5;
            padding-top: 0 !important;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
            border-top-right-radius: var(--border-radius-lg) !important;
            border-bottom-right-radius: var(--border-radius-lg) !important;
        }
        
    }
    &.top_menu_vertical_mini{
        .border-style, .tab-pane{
            .form-switch .form-check-input:checked~.form-check-label::before{
                background-color: var(--biz-theme-primary-color) !important;
            }
            .form-switch .form-check-label::before {
                background-color: transparent !important;
            }
            .form-switch .form-check-label::after {
                background-color: var(--biz-theme-primary-color) !important;
            }
            .form-switch .form-check-input:checked~.form-check-label::after {
                background-color: var(--biz-theme-primary-text-color) !important;
            }
        }
        // WHEN DATABASE IN NEUTRILIZED MODE; ODOO ADDS A BANNER AT TOP OF THE SCREEN;
        .oe_neutralize_banner_active + header{
            color: red !important;
            .o_main_navbar{
                top: calc(70px + 24px) !important;
                .o_navbar_apps_menu{
                    #accordion{
                        .header-sub-menus{
                            top: calc(70px + 24px) !important;
                        }
                    }
                }
            }
        }
        &:not(.editor_has_snippets_hide_backend_navbar){
            header + .o_action_manager{
                @include media-breakpoint-up(lg){
                    padding: 0px 0rem 0px var(--header-vertical-mini-menu-width);
                }
                .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                    // padding-left: 2rem !important;
                    padding-right: 5rem !important;
                    transition: 0.2s;
                    @include media-breakpoint-down(lg){
                        padding-left: 2.5rem !important;
                    }
                }
                // > .o_action:not(.o_view_controller):not(.o_spreadsheet_dashboard_action){
                //     > .o_control_panel{
                //         // padding-left: 2rem !important;
                //     }

                //     > .o_content{
                //         iframe{
                //             padding-left: 2rem !important;
                //         }
                //     }
                // }
                .o_action.o_view_controller.o_kanban_view{
                    padding: unset;
                }
            }
        }
        @include media-breakpoint-up(lg){
            &:not(.bookmark_panel_show){
                header + .o_action_manager{
                    .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                        padding-right: 1rem !important;
                    }
                }
            }
        }
        .o_main_navbar {
            display: flex;
            flex-direction: column;
            * {
                transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
            }
            .o_company_logo {
                .company_logo {
                    max-height: 60px;
                }
                max-height: 60px;
            }
            transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
        }
    }
    &.top_menu_vertical_mini{
        .o_spreadsheet_dashboard_action{
            .o_side_panel_filter_icon{
                margin-top: 10px;
            }
            .o_record_autocomplete_with_caret{
                .o_input{
                    border: 0;
                }
            }
        }
        .o_main_navbar {
            .o_app_drawer {
                display: none;
            }
            .o_menu_systray {
                > li {
                    order: 3;
                }
                .o_switch_company_menu {
                    width: 100%;
                    order: -1;
                }
                .o_user_menu{
                    width: 100%;
                    width: -moz-available;
                    width: -webkit-fill-available;
                    order: -2;
                    margin-left: 0;

                    .o_user_menu {
                        width: 100%;
                        width: -moz-available;
                        width: -webkit-fill-available;
                        .dropdown-toggle{
                            display: block;
                            width: 100%;
                            padding: 0;
                        }
                        .oe_topbar_name{
                            max-width: -webkit-fill-available;
                            max-width: -moz-available;
                            max-width: calc(var(--header-vertical-mini-menu-width) - 30px);
                        }
                    }
                }
                .vertical_sidemenu_behaviour {
                    display: block !important;
                }
                margin-bottom: 30px !important;
                padding: 0 15px;
                flex-wrap: wrap;
                justify-content: center;
                align-items: center;
            }
            .o_mail_systray_item {
                .dropdown-toggle {
                    background-color: transparent !important;
                }
            }
            .MessagingMenuContainer, .o_ActivityMenuView {
                .o_MessagingMenu_dropdownMenu, .o_ActivityMenuView_dropdownMenu {
                    left: 0;
                    padding: 10px !important;
                }
            }
            .o_company_logo {
                text-align: center;
                margin-top: 20px;
                margin-bottom: 20px;
                img{
                    max-width: 180px;
                }
            }
            .o_menu_brand, .o_menu_sections {
                display: none !important;
            }
            // app menu drop-down designs 
            .o_navbar_apps_menu {
                // padding: 0 30px;
                height: 100% !important;
                display: block;
                height: unset;
                grid-area: none;
                list-style: none;
                border-bottom: none;
                > ul {
                    > li {
                        ul {
                            p {
                                font-size: 12px;
                                background-color: rgba(255, 255, 255, 0.5) !important;
                                border-radius: var(--border-radius-lg) !important;
                                text-align: center;
                                margin-bottom: 0.5rem !important;
                                margin-top: 0.5rem !important;
                                padding: 5px 15px;
                                color: var(--header-vertical-mini-text-color);
                                > a[aria-expanded=true] {
                                    .ri::before {
                                        content: "\ea4e";
                                    }
                                }
                            }
                            .child_multi_menu{
                                padding: 0.5rem 0 !important;
                                font-size: 12px;
                                font-weight: 500;
                                font-stretch: normal;
                                font-style: normal;
                                line-height: normal;
                                letter-spacing: 0.8px;
                                text-transform: uppercase;

                                &.collapsed{
                                    .ri{
                                        &::before{
                                            content: '\ea6e' !important;
                                        }
                                    }
                                }

                                span{
                                    color: #acafb3 !important;
                                }
                            }
                            .child_menus {
                                span{
                                    pointer-events: none;
                                }
                                cursor: pointer;
                                padding: 0.5rem 0 !important;
                                display: inline-block;
                                // margin-bottom: 0.1rem !important;
                                // margin-top: 0.1rem !important;
                                width: 100%;
                                transition: background-color 0.3s ease;
                                // &.active{
                                //     background-color: rgba(255, 255, 255, 0.1) !important;
                                // }
                            }
                            // display: none;
                            &:not(.show){
                                visibility: hidden;
                                height: 0;
                                width: 0;
                            }
                            list-style: unset;
                            padding-left: 15px;
                            &.show{
                                display: block;
                            }
                        }
                        &:not(:last-child){
                            > a{
                                &::after{
                                    content: "";
                                    position: absolute;
                                    height: 1px;
                                    right: 12px;
                                    left: 12px;
                                    background-color: #d4e7f4;
                                    bottom: 0;
                                }
                            }
                        }
                        > a {
                            padding: 1.25rem 1rem !important;
                            display: inline-flex !important;
                            margin-bottom: 0.1rem !important;
                            margin-top: 0.1rem !important;
                            width: 100%;
                            border-radius: 0 !important;
                            transition: background-color 0.3s ease;
                            flex-direction: column;
                            position: relative;
                            color: var(--header-vertical-mini-text-color) !important;
                            .menu_name{
                                font-weight: 500 !important;
                            }
                            &:hover {
                                background-color: rgba(255, 255, 255, 0.2) !important;
                            }
                            &.selected, &.active{
                                &::before{
                                    content: "";
                                    position: absolute;
                                    width: 3px;
                                    top: 9px;
                                    bottom: 9px;
                                    background-color: var(--header-vertical-mini-text-color);
                                    left: 0;
                                }
                            }
                        }
                    }
                    max-height: calc(100vh - 70px - 10px) !important; // 10px for safety margin
                    list-style: unset !important;
                    overflow: hidden !important;
                    overflow-y: auto !important;
                    padding: 0;
                    scrollbar-width: none; // hide scrollbar in firefox
                }
                .app_icon {
                    img {
                        // height: 24px;
                        // max-width: 24px;
                        // min-width: 24px;
                        object-fit: contain;
                    }
                    .ri {
                        font-size: 24px;
                        vertical-align: middle;
                    }
                }
                .main_link {
                    display: flex;
                    width: 100%;
                    align-items: center;
                    text-align: center;
                    flex-direction: column;
                    .dropdown_icon{
                        display: none;
                    }
                    > * {
                        pointer-events: none;
                    }
                    &.active,&.selected,&:hover{
                        background-color: rgba(255, 255, 255, 0.2) !important;
                        *{
                            color: var(--biz-theme-primary-color) !important;
                        }
                    }
                    &.active{
                        .ri.ri-arrow-right-s-line::before {
                            content: "\ea4e";
                        }
                        background-color: rgba(255, 255, 255, 0.1) !important;
                    }
                }
            }
            > div {
                > div {
                    > a, > div > a, div > label {
                        // line-height: 30px !important;
                        height: inherit !important;
                        cursor: pointer;
                        &:hover {
                            background-color: transparent !important;
                        }
                    }
                    float: unset;
                }
                float: unset;
            }
            background-color: var(--header-vertical-mini-bg-color);
            color: var(--header-vertical-mini-text-color);
            height: calc(100vh - 70px) !important;
            position: fixed;
            top: 70px;
            width: var( --header-vertical-mini-menu-width);
            min-width: unset;
            z-index: 100;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
            border-top-right-radius: 0 !important;
            border-bottom-right-radius: 0 !important;
        }
        
    }

    &.top_menu_vertical_mini_2{
        &:not(.editor_has_snippets_hide_backend_navbar){
            header + .o_action_manager{
                padding: 0px 0rem 0px 19rem;
                @include media-breakpoint-down(lg){
                    padding: 0px 0rem 0px 19rem;
                }
                .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                    // padding-left: 4.375rem !important;
                    padding-right: 4.375rem !important;
                    transition: 0.2s;
                    @include media-breakpoint-down(lg){
                        padding-left: 4.8125rem !important;
                    }
                }
                // > .o_action:not(.o_view_controller):not(.o_spreadsheet_dashboard_action){
                    // > .o_control_panel{
                        // padding-left: 4.375rem !important;
                    // }
                    // > .o_content{
                        // iframe{
                            // padding-left: 4.375rem !important;
                        // }
                    // }
                // }
                .o_action.o_view_controller.o_kanban_view{
                    padding: unset;
                }
            }
        }
        &:not(.bookmark_panel_show) {
            header + .o_action_manager{
                .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                    padding-right: 0rem !important;
                }
            }
        }
        .o_action_manager {
            .o_action.o_view_controller.o_kanban_view{
                padding: 0 5rem;
            }
        }
        .o_main_navbar {
            display: flex;
            flex-direction: column;
            * {
                transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
            }
            .o_company_logo {
                .company_logo {
                    max-height: 60px;
                }
                max-height: 60px;
            }
            transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
        }
        &:not(.pinned) {
            .o_main_navbar:not(:hover) { //:not(:hover)
                .o_menu_systray {
                    .o_user_menu {
                        .user-info {
                            opacity: 0;
                            visibility: hidden;
                            width: 0;
                            height: 0;
                        }
                        .user-image-style {
                            padding: 10px 0 !important;
                        }
                        .o-dropdown--menu.dropdown-menu{
                            opacity: 0;
                            visibility: hidden;
                        }
                        // margin-left: 0px;
                    }
                    > div:not(.o_user_menu) {
                        opacity: 0;
                        visibility: hidden;
                        width: 0;
                    }
            
                    > button:not(.o_user_menu) {
                        display: none;
                    }
                }
                .o_navbar_apps_menu {
                    > ul > li {
                        > a {
                            > span:not(.app_icon) {
                                opacity: 0;
                                visibility: hidden;
                                width: 0;
                                margin: 0 !important;
                                height: 0;
                            }
                            > span.app_icon {  
                                margin: 0 !important;
                            }
                            justify-content: center;
                        }
                        > ul {
                            display: none;
                        }
                    }
                    padding: 0 12px !important;
                }
                .o_company_logo {
                    .company_logo_icon {
                        display: block !important;
                        margin: auto;
                    }
                    .company_logo {
                        display: none !important;
                    }
                    .img{
                        max-height: 50px;
                    }
                    margin-left: 15px;
                    margin-right: 15px;
                    text-align: left;
                    position: relative;
                    overflow: hidden;
                }
                width: 80px !important;
                transition: 0.3s;
            }
            header + .o_action_manager{
                @include media-breakpoint-up(lg){
                    padding: 0px 0rem 0px 5.5rem;
                }
                @include media-breakpoint-down(md){
                    padding: 0px 0rem 0px 1rem;
                }
                .o_action.o_view_controller.o_kanban_view{
                    padding: unset;
                }
            }
        }
    }
    &.top_menu_vertical_mini_2{
        .border-style, .tab-pane{
            .form-switch .form-check-input:checked~.form-check-label::before{
                background-color: var(--biz-theme-primary-color) !important;
            }
            .form-switch .form-check-label::before {
                background-color: transparent !important;
            }
            .form-switch .form-check-label::after {
                background-color: var(--biz-theme-primary-color) !important;
            }
            .form-switch .form-check-input:checked~.form-check-label::after {
                background-color: var(--biz-theme-primary-text-color) !important;
            }
        }
        .o_menu_systray_item{
            .dropdown-toggle{
                background-color: rgba(255, 255, 255, 0) !important;
            }
        }
        .o_website_publish_container{
            margin-bottom: 10px;
            order: -1;
            margin-left: -7px !important;
            .form-switch{
                margin-left: 15px !important;
                .form-check-label{
                    padding-left: 4rem !important;
                }
            }
        }
        .o_mobile_preview{
            margin-left: -15px !important;
            margin-right: 5px;
            order: -1;
            .o_nav_entry{
                padding-left: 0 !important;
                padding-right: 0 !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
        }
        .o_main_navbar {
            background-repeat: no-repeat;
            background-size: cover;
            .dropdown-toggle{
                padding-left: 50px;
            }
            .o_website_publish_container{
                margin-bottom: 10px;
                .dropdown-toggle{
                    background-color: rgba(255, 255, 255, 0) !important;
                    opacity: 0;
                }
                .o-checkbox{
                    padding-left: 10px;
                }
            }
            .o_app_drawer {
                display: none;
            }
            .o_menu_systray {
                > li {
                    order: 3;
                }
                .o-mail-DiscussSystray-class .dropdown-toggle, .dropdown-toggle{
                    margin-right: 0.75rem;
                    height: 30px;
                    width: 30px;
                    background-color: rgba(255, 255, 255, 0.2);
                    justify-content: center;
                    border-radius: 14%;
                }
                .o_switch_company_menu {
                    .debug_activator{
                        margin-left: 0.3rem !important;
                    }
                    .theme_selector{
                        margin-left: 0.3rem !important;
                    }
                    .header_to_do_list{
                        margin-left: 0.3rem !important;
                    }
                    .dark_mode{
                        margin-left: 0.3rem !important;
                    }
                    .o_user_lang{
                        margin-right: 0.3rem !important;
                    }
                    margin-bottom: 0.75rem !important;
                    justify-content: flex-start !important;
                    width: 100%;
                    order: -1;
                    .o_switch_company_menu{
                        background-color: rgba(255, 255, 255, 0) !important;
                    }
                    > div{
                        height: 30px !important;
                        width: 30px !important;
                        background-color: rgba(255, 255, 255, 0.2) !important;
                        border-radius: 14% !important;
                        > a{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                        .debug_activator, .theme_selector, .vertical_sidemenu_behaviour, .header_to_do_list{
                            height: 30px !important;
                            width: 30px !important;
                            padding: 5px;
                            > .label{
                                display: flex;
                                justify-content: center;
                                align-items: center;
                            }
                        }
                        .dark-light-mode-button-design{
                            height: 30px;
                            width: 30px;
                            > .label{
                                display: flex;
                                justify-content: center;
                                align-items: center;
                            }
                        }
                    }
                }
                .o_user_menu{
                    .oe_topbar_avatar{
                        border-radius: var(--border-radius-lg) !important;
                    }
                    .user-info {
                        margin-right: 10px;
                        align-items: start;
                        margin-top: 15px;
                        line-height: 1.5;
                        .oe_topbar_name{
                            display: flex;
                            flex-direction: column;
                            align-items: start;
                            margin-left: 10px;
                        }
                        .greeting{
                            align-items: start;
                            margin-left: 10px;
                        }
                    }
                    width: 100%;
                    width: -moz-available;
                    width: -webkit-fill-available;
                    order: -2;
                    margin-left: -10px;
                    margin-bottom: inherit;
                    .dropdown-toggle{
                        margin-right: 0 !important;
                        height: 30px !important;
                        justify-content: start;
                        width: 30px !important;
                        background-color: rgba(255, 255, 255, 0) !important;
                        border-radius: 0 !important;
                    }
                    .user-image-style{
                        padding-top: 0px;
                        padding-left: 0px;
                        padding-bottom: 15px !important;
                        margin-bottom: 0px !important;
                        width: 100%;
                        width: -moz-available;
                        width: -webkit-fill-available;
                        .dropdown-toggle{
                            display: flex !important;
                            width: 100%;
                            padding: 0;
                        }
                        .oe_topbar_name{
                            display: contents;
                            max-width: -webkit-fill-available;
                            max-width: -moz-available;
                            max-width: calc(var(--vertical-menu-width) - 30px);
                            .database_name{
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                /* font-size: 12px; */
                                max-width: 120px;
                            }
                        }
                        &:hover {
                            box-shadow: none !important;
                            background-color:transparent !important;
                        }
                    }
                }
                .vertical_sidemenu_behaviour {
                    margin-left: 0.3rem !important; 
                    display: block !important;
                }
                margin-bottom: 17px !important;
                padding: 0 15px;
                flex-wrap: wrap;
                align-items: center;
                > .o-dropdown{
                    button{
                        margin-right: 0.75rem;
                    height: 30px !important;
                    width: 30px !important;
                    background-color: rgba(255, 255, 255, 0.1) !important;
                    justify-content: center;
                    border-radius: 14% !important;}
                    
                }
            }
            .o_mail_systray_item {
                .dropdown-toggle {
                    background-color: transparent !important;
                }
            }
            .MessagingMenuContainer, .o-mail-DiscussSystray-class {
                .o_MessagingMenu_dropdownMenu, .o_ActivityMenuView_dropdownMenu {
                    left: 0;
                    padding: 10px !important;
                }
            }
            .o_company_logo {
                margin-left: 10px;
                margin-top: 15px;
                margin-bottom: 15px;
                img{
                    max-width: 180px;
                }
            }
            .o_menu_brand, .o_menu_sections {
                display: none !important;
            }
            // app menu drop-down designs 
            .o_navbar_apps_menu {
                padding: 0 15px;
                padding-left: 8px;
                display: block;
                height: unset;
                grid-area: none;
                list-style: none;
                > ul {
                    > li {
                        ul {
                            p {
                                font-size: 12px;
                                background-color: rgba(255, 255, 255, 0.1) !important;
                                border-radius: var(--border-radius-lg) !important;
                                text-align: center;
                                margin-bottom: 0.5rem !important;
                                margin-top: 0.5rem !important;
                                padding: 5px 15px;
                                color: var(--biz-theme-primary-text-color);
                                
                                > a {
                                    font-size: 13px;
                                    padding: 5px 0.50rem;
                                    border-radius: var(--border-radius-lg) !important;
                                }
                                > a[aria-expanded=true] {
                                    .ri::before {
                                        content: "\ea4e";
                                    }
                                    &.selected, &.active{
                                        background-color: rgba(255, 255, 255, 0.1) !important;
                                    }
                                }
                                &.active{
                                    background-color: rgba(255, 255, 255, 0.1) !important;
                                }
                            }
                            .child_menus {
                                span{
                                    pointer-events: none;
                                    &.selected, &.active{
                                        background-color: rgba(255, 255, 255, 0.1) !important;
                                    }
                                }
                                font-size: 13px;
                                cursor: pointer;
                                padding: 0.5rem 1rem !important;
                                color: var(--biz-theme-primary-text-color) !important;
                                display: inline-block;
                                margin-bottom: 0.1rem !important;
                                margin-top: 0.1rem !important;
                                width: 100%;
                                border-radius: var(--border-radius-lg) !important;
                                transition: background-color 0.3s ease;
                                &.active {
                                    background-color: rgba(255, 255, 255, 0.1) !important;
                                }
                            }
                            .child_menus:hover{
                                background-color: rgba(255, 255, 255, 0.1) !important;
                            }
                            display: none;
                            list-style: unset;
                            padding-left: 15px;
                            &.show{
                                display: block;
                            }
                        }
                        > a {
                            font-size: 13px;
                            color: var(--biz-theme-primary-text-color) !important;
                            padding: 0.75rem 0.50rem !important;
                            display: inline-flex !important;
                            margin-bottom: 0.1rem !important;
                            margin-top: 0.1rem !important;
                            width: 100%;
                            border-radius: var(--border-radius-lg) !important;
                            transition: background-color 0.3s ease;
                            &:hover {
                                background-color: rgba(255, 255, 255, 0.1) !important;
                            }
                            &.active{
                                background-color: rgba(255, 255, 255, 0.1) !important;
                            }
                        }
                        // .header-sub-menus.show {
                        //     > li {
                        //         > p {
                        //             > a[aria-expanded=true] {
                        //                 background-color: rgba(255, 255, 255, 0.1) !important;
                        //             }
                        //         }
                        //         .collapse.show{
                        //             > ul.show{
                        //                 > li {
                        //                     > p {
                        //                         > a[aria-expanded=true]{
                        //                             background-color: rgba(255, 255, 255, 0.1) !important;
                        //                         }
                                               
                        //                     }
                        //                 }
                        //             }
                        //         }
                        //     }
                        // }
                    }
                    max-height: calc(100vh - 104px - 240px) !important;
                    list-style: unset !important;
                    overflow: hidden !important;
                    overflow-y: auto !important;
                    padding: 0;
                    padding-bottom: 15px !important;
                    scrollbar-width: none; // hide scrollbar in firefox
                }
                .app_icon {
                    img {
                        max-width: 20px;
                        min-width: 20px;
                    }
                    .ri {
                        font-size: 20px;
                        vertical-align: middle;
                    }
                    margin-right: 5px;
                }
                .main_link {
                    display: flex;
                    width: 100%;
                    align-items: center;
                    > * {
                        pointer-events: none;
                    }
                    &.active{
                        .ri.ri-arrow-right-s-line::before {
                            content: "\ea4e";
                        }
                        background-color: rgba(255, 255, 255, 0.1) !important;
                        // background-color: #4256d0 !important;
                    }
                    &:hover {
                        background-color: rgba(255, 255, 255, 0.1) !important;
                    }
                }
            }
            > div {
                > div {
                    > a, > div > a, div > label {
                        // line-height: 30px !important;
                        height: inherit !important;
                        cursor: pointer;
                        &:hover {
                            background-color: transparent !important;
                        }
                    }
                    float: unset;
                }
                float: unset;
            }
            background-color: var(--biz-theme-primary-color) !important;
            height: 100vh !important;
            position: fixed;
            width: var( --vertical-menu-width);
            min-width: unset;
            z-index: 5;
            padding-top: 0 !important;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
            border-top-right-radius: var(--border-radius-lg) !important;
            border-bottom-right-radius: var(--border-radius-lg) !important;
        }
        
    }

    &.top_menu_horizontal {
        %-main-navbar-entry-spacing {
            margin: 0;
            height: auto;
            margin-left: var(--NavBar-entry-margin-left, #{$o-navbar-entry-margin-h});
            margin-right: var(--NavBar-entry-margin-right, #{$o-navbar-entry-margin-h});
            padding: 0;
            padding-left: var(--NavBar-entry-padding-left, #{$o-navbar-entry-padding-h});
            padding-right: var(--NavBar-entry-padding-right, #{$o-navbar-entry-padding-h});
            line-height: calc(var(--o-navbar-height) - #{$o-navbar-padding-v * 2});
            @media (max-width: 992px){
                height: auto;
            }
        }
        .o_website_systray_separator{
            border-left: 0 !important;
        }
        .form-switch .form-check-input:checked~.form-check-label::before{
            background-color: #28a745 !important;
        }
        .form-switch .form-check-label::before {
            background-color: #e6586c !important;
        }
        .form-switch .form-check-label::after {
            background-color: var(--biz-theme-primary-text-color) !important;
        }
        .border-style, .tab-pane{
            .form-switch .form-check-input:checked~.form-check-label::before{
                background-color: var(--biz-theme-primary-color) !important;
            }
            .form-switch .form-check-label::before {
                background-color: transparent !important;
            }
            .form-switch .form-check-label::after {
                background-color: var(--biz-theme-primary-color) !important;
            }
            .form-switch .form-check-input:checked~.form-check-label::after {
                background-color: var(--biz-theme-primary-text-color) !important;
            }
        }
        .o_website_publish_container{
            order: -1;
            // .o-checkbox{
            //     margin-right: 45px !important;
            //     padding-left: inherit;
            // }
        }
        .o_main_navbar {
            display: flex;
            > ul {
                li.o_extra_menu_items{
                    &.show{
                        > ul {
                            > li {
                                a.dropdown-toggle{
                                    color: var(--biz-theme-primary-color);
                                }
                            }
                        }
                    }
                }
            }
            
            .o_navbar_apps_menu {
                #accordion {
                    display: none;
                }
            }
            .o_app_drawer {
                a {
                    .ri {
                        font-size: 30px;
                    }
                }
            }
            .o_menu_sections{
                display: flex;
            }

            .o_menu_sections{
                .dropdown-toggle{
                    height: unset !important;
                    background: transparent;
                    color: var(--biz-theme-primary-text-color);
                    border-color: transparent !important;
                }
            }
                
            .o_menu_sections{
                .o-dropdown .dropdown-toggle, .o_nav_entry{
                    height: var(--horizontal-menu-height);
                    line-height: var(--horizontal-menu-height);
                    white-space: nowrap;

                    &:hover {
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                        background: rgba(255, 255, 255, 0.05) !important;
                    }
                }
            }
            .o_menu_sections, .o_menu_systray, .o_app_drawer{
                > li, > div {
                    > a, .o_MessagingMenu_toggler  {
                        // font-size: 12px;
                        height: var(--horizontal-menu-height);
                        line-height: var(--horizontal-menu-height);
                        cursor: pointer;
                        white-space: nowrap;
                        padding: 0 10px;
                        display: block;
                        &:hover {
                            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                            background: rgba(255, 255, 255, 0.05) !important;
                        }
                    }
                }
                .show .dropdown-toggle {
                    background-color: transparent;
                }
            }

            .o_menu_systray{
                .badge {
                    margin-right: 0;

                }
                margin-left: auto !important;
                > div > a, > div > button, div > div > a {
                    height: 100% !important;
                    &:hover {
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                        background: rgba(255, 255, 255, 0.05) !important;
                    }
                }
                .o_debug_manager, .o_mail_systray_item, .o-mail-DiscussSystray-class, .o-mail-DiscussSystray-class {
                    .dropdown-toggle, .o_MessagingMenu_toggler {
                        .badge-pill ,.rounded-pill ,.badge{
                            transform: translateY(-100%) !important;
                            top: 50%;
                        }
                    }
                }
                .dropdown-toggle {
                    padding: 0 10px;
                    .badge{
                        top: 50%;
                        transform: translateY(-100%) !important;
                        text-shadow: revert;
                    }
                    &:hover {
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                        background: rgba(255, 255, 255, 0.05) !important;
                    }
                }
                .o_switch_company_menu {
                    .o_user_lang {
                        position: relative;
                    }
                    .dropdown-toggle {
                        margin: 0 !important;
                        line-height: var(--horizontal-menu-height);
                        display: block;
                        padding: 0 10px;
                        &:hover {
                            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                            background: rgba(255, 255, 255, 0.05) !important;
                        }
                    }
                    .debug_activator, .theme_selector, .header_to_do_list {
                        a {
                            margin: 0 !important;
                            line-height: var(--horizontal-menu-height);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0 10px;
                            &:hover {
                                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                                background: rgba(255, 255, 255, 0.05) !important;
                            }
                        }
                        margin-left: 0 !important;
                        line-height: var(--horizontal-menu-height);
                    }
                    .dropdown-menu {
                        @media (min-width: 768px){
                            // transform: unset !important;
                            min-width: max-content !important;
                            left: unset !important;
                            right: 0;
                        }
                    }
                    height: var(--horizontal-menu-height);
                    margin: 0;
                }
                .o_user_menu {
                    .o_user_menu {
                        .dropdown-toggle {
                            .oe_topbar_avatar {
                                width: 36px !important;
                                height: 36px !important;
                            }
                            .user-info {
                                align-items: end;
                                margin-right: 10px;
                            }
                            display: flex;
                            flex-direction: row-reverse;
                            align-items: center;
                            height: var(--horizontal-menu-height);
                            @include media-breakpoint-down(md){
                                padding: 0;
                            }
                        }
                        .dropdown-menu {
                            transform: unset !important;
                            // right: 0;
                            left: unset;
                        }
                        &.show {
                            a.dropdown-toggle {
                                background-color: transparent;
                            }
                        }
                        padding: 0 10px !important;
                        margin: 0;
                        border-radius: 0 !important;
                    }
                    order: 1;
                }
                .dark_mode {
                    .dark-light-mode-button-design {
                        label {
                            margin: 0;
                            padding: 0 10px !important;
                            position: relative;
                            // top: -6px;
                        }
                        line-height: var(--horizontal-menu-height);
                    }
                    margin: 0 !important;
                    &:hover {
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                        background: rgba(255, 255, 255, 0.05) !important;
                    }
                }
                position: relative;
                z-index: 90;
            }
            height: var(--horizontal-menu-height);
            @media(min-width: 1400px) {
                padding: 0 50px;
            }
            @media(min-width: 992px) and (max-width: 1399.98px){
                .o_menu_systray {
                    .o_user_menu {
                        .user-info {
                            display: none !important;
                        }
                    }
                }
            }
            @media(min-width: 992px) and (max-width: 1299.98px){
                // font-size: 80%;
                > a{
                    padding: 0 8px 0 10px;
                }
                .o_menu_brand{
                    margin-left: 0 !important;
                }
                .o_company_logo{
                    min-width: 100px;
                    img{
                        max-width: 100px;
                    }
                }
                > ul > li > a, label{
                    padding: 0 5px !important;
                }
            }
            @include media-breakpoint-up(lg){
                padding: 0 20px;
                .o_menu_brand{
                    height: var(--horizontal-menu-height);
                    line-height: 79px;
                    margin-right: 10px;
                    padding: 0 12px 0 16px;
                    font-size: 22px;
                    &:hover {
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
                        background: rgba(255, 255, 255, 0.05) !important;
                    }
                }
                .o_company_logo{
                    img {
                        max-height: 50px;
                    }
                    min-width: 100px;
                    line-height: var(--horizontal-menu-height);
                }
                .o_menu_systray {
                    order: 3;
                }
                
                /* .mobile-header-toggle{
                    display: none !important;
                } */
            }
            .o_menu_sections {
                @media (max-width: 992px) {
                    button .dropdown-toggle{
                        margin-top: 50px;
                    }
                    a {
                        color: white !important;
                    }
                    #mobileMenuclose {
                        display: block !important;
                        padding: 0 15px;
                        line-height: 46px;
                        position: absolute;
                        z-index: 20;
                        right: 10px;
                        top: 10px;
                    }
                    .o_menu_brand {
                        height: 46px !important;
                        line-height: 46px !important;
                        background-color: rgba(255, 255, 255, 0.05);
                        color: var(--biz-theme-primary-text-color) !important;
                        border-radius: var(--border-radius-lg);
                        padding: 0 10px;
                        margin: 10px 0;
                        font-size: inherit;
                    }
                    > li {
                        &.o_extra_menu_items{
                            #mobileMenuclose {
                                right: 0;
                                top: 0;
                            }
                            > a.dropdown-toggle{
                                display: none !important;
                            }
                        }
                        > a {
                            height: 46px !important;
                            color: var(--biz-theme-primary-text-color) !important;
                            line-height: 46px !important;
                        }
                        .dropdown-menu {
                            .dropdown-item {
                                color: var(--biz-theme-primary-text-color) !important;
                            }
                            position: unset;
                            display: block;
                            float: unset;
                            background-color: transparent !important;
                            border: 0;
                            box-shadow: unset;
                            color: var(--biz-theme-primary-text-color) !important;
                            padding-top: 0;
                            padding-bottom: 0;
                        }
                        .dropdown-header, .o_menu_header_lvl_1{
                            background-color: rgba(255, 255, 255, 0.05);
                            color: var(--biz-theme-primary-text-color) !important;
                            border-radius: var(--border-radius-lg);
                            height: 35px !important;
                            line-height: 35px !important;
                            padding: 0 10px;
                            font-weight: normal;
                            font-size: inherit;
                        }
                        float: unset;
                    }
                    position: fixed;
                    float: unset;
                    z-index: 100;
                    background-color: var(--biz-theme-primary-color);
                    color: var(--biz-theme-primary-text-color);
                    top: 0;
                    height: 100%;
                    overflow-y: auto;
                    padding: 0 10px;
                    width: 300px;
                    right: -100%;
                    transition: 0.3s;
                    &.toggle{
                        right: 0%;
                    }
                }
            }
            @include media-breakpoint-down(lg){
                padding: 0 15px;
                display: flex;
                align-items: center;
                > .o_menu_brand, > .o_company_logo{
                    display: none !important;
                }
                .o_app_drawer {
                    .appDrawerToggle {
                        .ri {
                            font-size: 25px;
                        }
                        padding: 0;
                        margin-right: 0 !important;
                    }
                }
                .o_menu_systray {
                    .o_user_menu {
                        .user-info {
                            display: none !important;
                        }
                        .o_user_menu {
                            a.dropdown-toggle .oe_topbar_avatar {
                                width: 30px !important;
                                height: 30px !important;
                            }
                            padding: 0 6.5px !important;
                        }
                        
                        margin-left: 0;
                    }
                    
                    .theme_selector, .debug_activator {
                        display: none !important;
                    }
                    .o_mail_systray_dropdown, .o_MessagingMenu_dropdownMenu, .o_debug_manager > .dropdown-menu {
                        position: fixed !important;
                        width: 100%;
                        right: unset !important;
                        top: 65px !important;
                        left: 0px;
                        left: 0px !important;
                        max-height: unset !important;
                    }
                    margin-left: auto;
                }
                .mobile-header-toggle{
                    margin-left: 10px;
                    a{
                        color: var(--biz-theme-primary-text-color);
                        font-size: 25px;
                    }
                }
            }
            background-color: var(--biz-theme-primary-color) !important;
            color: var(--biz-theme-primary-text-color);
            border-color: var(--biz-theme-primary-color) !important;
            border-top-left-radius: 0 !important;
            border-top-right-radius: 0 !important;
            border-bottom-left-radius: var(--border-radius-lg) !important;
            border-bottom-right-radius: var(--border-radius-lg) !important;
        }
        &.top_menu_vertical_mini_mobile{
            @include media-breakpoint-down(lg){
                .o_main_navbar{
                    background-color: var(--header-vertical-mini-bg-color) !important;
                    background-size: cover;
                    border: none !important;
                    margin-bottom: -1px;
                    .appDrawerToggle,.o_mobile_menu_toggle{
                        .ri{
                            font-size: 18px !important;
                            color: var(--header-vertical-mini-text-color) !important;
                        }
                    }
                    .o_mobile_menu_toggle{
                        padding: 0 4px !important;
                    }
                    .o_menu_systray{
                        display: flex;
                        align-items: center;
                        .o_switch_company_menu{
                            align-items: center;
                            &:last-child{
                                margin-right: 0 !important;
                            }
                        }
                    }
                    .o_MessagingMenu_toggler,.o_ActivityMenuView_dropdownToggle,.o_user_lang .dropdown-toggle,.company_selections .dropdown-toggle,.debug_activator .activate_debug,.theme_selector .theme-edit,.header_to_do_list .to_do_list,.dark_mode .dark-light-mode-button-design .label,.o_menu_systray .dropdown .dropdown-toggle{
                        width: 28px;
                        height: 28px !important;
                        line-height: 28px !important;
                        background-color: rgba(255, 255, 255,0.14) !important;
                        color: var(--header-vertical-mini-text-color) !important;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 6px !important;
                        &:hover{
                            background: rgba(255, 255, 255,0.14) !important;
                        }
                        // i,.fa,.ri{
                        //     font-size: 16px !important;
                        // }
                        .badge{
                            position: absolute !important;
                            top: 8px !important;
                            right: 0;
                            font-size: 10px;
                            font-weight: 700;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: normal;
                            letter-spacing: 0.05px;
                            background-color: #5AC89D !important;
                            color: #fff !important;
                            left: 67%;
                            min-width: 18px;
                            padding: 2px;
                            height: 17px;
                        }
                    }
                    .header_to_do_list{
                        margin-right: 8px !important;
                    }
                }
                .o_burger_menu {
                    background-color: var(--header-vertical-mini-bg-color) !important;
                    .o_burger_menu_topbar{
                        background-color: var(--header-vertical-mini-bg-color) !important;
                        color: var(--header-vertical-mini-text-color) !important;
                        border-bottom: 1px solid #e5e5e5 !important;
                        border-radius: 0;
                        margin-bottom: 0 !important;
                        @include media-breakpoint-down(lg){
                            padding-bottom: 1px !important;
                        }
                        .o_burger_menu_username{
                            font-size: 0.875rem
                        }
                        .dropdown-toggle{
                            border: none !important;
                        }
                    }
                    .o_burger_menu_topbar,.o_burger_menu_topbar .dropdown-toggle{
                        background-color: var(--header-vertical-mini-bg-color) !important;
                        color: var(--header-vertical-mini-text-color) !important;
                        &.active {
                            color: var(--header-vertical-mini-text-color) !important;
                        }
                    }
                    .o_burger_menu_content {
                        .o_user_menu_mobile {
                            .dropdown-item ,.dropdown-divider{
                                color: var(--header-vertical-mini-text-color) !important;
                                border-color: var(--header-vertical-mini-text-color) !important;
                            }
                        }
                        li{
                            color: var(--header-vertical-mini-text-color) !important;
                        }
                        ul.ps-0.mb-0 li.ps-0 .text-900.bg-transparent.fw-bold{
                            font-size: 14px !important;
                            color: var(--header-vertical-mini-text-color) !important;
                        }
                        ul.ps-0 {
                            li.py-2 {
                                color: var(--header-vertical-mini-text-color) !important;
                            }
                            li.ps-0 {
                                .py-3.bg-transparent {
                                    color: var(--header-vertical-mini-text-color) !important;
                                }
                                ul.ps-0 {
                                    li.py-2 {
                                        color: var(--header-vertical-mini-text-color) !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
       
        header + .o_action_manager{
            .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                padding-left: 1rem !important;
                padding-right: 5rem !important;
                transition: 0.2s;
                height: calc(100vh - 90px) !important;

                @include media-breakpoint-down(lg){
                    padding-left: unset !important;
                    padding-right: unset !important;
                }
            }
            > .o_action:not(.o_view_controller):not(.o_spreadsheet_dashboard_action){
                > .o_control_panel{
                    padding-left: 1rem !important;
                    padding-right: 5rem !important;
                    transition: 0.2s;
                }

                > .o_content{
                    iframe{
                        padding-left: 1rem !important;
                        padding-right: 5rem !important;
                        transition: 0.2s;
                    }
                }
            }
            .o_action.o_view_controller.o_kanban_view{
                padding: unset;
            }
        }
        @include media-breakpoint-up(lg){
            &:not(.bookmark_panel_show) {
                header + .o_action_manager{
                    .o-mail-Discuss, > .o_action.o_view_controller, .o_spreadsheet_dashboard_action{
                        padding-right: 0rem !important;
                    }
                    > .o_action:not(.o_view_controller):not(.o_spreadsheet_dashboard_action){
                        > .o_control_panel{
                            padding-right: 1rem !important;
                        }
        
                        > .o_content{
                            iframe{
                                padding-right: 1rem !important;
                            }
                        }
                    }
                    .o_action.o_view_controller.o_kanban_view{
                        padding: unset;
                    }
                }
            }
        }
    }
}

// dark mode colors
body.o_web_client.dark_mode {
    .dynamic_data {
        .backend_configurator_close {
            img {
                filter: brightness(0) invert(1);
            }
        }
    }
    .dark-light-mode-button-design {
        .bulb-on::before {
            content: "\eea8";
        }
    }
    .o_main_navbar {
        .dropdown-menu {
            background-color: var(--biz-theme-secondary-color) !important;
            color: var(--biz-theme-secondary-text-color) !important;
            box-shadow: 0px 0px 2px 0px white;
        }
        .o_mail_systray_dropdown_items {
            .o_mail_preview,.o_no_activity  {
                color: var(--biz-theme-secondary-text-color);
            }
        }
    }
}
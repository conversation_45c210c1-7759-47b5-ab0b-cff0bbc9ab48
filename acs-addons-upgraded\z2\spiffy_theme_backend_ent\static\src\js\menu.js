/** @odoo-module **/

// # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
// # See LICENSE file for full copyright and licensing details.

import { fuzzyLookup } from "@web/core/utils/search";
import { rpc } from "@web/core/network/rpc";
import { renderToElement } from "@web/core/utils/render";
import { renderToFragment } from "@web/core/utils/render";
import { ColorPallet } from "@spiffy_theme_backend/js/color_pallet";
import { isMacOS, isMobileOS } from "@web/core/browser/feature_detection";
import { listenSizeChange, SIZES, utils as uiUtils } from "@web/core/ui/ui_service";
import { NavBar } from "@web/webclient/navbar/navbar";
import { patch } from "@web/core/utils/patch";
import { session } from "@web/session";
import { useService, useBus } from '@web/core/utils/hooks';
import { registry } from "@web/core/registry";
import { loadCSS, loadJS } from "@web/core/assets";
import { useExternalListener,onMounted } from "@odoo/owl";
import { UserMenu } from "@web/webclient/user_menu/user_menu";
import { useEffect } from "@odoo/owl";
import { user } from "@web/core/user";

// const websiteSystrayRegistry = registry.category('website_systray');
// websiteSystrayRegistry.add("UserMenu", { Component: UserMenu }, { sequence: 14 });

function findNames(memo, menu) {
    if (menu.actionID) {
        memo[menu.name.trim()] = menu;
    }
    if (menu.childrenTree) {
        const innerMemo = menu.childrenTree.reduce(findNames, {});
        for (const innerKey in innerMemo) {
            memo[menu.name.trim() + " / " + innerKey] = innerMemo[innerKey];
        }
    }
    return memo;
}

var session_dict = { 'demo': 'demo' }
var methods = {}
/**
 * Responsible for invoking native methods which called from JavaScript
 *
 * @param {String} name name of action want to perform in mobile
 * @param {Object} args extra arguments for mobile
 *
 * @returns Promise Object
 */

methods['divertColor'] = function () {
    return divertColor('divert_color', session_dict);
};

patch(NavBar.prototype, {
    async setup(parent, menuData) {
        super.setup();
        var self = this
        self.menuService = useService("menu");

        this.companyService = useService("company");
        this.currentCompany = this.companyService.currentCompany;

        $(document).on('click', '.bookmark_section .dropdown-toggle', function (ev) { self._getCurrentPageName(ev) });
        $(document).on('click', '.bookmark_section .add_bookmark', function (ev) { self._saveBookmarkPage(ev) });
        $(document).on('contextmenu', '.bookmark_list .bookmark_tag', function (ev) { self._showbookmarkoptions(ev) });
        $(document).on('click', '.magnifier_section .minus', function (ev) { self._magnifierZoomOut(ev) });
        $(document).on('click', '.magnifier_section .plus', function (ev) { self._magnifierZoomIn(ev) });
        $(document).on('click', '.magnifier_section .reset', function (ev) { self._magnifierZoomReset(ev) });
        $(document).on('click', '.fullscreen_section > a.full_screen', function (ev) { self._FullScreenMode(ev) });
        $(document).on("click", ".theme_selector a", function (ev) { self._openConfigModal(ev) })
        $(document).on('click', '#dark_mod', function (ev) { self._ChangeThemeModeCLicked(ev) });
        $(document).on('click', '.pin_sidebar', function (ev) { self._ChangeSidebarBehaviour(ev) });
        // $(document).on('click', '.lang_selector', function(ev){self._GetLanguages(ev)});

        $(document).on('click', 'body:not(.top_menu_vertical_mini) .o_navbar_apps_menu .main_link', function (ev) { self._ShowCurrentMenus(ev) });
        $(document).on('click', 'body.top_menu_vertical_mini .o_navbar_apps_menu .main_link', function (ev) { self._ShowCurrentMenusNew(ev) });
        $(document).on('click', 'body.top_menu_vertical_mini .o_navbar_apps_menu .parent-menu', function (ev) { self._ShowCurrent(ev) });

        // SPIFFY MULTI TAB START
        $(document).on('click', '.o_navbar_apps_menu .child_menus', function (ev) { self._childMenuClick(ev) });
        $(document).on('click', '.o_menu_sections .o_menu_entry_lvl_2, .o_menu_sections .o_nav_entry', function (ev) { self._childMenuClick(ev) });
        $(document).on('click', '.o_navbar_apps_menu .menu-link-for-apps', function (ev) { self._AppsMenuClick(ev) });
        $(document).on('click', '.o_navbar_apps_menu .parent_menus', function (ev) { self._MenuClick(ev) });
        $(document).on('click', '.o_navbar_apps_menu .parent_main_menus', function (ev) { self._AppsMainMenuClick(ev) });        
        $(document).on('click', '.o_navbar_apps_menu .spiffy-menu-group-list', function (ev) { self._SpiffyMenuGroupList(ev) });
        $(document).on('click', '.o_navbar_apps_menu .parent-main-menu', function (ev) { self._ParentMainMenu(ev) });
        $(document).on('click', '.o_navbar_apps_menu .top_menu_horizontal', function (ev) { self._onMenuGroupClick(ev) });
        $(document).on('click', '.o_navbar_apps_menu .submenu-link', function (ev) { self._SpiffyMainGroup(ev) });
        $(document).on('click', '.o_navbar_apps_menu .spiffy-submenu-group', function (ev) { self._SpiffySubMenuGroup(ev) });
        $(document).on('click', '.multi_tab_section .multi_tab_div a', function (ev) { self._TabClicked(ev) });
        $(document).on('click', '.multi_tab_section .remove_tab', function (ev) { self._RemoveTab(ev) });
        // SPIFFY MULTI TAB END

        $(document).on('click', '.search_bar, .close-search-bar', function (ev) { self._showSearchbarModal(ev) });
        // T10246 start
        $(document).on('shown.bs.modal', '#search_bar_modal', function (ev) { self._all_apps_records_data(ev) });
        // T10246 end
        $(document).on('shown.bs.modal', '#search_bar_modal', function (ev) { self._searchModalFocus(ev) });
        $(document).on('hidden.bs.modal', '#search_bar_modal', function (ev) { self._searchModalReset(ev) });

        // T10246 start
        $(document).on('click', '.form-check-input', function (ev) { self._showSearchbarModalOption(ev) });
        // T10246 end

        $(document).on('click', '#search_bar_modal #active-menu-categories', function (ev) { self._showSearchbarMenu(ev) });
        $(document).on('click', '#search_bar_modal #active-records-global-search', function (ev) { self._showSearchbarRecord(ev) });

        $(document).on('keydown', '#searchPagesInput', function (ev) { self._searchResultsNavigate(ev) });
        $(document).on('input', '#searchPagesInput', function (ev) { self._searchMenuTimeout(ev) });
        $(document).on('click', '#searchPagesResults .autoComplete_highlighted', function (ev) { self._searchResultChosen(ev) });
        $(document).on('click', '.o_app_drawer a', function (ev) { self._OpenAppdrawer(ev) });
        $(document).on('click', '.mobile-header-toggle #mobileMenuToggleBtn', function (ev) { self._mobileHeaderToggle(ev) });
        $(document).on('click', '.o_menu_sections #mobileMenuclose', function (ev) { self._mobileHeaderClose(ev) });
        $(document).on('click', '.fav_app_drawer .fav_app_drawer_btn', function (ev) { self._OpenFavAppdrawer(ev) });
        $(document).on('click', '.header_menu_right_content_toggler .bookmark_panel_toggle', function (ev) { self._ToggleBookmarkPanel(ev) });
        $(document).on('click', '.appdrawer_section .close_fav_app_btn', function (ev) { self._CloseAppdrawer(ev) });

        $(document).on('click', '.debug_activator .activate_debug', function (ev) { self._DebugToggler(ev) });

        $(document).on("click", ".header_to_do_list .to_do_list", function (ev) { self._openToDoList(ev) });

        this._searchableMenus = {};
        var menu = this.menuService.getApps()
        for (const menu of this.menuService.getApps()) {
            Object.assign(
                this._searchableMenus,[this.menuService.getMenuAsTree(menu.id)].reduce(findNames,{}),
            );
        }

        this._search_def = false;

        // on reload get mode color
        this._getModeData();
        // on reload add backend theme class
        this.addconfiguratorclass()
        // on reload add bookmark tags in menu
        this.addbookmarktags()

        // get all apps menu data
        await this._all_apps_menu_data()

        // T10246 start
        // get records data
        this._all_apps_records_data()
        // T10246 end

        // SPIFFY MULTI TAB START - on reload add multi tabs
        this.addmultitabtags()
        // SPIFFY MULTI TAB END
        this._GetLanguages()

        // close magnifier when clicked outside the magnifer div
        $(document).on("click", function (e) {
            if (!$(e.target).closest('.magnifier_section').length) {
                $('#magnifier').collapse("hide")
            }
            if (!$(e.target).closest('header.o_navbar').length) {
                $('body.top_menu_vertical_mini #accordion .main_link.active').click()
            }
        });

        /* EVENTS FOR WINDOW FULLSCREEN WITH ESC BUTTON TRIGGER */
        document.addEventListener("fullscreenchange", function () {
            if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {
                var fullScreenBtn = $('.fullscreen_section .full_screen');
                if ($(fullScreenBtn).hasClass('fullscreen-exit')) {
                    $(fullScreenBtn).removeClass('fullscreen-exit')
                }
            }
        });
        document.addEventListener("mozfullscreenchange", function () {
            if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {
                var fullScreenBtn = $('.fullscreen_section .full_screen');
                if ($(fullScreenBtn).hasClass('fullscreen-exit')) {
                    $(fullScreenBtn).removeClass('fullscreen-exit')
                }
            }
        });
        document.addEventListener("webkitfullscreenchange", function () {
            if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {
                var fullScreenBtn = $('.fullscreen_section .full_screen');
                if ($(fullScreenBtn).hasClass('fullscreen-exit')) {
                    $(fullScreenBtn).removeClass('fullscreen-exit')
                }
            }
        });
        document.addEventListener("msfullscreenchange", function () {
            if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {
                var fullScreenBtn = $('.fullscreen_section .full_screen');
                if ($(fullScreenBtn).hasClass('fullscreen-exit')) {
                    $(fullScreenBtn).removeClass('fullscreen-exit')
                }
            }
        });

        var size = $(window).width();
        var upTo1200 = size <= 1023.98

        this.isIpad = upTo1200
        this.$search_modal_popup = $(this.root.el).find("#search_bar_modal");
        // T10246 start
        this.$search_modal_input_option = $(this.root.el).find("#search_bar_modal input");
        if (this.$search_modal_input_option == ""){
            this.$search_modal_input_option = "menu"
        }
        this.$search_modal_input_option_select = $(this.root.el).find("#search_bar_modal #active-records-global-search");
        // T10246 end
        this.$search_modal_input = $(this.root.el).find("#search_bar_modal #searchPagesInput");
        this.$search_modal_select = $(this.root.el).find("#search_bar_modal #active-menu-categories");
        this.$search_modal_results = $(this.root.el).find("#search_bar_modal #searchPagesResults");
        this.$search_modal_Noresults = $(this.root.el).find("#search_bar_modal .searchNoResult");

        var currentapp = this.menuService.getCurrentApp();
    },
    getsubMenuItemHref(payload) {
        return `/odoo/${payload.actionPath || "action-" + payload.actionID}`;
    },
    _DebugToggler: function (ev) {
        $(ev.currentTarget).toggleClass('toggle');
        if ($(ev.currentTarget).hasClass('toggle')) {
            var current_href = window.location.href;
            window.location.search = "?debug=1"
        } else {
            window.location.search = "?debug="
        }
    },

    _on_secondary_menu_click: function (menu_id, action_id) {
        this._super.apply(this, arguments);
        $('.o_menu_sections').removeClass('toggle');
        $('body').removeClass('backdrop');
    },

    _mobileHeaderToggle: function (ev) {
        var menu_brand = $('.o_main_navbar > a.o_menu_brand').clone()
        $('.o_menu_sections > a.o_menu_brand').remove()
        $('#mobileMenuclose').before(menu_brand)
        $('.o_menu_sections').addClass('toggle');
        $('body').addClass('backdrop');
    },
    _mobileHeaderClose: function (ev) {
        $('.o_menu_sections').removeClass('toggle');
        $('body').removeClass('backdrop');
    },
    _OpenAppdrawer: function (ev) {
        this._AppdrawerIcons()

        $('.o_main_navbar').toggleClass('appdrawer-toggle')
        // $(ev.currentTarget).toggleClass('toggle')
        $('.appdrawer_section').toggleClass('toggle')

        if ($(".appdrawer_section").hasClass('toggle')) {
            var size = $(window).width();
            if (size > 992) {
                setTimeout(() => $(".appdrawer_section input").focus(), 100);
            }
        } else {
            $(".appdrawer_section input").val("");
            $(".appdrawer_section #search_result").empty();
            $('#searched_main_apps').empty().addClass('d-none').removeClass('d-flex');
            $('.appdrawer_section .apps-list .row').removeClass('d-none');
        }
        this._all_apps_menu_data()
    },
    _OpenFavAppdrawer: function (ev) {
        this._OpenAppdrawer(ev)
        $('.appdrawer_section').toggleClass('show_favourite_apps')
        $('.apps-list').addClass('d-none')
        $('.favourite_apps').removeClass('d-none')
    },
    _ToggleBookmarkPanel: function (ev) {
        $('body').toggleClass('bookmark_panel_show')
        if ($('body').hasClass('bookmark_panel_show')) {
            var bookmark_panel = true
        } else {
            var bookmark_panel = false
        }
        rpc('/update/bookmark/panel/show', {
            'bookmark_panel': bookmark_panel,
        })
    },

    _CloseAppdrawer: function (ev) {
        $('.o_main_navbar').removeClass('appdrawer-toggle')
        $('.appdrawer_section').removeClass('show_favourite_apps')
        $('.apps-list').removeClass('d-none')
        $('.favourite_apps').addClass('d-none')
        $('.appdrawer_section').removeClass('toggle')
        $(".appdrawer_section input").val("");
        $(".appdrawer_section #search_result").empty();
        $('#searched_main_apps').empty().addClass('d-none').removeClass('d-flex');
        $('.appdrawer_section .apps-list .row').removeClass('d-none');
        var $target = $(ev.currentTarget).siblings('.submenu-group');
    
        if ($target.hasClass('active')) {
            $target.removeClass('active').hide();
        }
        this._all_apps_menu_data()
    },

    _ShowCurrentMenus: function (ev) {
        $(ev.target).parent().parent().find('ul').removeClass('show')
        $(ev.target).parent().parent().find('a.main_link').removeClass('active')
        $(ev.target).parent().find('ul').addClass('show')
        $(ev.target).addClass('active')

        // SPIFFY MULTI TAB START
        if (ev.shiftKey) {
            this._createMultiTab(ev)
            ev.preventDefault()
        } else {
        }
        // SPIFFY MULTI TAB END
        this._all_apps_menu_data()
    },
    _ShowCurrentMenusNew: function (ev) {
        $('.header-sub-menus .collapse').addClass('show');
        $('.header-sub-menus-group .collapse').addClass('show');
        var $blurOverlay = $('#blur-overlay');
        var $backgroundOverlay = $('.top-menu-vertical-mini');
        if ($(ev.target).hasClass('dropdown-btn')) {
            ev.preventDefault();
        }
        if (ev.shiftKey) {
            this._createMultiTab(ev)
            ev.preventDefault()
        } else {
        }
        if ($('body').hasClass('top_menu_vertical_mini')) {
            if ($(ev.currentTarget).hasClass('parent-menu')) {
                if ($(ev.currentTarget).hasClass('active')) {
                    $('.header-sub-menus').removeClass('show').hide();
                    $blurOverlay.removeClass('background-blur').show();  
                    $backgroundOverlay.removeClass('header-background').hide();   
                } else {
                    $('.header-sub-menus').addClass('show').show();
                    $blurOverlay.addClass('background-blur').show();  
                    $(ev.currentTarget).prev($backgroundOverlay).addClass('header-background').show();  
                }
            }
            
        }
        if ($(ev.target).hasClass('active')) {
            $(ev.target).removeClass('active')
            if ($(ev.target).next().hasClass('header-sub-menus') && $(ev.target).next().hasClass('show')) {
                $(ev.target).next().removeClass('show')
            }
            if ($(ev.target).next().hasClass('header-sub-menus-group') && $(ev.target).next().hasClass('show')) {
                $(ev.target).next().removeClass('show')
            }
            if ($(ev.target).next().hasClass('submenu-group') && $(ev.target).next().hasClass('show')) {
                $(ev.target).next().removeClass('show')
                $(ev.currentTarget).prev($backgroundOverlay).removeClass('header-background').show();  
            }
            if ($(ev.target).next().hasClass('menu-link-for-apps') && $(ev.target).next().hasClass('show')) {
                $(ev.target).next().removeClass('show')
            }
        } else {
            $(ev.target).parent().parent().find('ul').removeClass('show')
            $(ev.target).parent().parent().find('a.main_link').removeClass('active')
            $(ev.target).parent().find('ul').addClass('show')
            $(ev.target).addClass('active')
        }
        this._all_apps_menu_data()
    },

    _ShowCurrent: function (ev) {
        var $backgroundOverlay = $('.top-menu-vertical-mini');
        $backgroundOverlay.removeClass('header-background').hide();  
        this._all_apps_menu_data()
    },
    
    _all_apps_menu_data: async function () {
        var menu_data = this.menuService.getApps()
        var self = this;
        var rec_ids = []
        menu_data.map(app => rec_ids.push(app.id))
        await rpc('/get/irmenu/icondata', {
            'menu_ids':rec_ids,
        }).then(function (rec) {
            $.each(menu_data, function (key, value) {
                var target_tag = '.o_navbar_apps_menu a.main_link[data-menu=' + value.id + ']'
                var $tagtarget = $(self.root.el).find(target_tag)
                $tagtarget.find('.app_icon').empty()
                var current_record = rec[value.id][0]
                var spiffy_app_group = rec["spiffy_app_group"]
                value.id = current_record.id
                value.use_icon = current_record.use_icon
                value.icon_class_name = current_record.icon_class_name
                value.icon_img = current_record.icon_img
                value.spiffy_app_group_id = current_record.spiffy_app_group_id
                value.spiffy_app_group = spiffy_app_group
                value.app_menu_list = JSON.parse(current_record.app_menu_list)

                if ($('body').hasClass('top_menu_horizontal')) {
                    const horizontalMenuContainer = $(".spiffy-app-group");
                    const horizontalMenuTemplate = $(renderToElement("spiffy_theme_backend.AppMenuGroup", {
                        group_info: value.spiffy_app_group,
                        menu_info: menu_data,
                        getMenuItemHref: self.getMenuItemHref
                    }));
                    horizontalMenuContainer.empty().append(horizontalMenuTemplate);
                } else {
                    const verticalMenuContainer = $(".all-apps-menus");

                    if (!verticalMenuContainer.data('menu-rendered')) {
                        const verticalMenuTemplate = $(renderToElement("spiffy_theme_backend.AllAppsMenus", {
                            group_info: value.spiffy_app_group,
                            menu_info: menu_data,
                            getMenuItemHref: self.getMenuItemHref,
                            menuService: self.menuService,
                            app_menu_list: value.app_menu_list,
                        
                        }));                
                        verticalMenuContainer.empty().append(verticalMenuTemplate);
                        verticalMenuContainer.data('menu-rendered', true);
                    }
                }


                if (current_record.use_icon) {
                    if (current_record.icon_class_name) {
                        var icon_image = "<span class='ri " + current_record.icon_class_name + "'/>"
                    } else if (current_record.icon_img) {
                        var icon_image = "<img class='img img-fluid' src='/web/image/ir.ui.menu/" + current_record.id + "/icon_img' />"
                    } else if (current_record.web_icon != false) {
                        var icon_data = current_record.web_icon.split('/icon.')
                        if (icon_data[1] == 'svg') {
                            var web_svg_icon = current_record.web_icon.replace(',', '/')
                            var icon_image = "<img class='img img-fluid' src='" + web_svg_icon + "' />"
                        } else {
                            var icon_image = "<img class='img img-fluid' src='data:image/" + icon_data[1] + ";base64," + current_record.web_icon_data + "' />"
                        }
                    } else {
                        var icon_image = "<img class='img img-fluid' src='/spiffy_theme_backend/static/description/bizople-icon.png' />"
                    }
                    $tagtarget.find('.app_icon').append($(icon_image))
                } else {
                    if (current_record.icon_img) {
                        var icon_image = "<img class='img img-fluid' src='/web/image/ir.ui.menu/" + current_record.id + "/icon_img' />"
                    } else if (current_record.web_icon != false) {
                        var icon_data = current_record.web_icon.split('/icon.')
                        if (icon_data[1] == 'svg') {
                            var web_svg_icon = current_record.web_icon.replace(',', '/')
                            var icon_image = "<img class='img img-fluid' src='" + web_svg_icon + "' />"
                        } else {
                            var icon_image = "<img class='img img-fluid' src='data:image/" + icon_data[1] + ";base64," + current_record.web_icon_data + "' />"
                        }
                    } else {
                        var icon_image = "<img class='img img-fluid' src='/spiffy_theme_backend/static/description/bizople-icon.png' />"
                    }
                    $tagtarget.find('.app_icon').append($(icon_image))
                }
            });
        })
    },

    _all_apps_records_data : function () {
        var self = this;
        rpc('/get/records/global/search', {
        }).then(function (rec) {
            // Check if the response contains records
            if (rec) {
                var records = rec;
                // Clear existing options
                $(self.root.el).find("#search_bar_modal #active-records-global-search").empty();
                // Iterate over the fetched records
                records.forEach(function (record) {
                    // Create a new option element
                    var option = $('<option></option>')
                        .attr('id', record['id'])
                        .attr('value', record['model_name'])
                        .attr('name', record['name'])
                        .text(record['name']);
                    // Append the new option element to the select element
                    $(self.root.el).find("#search_bar_modal #active-records-global-search").append(option);
                });
            }
        }).catch(error => {
                console.error('Error fetching global search records:', error);
        });
    },

    // SPIFFY MULTI TAB START
    _childMenuClick: function (ev) {
        ev.preventDefault();
        var menu = this.menuService.getMenu($(ev.target).data('menu'))
        var $blurOverlay = $('#blur-overlay');
        var $backgroundOverlay = $('.top-menu-vertical-mini');
        $backgroundOverlay.removeClass('header-background').hide(); 
        if (menu) {
            this.onNavBarDropdownItemSelection(menu)
        }
        if ($('body').hasClass('top_menu_vertical_mini')) {
            if ($(ev.target).parents('.header-sub-menus').hasClass('show')) {
                $(ev.target).parents('.header-sub-menus').removeClass('show')
                $(ev.target).parents('.header-sub-menus').prev().removeClass('active')
                $blurOverlay.removeClass('background-blur').hide(); 
            }
            if ($(ev.target).parents('.header-sub-menus-group').hasClass('show')) {
                $(ev.target).parents('.header-sub-menus-group').removeClass('show')
                $(ev.target).parents('.header-sub-menus-group').prev().removeClass('active')
                $blurOverlay.removeClass('background-blur').hide(); 
            }
            if ($(ev.target).parents('.submenu-group').hasClass('show')) {
                $(ev.target).parents('.submenu-group').removeClass('show')
                $(ev.target).parents('.submenu-group').next().removeClass('active')
            }
        }
        else {
            if (ev.shiftKey) {
                this._createMultiTab(ev)
                ev.preventDefault()
            } else {
            }
        }
    },

    _AppsMenuClick: function (ev) {
        ev.preventDefault();
        var $blurOverlay = $('#blur-overlay');
        var $backgroundOverlay = $('.top-menu-vertical-mini');
        $backgroundOverlay.removeClass('header-background').hide(); 
        if ($('body').hasClass('top_menu_vertical_mini')) {
            if ($(ev.currentTarget).hasClass('active')) {
                $('.submenu-group').css({
                    'overflow-y': 'auto',
                    'overflow-x': 'hidden'
                });
                $(ev.currentTarget).closest('li').find('.header-sub-menus-group').removeClass('show').hide();
                $blurOverlay.addClass('background-blur').show();  
                $(ev.currentTarget).prev($backgroundOverlay).addClass('header-background').show();  
            } else {
                $(ev.currentTarget).prev($backgroundOverlay).removeClass('header-background').show();  
                $(ev.currentTarget).closest('ul').find('.submenu-group').removeClass('show').hide();
                $blurOverlay.removeClass('background-blur').hide(); 
                $blurOverlay.removeClass('header-background').hide(); 

                $(ev.currentTarget).removeClass('active')
            }
            
        }
        if ($('body').hasClass('top_menu_vertical')){
            if ($(ev.currentTarget).hasClass('active')) {
                $(ev.currentTarget).closest('li').find('.header-sub-menus-group').removeClass('show').hide();
            } else {
                $(ev.currentTarget).closest('li').find('.header-sub-menus-group').addClass('show').show();
            }
            
        }
        if ($('body').hasClass('top_menu_vertical_mini_2')) {
            if ($(ev.currentTarget).hasClass('active')) {
                $(ev.currentTarget).closest('li').find('.header-sub-menus-group').removeClass('show').hide();
            } else {
                $(ev.currentTarget).closest('li').find('.header-sub-menus-group').addClass('show').show();
            }
            
        }
    },

    _MenuClick: function (ev) {
        ev.preventDefault();
        var $blurOverlay = $('#blur-overlay');
        if ($('body').hasClass('top_menu_vertical_mini')) {
            if ($(ev.target).parents('.submenu-group').hasClass('show')) {
                $(ev.target).parents('.submenu-group').removeClass('show')
                $(ev.target).parents('.submenu-group').next().removeClass('active')
                $blurOverlay.removeClass('background-blur').hide(); 
            }
        }
        if ($('body').hasClass('top_menu_vertical')) {
            if ($(ev.target).parents('.submenu-group').hasClass('show')) {
                $(ev.target).parents('.submenu-group').removeClass('show')
                $(ev.target).parents('.submenu-group').next().removeClass('active')
            }
        }
        var targetUrl = $(ev.currentTarget).attr('href');
        if (targetUrl) {
            window.location.href = targetUrl;
        }
    },

    _SpiffyMenuGroupList: function (ev) {
        ev.preventDefault();
        var $blurOverlay = $('#blur-overlay');
        var $backgroundOverlay = $('.top-menu-vertical-mini');

        if ($('body').hasClass('top_menu_vertical_mini')) {
            if ($(ev.target).hasClass('background-blur')) {
                $('.submenu-group').removeClass('show').hide();
                $('.header-sub-menus').removeClass('show').hide();
                $('.menu-link-for-apps').removeClass('active').hide();
                $('.parent-menu').removeClass('active').hide();
                $blurOverlay.removeClass('background-blur').hide(); 
                ($backgroundOverlay).removeClass('header-background').hide();  
            }else{
                $blurOverlay.removeClass('background-blur').hide(); 
            }
        }
    },

    _ParentMainMenu: function (ev) {
        var $blurOverlay = $('#blur-overlay');
        if ($('body').hasClass('top_menu_vertical_mini')) {
            
            if ($(ev.currentTarget).hasClass('parent-main-menu')) {
                $blurOverlay.removeClass('background-blur').show();  
            } else {
                $blurOverlay.removeClass('background-blur').hide();  
            }
            
        }
    },


    _AppsMainMenuClick: function (ev) {
        var $target = $(ev.currentTarget);
        var $parentLi = $target.closest('li');
        var $submenu = $parentLi.find('.header-sub-menus-group');
        var $backgroundOverlay = $('.top-menu-vertical-mini');

        if ($('body').hasClass('top_menu_vertical_mini')) {
            if ($('parent_main_menus').hasClass('active')) {
                $('parent_main_menus').removeClass('active');
            }else{
                $('parent_main_menus').addClass('active');
            }
            
            if ($target.hasClass('active')) {
                $('.submenu-group').css({
                    'overflow': 'unset'
                });
                $target.closest("ul").closest("li").find($backgroundOverlay).addClass('header-background').show(); 
            }else{
                $backgroundOverlay.remove('header-background').hide(); 
                $('.submenu-group').css({
                    'overflow-y': 'auto',
                    'overflow-x': 'hidden'
                });
            }
        }
        if ($('body').hasClass('top_menu_vertical')) {
            $('.parent_main_menus').removeClass('active');
            $('.header-sub-menus-group').removeClass('show').css('display', 'none');
            $target.addClass('active');
            $submenu.addClass('show').css('display', '');
        }
        if ($('body').hasClass('top_menu_vertical_mini_2')) {
            $('.parent_main_menus').removeClass('active');
            $('.header-sub-menus-group').removeClass('show').css('display', 'none');
            $target.addClass('active');
            $submenu.addClass('show').css('display', '');
        }
    },

    _onMenuGroupClick: function (ev) {
        ev.preventDefault();
        
        if ($('body').hasClass('top_menu_horizontal')) {
            var $target = $(ev.currentTarget).siblings('.spiffy-submenu-group');
            
            if ($target.hasClass('active')) {
                $target.removeClass('active').hide();
            } else {
                $('.spiffy-submenu-group').not($target).removeClass('active').hide();
                $target.addClass('active').show();
            }
        }
        
    },
        
    _SpiffyMainGroup: function (ev) {
        if ($('body').hasClass('top_menu_horizontal')) {
            // $('.appdrawer_section').removeClass('toggle');
            $('.submenu-group').addClass('d-none');
            $('.submenu-group').removeClass('active');
        }
    },
    _SpiffySubMenuGroup: function (ev) {
        if ($('body').hasClass('top_menu_horizontal')) {
            $('.spiffy-submenu-group').addClass('d-none').removeClass('active');
        }
    },


    _createMultiTab: function (ev) {
        var tab_name = $(ev.target).find('.app_name').text() || $(ev.target).text()
        var url = $(ev.target).attr('href')
        var actionId = $(ev.target).data('action-id')
        var menuId = $(ev.target).data('menu')
        var menu_xmlid = $(ev.target).data('menu-xmlid')
        if (menu_xmlid) {
            var menu_xmlid = menu_xmlid.split('.')[0]
        }
        var self = this
        localStorage.setItem('LastCreatedTab', actionId)

        rpc('/add/mutli/tab', {
            'name': tab_name,
            'url': url,
            'actionId': actionId,
            'menuId': menuId,
            'menu_xmlid': menu_xmlid,
        }).then(function (rec) {
            self.addmultitabtags(ev)
        });
    },

    addmultitabtags: function (ev) {
        var self = this
        rpc('/get/mutli/tab', {}).then(function (rec) {
            if (rec) {
                $('.multi_tab_section').empty()
                $.each(rec, function (key, value) {
                    var tab_tag = '<div class="d-flex justify-content-between multi_tab_div"><a href="' + value.url + '"' + ' class="flex-fill" data-xml-id="' + value.menu_xmlid + '" data-menu="' + value.menuId + '" data-action-id="' + value.actionId + '" multi_tab_id="' + value.id + '" multi_tab_name="' + value.name + '"><span>' + value.name + '</span></a><span class="remove_tab ml-4">X</span></div>'
                    $('.multi_tab_section').append(tab_tag)
                })
                var SpiffystoredActionId = sessionStorage.getItem("spiffy_current_action_id");
                var SpiffystoredAction = sessionStorage.getItem("spiffy_current_action");

                if (SpiffystoredActionId) {
                    var TabDiv = $('.multi_tab_section .multi_tab_div');
                    var ActiveMenu = TabDiv.find('a[data-action-id="' + SpiffystoredActionId + '"]');
                    ActiveMenu.parent().addClass('tab_active')
                }

                if (ev) {
                    var actionId = $(ev.target).data('action-id')
                    var menu_xmlid = $(ev.target).attr('data-menu-xmlid')
                    var menu_xmlid = menu_xmlid.split('.')[0]

                    if (localStorage.getItem('LastCreatedTab')) {
                        var target = '.multi_tab_section .multi_tab_div a[data-action-id="' + localStorage.getItem('LastCreatedTab') + '"]'
                        $(target).parent().addClass('tab_active')
                        $(target)[0].click()
                        localStorage.removeItem('LastCreatedTab')
                    } else {
                        var target = '.multi_tab_section .multi_tab_div a[data-xml-id="' + menu_xmlid + '"]'
                        $(target).parent().addClass('tab_active')
                        $(target)[0].click()
                    }
                }
                $('body').addClass("multi_tab_enabled");
            } else {
                $('body').removeClass("multi_tab_enabled");
            }
        });
    },

    _RemoveTab: function (ev) {
        var self = this
        var multi_tab_id = $(ev.target).parent().find('a').attr('multi_tab_id')
        rpc('/remove/multi/tab', {
            'multi_tab_id': multi_tab_id,
        }).then(function (rec) {
            if (rec) {
                if (rec['removeTab']) {
                    $(ev.target).parent().remove()
                    var FirstTab = $('.multi_tab_section').find('.multi_tab_div:first-child')
                    if (FirstTab.length) {
                        $(FirstTab).find('a')[0].click()
                        $(FirstTab).addClass('tab_active')
                    }
                }
                if (rec['multi_tab_count'] == 0) {
                    $('body').removeClass("multi_tab_enabled");
                }
            }
        });
    },
    _TabClicked: function (ev) {
        localStorage.setItem("TabClick", true);
        localStorage.setItem("TabClickTilteUpdate", true);
        if ($(ev.target).data('action-id')) {
            $('.multi_tab_section').find('.tab_active').removeClass('tab_active');
            $(ev.target).parent().addClass('tab_active')
        }
    },
    // SPIFFY MULTI TAB END

    change_menu_section: function (primary_menu_id) {
        this._super.apply(this, arguments);
        var target_tag = '.o_navbar_apps_menu a.main_link[data-menu=' + primary_menu_id + ']'
        var $tagtarget = $(target_tag)
        $tagtarget.parent().find('ul').addClass('show')
        $tagtarget.addClass('active')
    },
    _getModeData: function () {
        var self = this
        rpc('/get/dark/mode/data').then(function (rec) {
            var dark_mode = rec
            self._ChangeThemeMode(dark_mode)
        })
    },
    addconfiguratorclass: function () {
        rpc('/get/model/record').then(function (rec) {
            $("body").addClass(rec.record_dict[0].separator);
            $("body").addClass(rec.record_dict[0].tab);
            $("body").addClass(rec.record_dict[0].checkbox);
            $("body").addClass(rec.record_dict[0].button);
            $("body").addClass(rec.record_dict[0].radio);
            $("body").addClass(rec.record_dict[0].popup);
            $("body").addClass(rec.record_dict[0].font_size);
            $("body").addClass(rec.record_dict[0].login_page_style);
            $("body").addClass(rec.record_dict[0].chatter_position);
            $("body").addClass(rec.record_dict[0].list_view_density);
            $("body").addClass(rec.record_dict[0].input_style);

            // Load Font size file based on selected option
            if (rec.record_dict[0].font_size) {
                loadCSS(`/spiffy_theme_backend/static/src/scss/font_sizes/${rec.record_dict[0].font_size}.css`);
            }

            var size = $(window).width();
            if (size <= 992) {
                $("body").addClass('top_menu_horizontal');
                $("html").attr('data-menu-position', 'top_menu_horizontal')
                $("html").attr('data-view-type', 'mobile')
                if (rec.record_dict[0].top_menu_position == 'top_menu_vertical_mini') {
                    $("body").addClass('top_menu_vertical_mini_mobile');
                }
            } else {
                $("body").addClass(rec.record_dict[0].top_menu_position);
                $("html").attr('data-menu-position', rec.record_dict[0].top_menu_position)
                $("html").attr('data-view-type', 'desktop')
            }

            $("body").addClass(rec.record_dict[0].theme_style);
            $("body").addClass(rec.record_dict[0].shape_style);
            $("body").addClass(rec.record_dict[0].loader_style);
            $("body").addClass('font_family_' + rec.record_dict[0].font_family);

            $("html").attr('data-font-size', rec.record_dict[0].font_size)
            $("html").attr('data-theme-style', rec.record_dict[0].theme_style)

            if (rec.record_dict[0].use_custom_drawer_color) {
                $("body").addClass('custom_drawer_color');
            } else {
                $("body").addClass(rec.record_dict[0].drawer_color_pallet);
            }

            if (rec.record_dict[0].attachment_in_tree_view) {
                $("body").addClass("show_attachment");
            }
            if (rec.darkmode) {
                $("body").addClass(rec.darkmode);
            }
            if (rec.bookmark_panel) {
                $("body").addClass("bookmark_panel_show");
            }
            if (rec.prevent_auto_save) {
                $("body").addClass(rec.prevent_auto_save);
            }
            if (!rec.todo_list_enable) {
                // $("body").addClass(rec.todo_list_enable);
                $('.header_to_do_list').remove()
            }
            if (rec.pinned_sidebar) {
                $("body").addClass(rec.pinned_sidebar);
                $("header .pin_sidebar").addClass('pinned');
            }
            if (rec.record_dict[0].tree_form_split_view) {
                if (size >= 992) {
                    $("body").addClass("tree_form_split_view");
                }
            }
            if (rec.record_dict[0].list_view_sticky_header) {
                $("body").addClass("list_view_sticky_header");
            }
            if (rec.record_dict[0].apply_menu_shape_style) {
                $("body").addClass("apply_menu_shape_style");
            }
            if (rec.record_dict[0].apply_light_bg_img) {
                if (rec.record_dict[0].light_bg_image) {
                    $(".appdrawer_section").attr("style", "background-image: url('/web/image/backend.config/" + rec.record_dict[0].id + "/light_bg_image')");
                }
            }

            if (rec.record_dict[0].top_menu_position == "top_menu_vertical_mini_2") {
                if (rec.record_dict[0].top_menu_bg_vertical_mini_2 == "top_menu_vertical_bg1") {
                    if (rec.record_dict[0].vertical_mini_bg_image_one) {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_one') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_one') !important; background-size: cover !important; background-position: center !important;");
                    } else {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg1.jpg') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg1.jpg') !important; background-size: cover !important; background-position: center !important;");
                    }
                }
                
                if (rec.record_dict[0].top_menu_bg_vertical_mini_2 == "top_menu_vertical_bg2") {
                    if (rec.record_dict[0].vertical_mini_bg_image_two) {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_two') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_two') !important; background-size: cover !important; background-position: center !important;");
                    } else {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg2.jpg') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg2.jpg') !important; background-size: cover !important; background-position: center !important;");
                    }
                }
                
                if (rec.record_dict[0].top_menu_bg_vertical_mini_2 == "top_menu_vertical_bg3") {
                    if (rec.record_dict[0].vertical_mini_bg_image_three) {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_three') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_three') !important; background-size: cover !important; background-position: center !important;");
                    } else {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg3.jpg') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg3.jpg') !important; background-size: cover !important; background-position: center !important;");
                    }
                }
                
                if (rec.record_dict[0].top_menu_bg_vertical_mini_2 == "top_menu_vertical_bg4") {
                    if (rec.record_dict[0].vertical_mini_bg_image_four) {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_four') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/web/image/backend.config/" + rec.record_dict[0].id + "/vertical_mini_bg_image_four') !important; background-size: cover !important; background-position: center !important;");
                    } else {
                        $(".o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg4.jpg') !important; background-size: cover !important; background-position: center !important;");
                        
                        $(".top_menu_vertical_mini_2.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background: linear-gradient(to bottom, rgba(27, 27, 27, 1) 0%, rgba(27, 27, 27, 0.7) 60%, rgba(27, 27, 27, 0) 100%), url('/spiffy_theme_backend/static/description/top-menu-v2-bg4.jpg') !important; background-size: cover !important; background-position: center !important;");
                    }
                }
            }
            
            if (rec.record_dict[0].menu_bg_image) {
                $(".new_systray").attr("style", "background-image: url('/web/image/backend.config/" + rec.record_dict[0].id + "/menu_bg_image')");
                $(".top_menu_horizontal.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background-image: url('/web/image/backend.config/" + rec.record_dict[0].id + "/menu_bg_image')");
            } else {
                $(".new_systray").attr("style", "background-image: url('/spiffy_theme_backend/static/description/header_vertical_mini.svg')");
                $(".top_menu_horizontal.top_menu_vertical_mini_mobile .o_main_navbar").attr("style", "background-image: url('/spiffy_theme_backend/static/description/header_vertical_mini.svg')");
            }

            if (!rec.show_edit_mode) {
                $('.theme_selector').remove()
            }
            if (!rec.is_admin) {
                $('.debug_activator').remove()
            }
            var pallet_name = rec.record_dict[0].color_pallet
            var apply_color = new ColorPallet(this)
            if (rec.record_dict[0].use_custom_colors) {
                apply_color['custom_color_pallet'](rec.record_dict[0])

            } else {
                apply_color[pallet_name]()
            }

            var app_drawer_pallet_name = rec.record_dict[0].drawer_color_pallet
            var app_drawer_apply_color = new ColorPallet(this)
            if (rec.record_dict[0].use_custom_drawer_color) {
                app_drawer_apply_color['custom_app_drawer_color_pallet'](rec.record_dict[0])
            }

            var header_apply_color = new ColorPallet(this)
            header_apply_color['header_color_pallet'](rec.record_dict[0])

            var menu_shape_apply_color = new ColorPallet(this)
            menu_shape_apply_color['menu_shape_color_pallet'](rec.record_dict[0])

            $('body').attr('headerMode', 'visible');
            // $('.o_main_navbar').removeClass('d-none');
        })
    },
    addbookmarktags: function() {
        rpc('/get/bookmark/link', {}).then(function(rec) {
            $('.bookmark_list').empty()
            $.each(rec, function(key, value) {
                let urlParams = value.url.endsWith('?') ? value.url.slice(0, -1) : value.url;

                // var app_actionPath = `/odoo/${data.actionPath || "action-" + data.actionID}`;
                // href="#id=${Id}&amp;menu_id=${menu_id}&amp;action=${actionId}&amp;model=${model}&amp;view_type=${view_type}&amp;"
                var anchor_tag = `
                    <div class="d-inline-block bookmark_div">
                        <a role="menuitem"
                            href="${value.url}"
                            class="bookmark_tag btn-light btn demo_btn d-block o_app text-center"
                            bookmark-id="${value.id}"
                            bookmark-name="${value.name}"
                            title="${value.name}">
                            ${value.title}
                        </a>
                    </div>`;

                $('.bookmark_list').append(anchor_tag);
            })
        });
    },
    _getCurrentPageName: function () {
        var breadcrumbs = $('.o_control_panel ol.breadcrumb li')
        var bookmark_name = ""
        $(breadcrumbs).each(function (index) {
            if (index > 0) {
                bookmark_name = bookmark_name + ' | ' + $(this).text()
            } else {
                bookmark_name = $(this).text()
            }
        });

        $('input#bookmark_page_name').val(bookmark_name)
    },
    _saveBookmarkPage: function () {
        var self = this
        var pathname = window.location.pathname
        var hash = window.location.hash
        var url = pathname + '?' + hash
        var name = $('input#bookmark_page_name').val()
        var title = $('input#bookmark_page_name').val().substr(0, 2)
        rpc('/add/bookmark/link', {
            'name': name,
            'url': url,
            'title': title,
        }).then(function (rec) {
            self.addbookmarktags()
        });
    },
    _showbookmarkoptions: function (ev) {
        var self = this
        ev.preventDefault();
        var bookmark_id = $(ev.target).attr('bookmark-id')
        var bookmark_name = $(ev.target).attr('bookmark-name')
        $('.bookmark_list .bookmark_options').remove()
        $('.bookmark_list .bookmark_rename_section').remove()
        var bookmark_options = $(renderToElement("BookmarkOptions", {
            bookmark_id: bookmark_id,
        }))
        $(ev.target).parent().append(bookmark_options)
        $('.bookmark_list .rename_bookmark').on("click", function (e) {
            self._RenameBookmark(ev.target, bookmark_id, bookmark_name);
        });

        $('.bookmark_list .remove_bookmark').on("click", function (e) {
            self._RemoveBookmark(bookmark_id);
        });
        //  document.addEventListener("click", function(){
        //      $('.bookmark_list .bookmark_options').remove()
        //  });
        //  useExternalListener(document, "click", () => {
        //     $('.bookmark_list .bookmark_options').remove()
        //  });
        ev.preventDefault();
    },
    _RenameBookmark: function (elem, bookmark_id, bookmark_name) {
        var self = this
        var bookmark_rename = $(renderToElement("BookmarkRename", {
            bookmark_id: bookmark_id,
            bookmark_name: bookmark_name,
        }))
        $(elem).parent().append(bookmark_rename)

        $('.bookmark_list .bookmark_rename_cancel').on("click", function (e) {
            $('.bookmark_list .bookmark_rename_section').remove()
        });
        $('.bookmark_list .bookmark_rename').on("click", function (e) {
            var new_bookmark_name = $('input#bookmark_rename').val()
            self._UpdateBookmark(bookmark_id, new_bookmark_name);
        });
    },
    _RemoveBookmark: function (bookmark_id) {
        var self = this
        rpc('/remove/bookmark/link', {
            'bookmark_id': bookmark_id,
        }).then(function (rec) {
            self.addbookmarktags()
        });
    },
    _UpdateBookmark: function (bookmark_id, bookmark_name) {
        var self = this
        var title = bookmark_name.substr(0, 2)
        rpc('/update/bookmark/link', {
            'bookmark_id': bookmark_id,
            'bookmark_name': bookmark_name,
            'bookmark_title': title,
        }).then(function (rec) {
            self.addbookmarktags()
        });
    },
    _magnifierZoomOut: function () {
        var current_zoom = parseInt($('.zoom_value').text())
        var current_zoom = current_zoom - 10
        if (current_zoom > 20) {
            $('.zoom_value').text(current_zoom)
            var scale_value = current_zoom / 100
            var width_value = ((100 / current_zoom) * 100).toFixed(4)
            if ($('.o_content > div').length > 1) {
                var target = $('.o_action_manager > .o_view_controller > .o_content')
            } else {
                var target = $('.o_content > div')
            }
            $(target).css({
                'width': width_value + '%',
                'transform-origin': 'left top',
                'transform': 'scale(' + scale_value + ')',
            })
        }
    },
    _magnifierZoomIn: function () {
        var current_zoom = parseInt($('.zoom_value').text())
        var current_zoom = current_zoom + 10
        if (current_zoom < 210) {
            $('.zoom_value').text(current_zoom)
            var scale_value = current_zoom / 100
            var width_value = ((100 / current_zoom) * 100).toFixed(4)
            if ($('.o_content > div').length > 1) {
                var target = $('.o_action_manager > .o_view_controller > .o_content')
            } else {
                var target = $('.o_content > div')
            }
            $(target).css({
                'width': width_value + '%',
                'transform-origin': 'left top',
                'transform': 'scale(' + scale_value + ')',
            })
        }
    },
    _magnifierZoomReset: function () {
        $('.zoom_value').text('100')
        if ($('.o_content > div').length > 1) {
            var target = $('.o_action_manager > .o_view_controller > .o_content')
        } else {
            var target = $('.o_content > div')
        }
        $(target).css({
            'width': '100%',
            'transform-origin': 'left top',
            'transform': 'scale(1)',
        })
    },
    _FullScreenMode: function (ev) {
        var elem = document.documentElement;
        if ($(ev.currentTarget).hasClass('fullscreen-exit')) {
            if (document.exitFullscreen) {
                document.exitFullscreen();
                $(ev.currentTarget).removeClass('fullscreen-exit')
            } else if (document.webkitExitFullscreen) { /* Safari */
                document.webkitExitFullscreen();
                $(ev.currentTarget).removeClass('fullscreen-exit')
            } else if (document.msExitFullscreen) { /* IE11 */
                document.msExitFullscreen();
                $(ev.currentTarget).removeClass('fullscreen-exit')
            }
        } else {
            if (elem.requestFullscreen) {
                elem.requestFullscreen();
                $(ev.currentTarget).addClass('fullscreen-exit')
            } else if (elem.webkitRequestFullscreen) { /* Safari */
                elem.webkitRequestFullscreen();
                $(ev.currentTarget).addClass('fullscreen-exit')
            } else if (elem.msRequestFullscreen) { /* IE11 */
                elem.msRequestFullscreen();
                $(ev.currentTarget).addClass('fullscreen-exit')
            }
        }
    },
    _openConfigModal: function () {
        var self = this
        self.showeditmodal();
        $('.dynamic_data').toggleClass('visible')
        $('body.o_web_client').toggleClass('backdrop')
    },
    showeditmodal: function (ev) {
        $.get('/color/pallet/data/', {}).then(function (data) {

            $(".dynamic_data").empty()
            $(".dynamic_data").append(data)

            $('#theme_color_pallets #use_custom_color_config').unbind().on('change', function (e) {
                if ($(this).prop("checked") == true) {
                    $('#theme_color_pallets .custom_color_config').removeClass('d-none')
                    $('#theme_color_pallets .predefined_color_pallets').addClass('d-none')
                } else {
                    $('#theme_color_pallets .custom_color_config').addClass('d-none')
                    $('#theme_color_pallets .predefined_color_pallets').removeClass('d-none')
                }
            });


            $('#app_drawer #use_custom_drawer_color').unbind().on('change', function (e) {
                if ($(this).prop("checked") == true) {
                    $('#app_drawer .custom_color_config').removeClass('d-none')
                    $('#app_drawer .predefined_color_pallets').addClass('d-none')
                } else {
                    $('#app_drawer .custom_color_config').addClass('d-none')
                    $('#app_drawer .predefined_color_pallets').removeClass('d-none')
                }
            });

            $('#apply_menu_shape_style').unbind().on('change', function (e) {
                if ($(this).prop("checked") == true) {
                    $('.apply_menu_shape').removeClass('d-none')
                } else {
                    $('.apply_menu_shape').addClass('d-none')
                }
            });

            $('#app_drawer #apply_light_bg').unbind().on('change', function (e) {
                if ($(this).prop("checked") == true) {
                    $('#app_drawer .app-drawer-bg-image-content').removeClass('d-none')
                } else {
                    $('#app_drawer .app-drawer-bg-image-content').addClass('d-none')
                }
            });

            $('.top_menu_style').unbind().on('click ', function (e) {
                if ($(this).val() == 'top_menu_vertical_mini') {
                    $('.vertical-mini-menu-options').removeClass('d-none')
                } else {
                    $('.vertical-mini-menu-options').addClass('d-none')
                }
                if ($(this).val() == 'top_menu_vertical_mini_2') {
                    $('.vertical-mini-menu-options-2').removeClass('d-none')
                } else {
                    $('.vertical-mini-menu-options-2').addClass('d-none')
                }
            });

            $('#apply_menu_bg').unbind().on('change', function (e) {
                if ($(this).prop("checked") == true) {
                    $('.menu-bg-image-content').removeClass('d-none')
                } else {
                    $('.menu-bg-image-content').addClass('d-none')
                }
            });

            $('.app_bg_img_light').unbind().on('change', function (e) {
                var upload_image = document.querySelector('#light_bg_image').files[0];
                var reader1 = new FileReader();
                var bg_data = reader1.readAsDataURL(upload_image);
                reader1.onload = function (e) {
                    var selected_bg_image = e.target.result;
                    window.app_light_bg_image = selected_bg_image
                }
                var fileName = $(this).val().split("\\").pop();
                $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
            });

            $('.app_bg_img_menu').unbind().on('change', function (e) {
                var upload_image = document.querySelector('#menu_bg_image').files[0];
                var reader1 = new FileReader();
                var bg_data = reader1.readAsDataURL(upload_image);
                reader1.onload = function (e) {
                    var selected_bg_image = e.target.result;
                    window.app_menu_bg_image = selected_bg_image
                }
                var fileName = $(this).val().split("\\").pop();
                $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
            });

            $('.app_bg_img_dark').unbind().on('change', function (e) {
                var upload_image = document.querySelector('#dark_bg_image').files[0];
                var reader1 = new FileReader();
                var bg_data = reader1.readAsDataURL(upload_image);
                reader1.onload = function (e) {
                    var selected_bg_image = e.target.result;
                    window.app_dark_bg_image = selected_bg_image
                }
            });

            $('#separator').unbind().on('change', function () {
                $("#theme_separator_style .preview").removeClass("separator_style_4 separator_style_3 separator_style_2 separator_style_1");
                var current_separator_style = $('#separator').val()
                $("#theme_separator_style .preview").addClass(current_separator_style);
            });

            $('#tab').unbind().on('change', function () {
                $("#theme_tab_style .preview").removeClass("tab_style_4 tab_style_3 tab_style_2 tab_style_1");
                var current_tab_style = $('#tab').val()
                $("#theme_tab_style .preview").addClass(current_tab_style);
            });

            $('#checkbox').unbind().on('change', function () {
                $("#theme_checkbox_style .preview").removeClass("checkbox_style_4 checkbox_style_3 checkbox_style_2 checkbox_style_1");
                var current_checkbox_style = $('#checkbox').val()
                $("#theme_checkbox_style .preview").addClass(current_checkbox_style);
            });

            $('#radio').unbind().on('change', function () {
                $("#theme_radio_style .preview").removeClass("radio_style_4 radio_style_3 radio_style_2 radio_style_1");
                var current_radio_style = $('#radio').val()
                $("#theme_radio_style .preview").addClass(current_radio_style);
            });
            $('#button').unbind().on('change', function () {
                $("#theme_buttons_style .preview").removeClass("button_style_4 button_style_3 button_style_2 button_style_1");
                var current_button_style = $('#button').val()
                $("#theme_buttons_style .preview").addClass(current_button_style);
            });

            $('#popup').unbind().on('change', function () {
                $("#theme_popup_style .preview").removeClass("popup_style_4 popup_style_3 popup_style_2 popup_style_1");
                var current_popup_style = $('#popup').val()
                $("#theme_popup_style .preview").addClass(current_popup_style);
            });

            $(".selected_value").on('click', function () {
                var light_primary_bg_color = $("input[id='primary_bg']").val()
                var light_primary_text_color = $("input[id='primary_text']").val()
                var light_secondry_bg_color = $("input[id='secondry_bg']").val()
                var light_secondry_text_color = $("input[id='secondry_text']").val()

                var custom_color_pallet = $("input[id='use_custom_color_config']").is(':checked')
                var selected_color_pallet = $("input[name='color_pallets']:checked").val()

                var custom_drawer_bg = $("input[id='custom_drawer_bg']").val()
                var custom_drawer_text = $("input[id='custom_drawer_text']").val()
                var custom_header_text = $("input[id='custom_header_text']").val()
                var custom_header_bg = $("input[id='custom_header_bg']").val()

                var menu_shape_bg = $("input[id='menu_shape_bg']").val()
                var menu_shape_bg_color_opacity = $("input[id='menu_shape_bg_color_opacity']").val()

                var custom_drawer_color_pallet = $("input[id='use_custom_drawer_color']").is(':checked')
                var selected_drawer_color_pallet = $("input[name='drawer_color_pallets']:checked").val()

                var apply_light_bg_img = $("input[id='apply_light_bg']").is(':checked')
                var apply_menu_shape_style = $("input[id='apply_menu_shape_style']").is(':checked')

                var tree_form_split_view = $("input[id='tree_form_split_view']").is(':checked')
                var attachment_in_tree_view = $("input[id='attachment_in_tree_view']").is(':checked')

                if (window.app_light_bg_image) {
                    var app_light_bg_img = window.app_light_bg_image
                } else if ($("input[id='light_bg_image']").attr('value')) {
                    var app_light_bg_img = $("input[id='light_bg_image']").attr('value')
                }
                else {
                    var app_light_bg_img = false
                }

                if (window.app_menu_bg_image) {
                    var app_menu_bg_img = window.app_menu_bg_image
                } else if ($("input[id='menu_bg_image']").attr('value')) {
                    var app_menu_bg_img = $("input[id='menu_bg_image']").attr('value')
                }
                else {
                    var app_menu_bg_img = false
                }
                var light_body_bg_color = $("input[id='body_bg']").val()
                var light_body_text_color = $("input[id='body_text']").val()

                var dark_primary_bg_color = $("input[id='dark_primary_bg']").val()
                var dark_primary_text_color = $("input[id='dark_primary_text']").val()
                var dark_secondry_bg_color = $("input[id='dark_secondry_bg']").val()
                var dark_secondry_text_color = $("input[id='dark_secondry_text']").val()

                if (window.app_dark_bg_image) {
                    var app_dark_bg_img = window.app_dark_bg_image
                } else if ($("input[id='dark_bg_image']").attr('value')) {
                    var app_dark_bg_img = $("input[id='dark_bg_image']").attr('value')
                }
                else {
                    var app_dark_bg_img = false
                }
                var dark_body_bg_color = $("input[id='dark_body_bg']").val()
                var dark_body_text_color = $("input[id='dark_body_text']").val()

                var selected_separator = $("input[name='separator']:checked").val()
                var selected_tab = $("input[name='tab']:checked").val()
                var selected_checkbox = $("input[name='checkbox']:checked").val()
                var selected_radio = $("input[name='radio']:checked").val()
                var selected_popup = $("input[name='popup']:checked").val()
                var selected_loader = $("input[name='loader_style']:checked").val()
                var selected_login = $("input[name='login_page_style']:checked").val()
                var selected_fonts = $("input[name='font_family']:checked").val()
                var selected_fontsize = $("input[name='font_size']:checked").val()
                var selected_top_menu_position = $("input[name='top_menu_position']:checked").val()
                var selected_top_menu_bg_vertical_mini_2 = $("input[name='top_menu_bg_vertical_mini_2']:checked").val()
                var selected_theme_style = $("input[name='theme_style']:checked").val()
                var selected_menu_shape = $("input[name='shape_style']:checked").val()
                var selected_list_view_density = $("input[name='list_view_density']:checked").val()
                var selected_list_view_sticky_header = $("input[id='list_view_sticky_header']:checked").val()
                var selected_input_style = $("input[name='input_style']:checked").val()

                rpc('/color/pallet/', {
                    'light_primary_bg_color': light_primary_bg_color,
                    'light_primary_text_color': light_primary_text_color,
                    'light_secondry_bg_color': light_secondry_bg_color,
                    'light_secondry_text_color': light_secondry_text_color,
                    'light_body_bg_color': light_body_bg_color,
                    'light_body_text_color': light_body_text_color,

                    'apply_light_bg_img': apply_light_bg_img,
                    'app_light_bg_image': app_light_bg_img,
                    'app_menu_bg_image': app_menu_bg_img,

                    'dark_primary_bg_color': dark_primary_bg_color,
                    'dark_primary_text_color': dark_primary_text_color,
                    'dark_secondry_bg_color': dark_secondry_bg_color,
                    'dark_secondry_text_color': dark_secondry_text_color,
                    'dark_body_bg_color': dark_body_bg_color,
                    'dark_body_text_color': dark_body_text_color,

                    'app_dark_bg_image': app_dark_bg_img,

                    'tree_form_split_view': tree_form_split_view,
                    'attachment_in_tree_view': attachment_in_tree_view,

                    'selected_separator': selected_separator,
                    'selected_tab': selected_tab,
                    'selected_checkbox': selected_checkbox,
                    'selected_radio': selected_radio,
                    'selected_popup': selected_popup,
                    'custom_color_pallet': custom_color_pallet,
                    'selected_color_pallet': selected_color_pallet,

                    'custom_drawer_bg': custom_drawer_bg,
                    'custom_drawer_text': custom_drawer_text,
                    'custom_header_text': custom_header_text,
                    'custom_header_bg': custom_header_bg,
                    'menu_shape_bg': menu_shape_bg,
                    'custom_drawer_color_pallet': custom_drawer_color_pallet,
                    'selected_drawer_color_pallet': selected_drawer_color_pallet,

                    'selected_loader': selected_loader,
                    'selected_login': selected_login,
                    'selected_fonts': selected_fonts,
                    'selected_fontsize': selected_fontsize,
                    // 'selected_chatter_position': selected_chatter_position,
                    'selected_top_menu_position': selected_top_menu_position,
                    'selected_top_menu_bg_vertical_mini_2': selected_top_menu_bg_vertical_mini_2,
                    'selected_theme_style': selected_theme_style,
                    'apply_menu_shape_style': apply_menu_shape_style,
                    'selected_menu_shape': selected_menu_shape,
                    'selected_list_view_density': selected_list_view_density,
                    'selected_list_view_sticky_header': selected_list_view_sticky_header,
                    'selected_input_style': selected_input_style,
                    'menu_shape_bg_color_opacity': menu_shape_bg_color_opacity,
                }).then(function (data) {
                    window.location.reload()
                })
            });
            $('.backend_configurator_close').unbind().click(function (e) {
                $('.dynamic_data').toggleClass('visible')
                $('body.o_web_client').toggleClass('backdrop')
            });


        })
        $('#myModal').modal("show")
    },
    _ChangeThemeModeCLicked: function (ev) {
        $('body').toggleClass('dark_mode')
        if ($('body').hasClass('dark_mode')) {
            var darkmode = true
        } else {
            var darkmode = false
        }
        this._ChangeThemeMode(darkmode)
    },
    _ChangeThemeMode: function (darkmode) {
        if (darkmode) {
            rpc('/active/dark/mode', { 'dark_mode': 'on' })
                .then(function (data) {
                    if (data) {
                    }
                })
            $('body').addClass('dark_mode')
            $(':root').css('--biz-theme-primary-color', 'var(--dark-theme-primary-color)');
            $(':root').css('--biz-theme-primary-text-color', 'var(--dark-theme-primary-text-color)');
            $(':root').css('--biz-theme-secondary-color', 'var(--dark-theme-secondary-color)');
            $(':root').css('--biz-theme-secondary-text-color', 'var(--dark-theme-secondary-text-color)');
            $(':root').css('--biz-theme-body-color', 'var(--dark-theme-body-color)');
            $(':root').css('--biz-theme-body-text-color', 'var(--dark-theme-body-text-color)');
            $(':root').css('--biz-theme-primary-rgba', 'var(--primary-rgba)');
        }
        else {
            rpc('/active/dark/mode', { 'dark_mode': 'off' })
                .then(function (data) {
                    if (data) {
                    }
                })
            $('body').removeClass('dark_mode')
            $(':root').css('--biz-theme-primary-color', 'var(--light-theme-primary-color)');
            $(':root').css('--biz-theme-primary-text-color', 'var(--light-theme-primary-text-color)');
            $(':root').css('--biz-theme-secondary-color', 'var(--light-theme-secondary-color)');
            $(':root').css('--biz-theme-secondary-text-color', 'var(--light-theme-secondary-text-color)');
            $(':root').css('--biz-theme-body-color', 'var(--light-theme-body-color)');
            $(':root').css('--biz-theme-body-text-color', 'var(--light-theme-body-text-color)');
            $(':root').css('--biz-theme-primary-rgba', 'var(--primary-rgba)');
        }
    },
    _ChangeSidebarBehaviour: function (ev) {
        $(ev.target).toggleClass('pinned')
        $('body').toggleClass('pinned')
        if ($(ev.target).hasClass('pinned')) {
            var sidebar_pinned = true
        } else {
            var sidebar_pinned = false
        }
        rpc('/sidebar/behavior/update', {
            'sidebar_pinned': sidebar_pinned,
        }).then(function (data) {
            if (data) {
            }
        })
        this._all_apps_menu_data()
    },

    _GetLanguages: function () {
        var self = this
        var session = session;
        rpc('/get/active/lang').then(function (data) {
            var lang_list = data
            if (data && data.length > 1) {
                $('.active_lang').empty()
                $.each(lang_list, function (index, value) {
                    var searchedlang = $(renderToElement("Searchedlang", {
                        lang_name: value['lang_name'],
                        lang_code: value['lang_code'],
                        active_lang: user.context.lang
                    }))
                    $('.active_lang').append(searchedlang)
                    $('.biz_lang_btn').unbind().on('click', function (ev) {
                        var lang = $(ev.currentTarget)[0].lang
                        self.LangSelect(lang)
                    })
                });
                $('.o_user_lang').removeClass('d-none')
            } else {
                $('.o_user_lang').addClass('d-none')
            }
        })
    },

    LangSelect: function (lang) {
        var self = this;
        rpc('/change/active/lang', {
            'lang': lang,
        }).then(function (data) {
            self.actionService.doAction("reload_context");
        });
    },

    _menuInfo: function (key) {
        return this._drawersearchableMenus[key];
    },

    _searchModalFocus: function () {
        if (!uiUtils.isSmall()) {
            // This timeout is necessary since the menu has a 100ms fading animation
            setTimeout(() => this.$search_modal_input.focus(), 100);
        }
    },

    _searchModalReset: function () {
        this.$search_modal_results.empty();
        this.$search_modal_Noresults.toggleClass('d-none', true);
        this.$search_modal_input.val("");
        this.$search_modal_select.val("all");
        $("input[name='searchOption'][value='menu']").prop('checked', true);
        this._showSearchbarModalOption();
    },

    // T10246 start
    _showSearchbarModalOption: function () {
        // Find the selected radio button's value
        var searchOption = $("input[name='searchOption']:checked").val();
        // Now 'searchOption' contains the value of the selected radio button
        if (searchOption == ""){
            searchOption = "menu"
        }
        this.$search_modal_input_option = searchOption;
        if (searchOption === 'menu') {
            this.$search_modal_results.empty();
            $(this.root.el).find("#search_bar_modal #searchPagesResults").empty();
            this.$search_modal_input.val("");
            this.$search_modal_input.attr('placeholder', 'Search in Menu...');
            this.$search_modal_select.toggleClass('d-none', false);
            this.$search_modal_input_option_select.toggleClass('d-none', true);
        } else if (searchOption === 'records') {
            this.$search_modal_results.empty();
            $(this.root.el).find("#search_bar_modal #searchPagesResults").empty();
            this._all_apps_records_data()
            this.$search_modal_input.val("");
            this.$search_modal_select.toggleClass('d-none', true);
            this.$search_modal_input_option_select.toggleClass('d-none', false);
            this.$search_modal_input.attr('placeholder', 'Search in Records...');
        }
    },
    // T10246 end

    // T10416 start
    _showSearchbarRecord: function () {
        this.$search_modal_results.empty();
        this.$search_modal_Noresults.toggleClass('d-none', true);
        this.$search_modal_input.val("");
    },
    // T10416 end

    // T10416 start
    _showSearchbarMenu: function () {
        this.$search_modal_results.empty();
        this.$search_modal_Noresults.toggleClass('d-none', true);
        this.$search_modal_input.val("");
    },
    // T10416 end

    _showSearchbarModal: function (ev) {
        this.$search_modal_popup = $(this.root.el).find("#search_bar_modal");
        this.$search_modal_input_option = $(this.root.el).find("#search_bar_modal input");
        this.$search_modal_input_option_select = $(this.root.el).find("#search_bar_modal #active-records-global-search");
        this.$search_modal_input = $(this.root.el).find("#search_bar_modal #searchPagesInput");
        this.$search_modal_select = $(this.root.el).find("#search_bar_modal #active-menu-categories");
        this.$search_modal_results = $(this.root.el).find("#search_bar_modal #searchPagesResults");
        this.$search_modal_Noresults = $(this.root.el).find("#search_bar_modal .searchNoResult");
        if (!this.$search_modal_popup.hasClass('show')) {
            this.$search_modal_popup.modal({ keyboard: false });
            this.$search_modal_popup.modal('show');
        } else {
            this.$search_modal_popup.modal('hide');
        }
    },

    _searchResultChosen: function (ev) {
        ev.preventDefault();
        ev.stopPropagation();
        const $result = $(ev.target),
            text = $result.text().trim(),
            data = $result.data(),
            suffix = ~text.indexOf("/") ? "/" : "";

        window.location.href = $(ev.target)[0].href

        // Find app that owns the chosen menu
        // console.log('this._apps========',this._apps)
        // const app = this._apps.find(_app => text.indexOf(_app.name + suffix) === 0);

        this.$search_modal_popup.modal('hide');
        // NOTE: Need to check below trigger_up because app.menuId is not found!
        // Update navbar menus
        // core.bus.trigger("change_menu_section", app.menuID);
    },

    _searchResultsNavigate: function (ev) {
        const all = this.$search_modal_results.find(".search_list_content");
        if (all.filter(".navigate_active").length) {
            var pre_focused = all.filter(".navigate_active")
        } else {
            var pre_focused = $(all[0]);
        }
        let offset = all.index(pre_focused),
            key = ev.key;
        if (!all.length) {
            return;
        }
        if (key === "Tab") {
            ev.preventDefault();
            key = ev.shiftKey ? "ArrowUp" : "ArrowDown";
        }
        switch (key) {
            case "Enter":
                if ($(pre_focused).length) {
                    $(pre_focused).find('.autoComplete_highlighted')[0].click();
                    this.$search_modal_popup.modal('hide');
                }
                break;
            case "ArrowUp":
                offset--;
                break;
            case "ArrowDown":
                offset++;
                break;
            default:
                return;
        }
        if (offset < 0) {
            offset = all.length + offset;
        } else if (offset >= all.length) {
            offset -= all.length;
        }
        const new_focused = $(all[offset]);
        pre_focused.removeClass("navigate_active");
        new_focused.addClass("navigate_active");
        this.$search_modal_results[0].scrollTo(new_focused, {
            offset: {
                top: this.$search_modal_results.height() * -0.5,
            },
        });
    },

    _searchMenuTimeout: function (ev) {
        this._search_def = new Promise((resolve) => {
            setTimeout(resolve, 100);
        });
        this._search_def.then(this._searchPages.bind(this));
    },

    _searchPages: function () {
        var searchvals_option = $("input[name='searchOption']:checked").val();
        const searchvals = this.$search_modal_input.val();
        if (searchvals === "") {
            this.$search_modal_results.empty();
            this.$search_modal_Noresults.toggleClass('d-none', true);
            return;
        }
        if (!searchvals_option) {
            searchvals_option = "menu"
        }
        var $selected_search_mainmenu_name = this.$search_modal_select.children(":selected").attr("id").toLowerCase();
        if (searchvals_option === "menu") {
            var self = this;
            for (const menu of this.menuService.getApps()) {
                Object.assign(
                    this._searchableMenus,[this.menuService.getMenuAsTree(menu.id)].reduce(findNames,{}),
                );
            }
            if ($selected_search_mainmenu_name != '0') {
                if (self._searchableMenus) {
                    Object.keys(self._searchableMenus).forEach(key => {
                        var appid = `${self._searchableMenus[key].appID}`
                        if (appid != $selected_search_mainmenu_name) {
                            delete self._searchableMenus[key]
                        }
                    });
                }

            }
            var results = searchvals
                    ? fuzzyLookup(searchvals, Object.keys(this._searchableMenus), (k) => k)
                    : [];
            this.$search_modal_Noresults.toggleClass('d-none', Boolean(results.length));
            this.$search_modal_results.empty().append(renderToFragment('spiffy_theme_backend.MenuSearchResults', {
                results: results,
                widget: this,
            }));
        }
        else{
            var search_modal_Noresults = this.$search_modal_Noresults
            var search_modal_results = this.$search_modal_results
            // Select the options within the specific elements
            var options = $(this.root.el).find("#search_bar_modal #active-records-global-search").children('option:selected');
            // Iterate through each option and log the attributes
            options.each(function() {
                var id = $(this).attr('id');
                var name = $(this).attr('name');
                var model = $(this).attr('value');
                rpc('/get/records/data', {
                    'model': model,
                    'id': id,
                    'searchvals': searchvals,
                }).then(function (rec) {
                    if (rec) {
                        let results_list = [];
                        for (const id of rec) {
                            let results = {
                                'model': model,
                                'id': id['rec_id'],
                                'name': id['rec_name'],
                                'display_name': id['display_name'],
                                'searchvals': searchvals,
                            }
                            console.log("LOGGGGGGGGGGGGGG",results);
                            
                            results_list.push(results);
                        }
                        search_modal_Noresults.toggleClass('d-none', Boolean(results_list.length));
                        search_modal_results.empty().append(renderToFragment('spiffy_theme_backend.RecordSearchResults', {
                            results: results_list,
                            widget: this,
                        }));
                    }
                }).catch((error) => {
                    console.error("Error fetching records data:", error);
                });
            });
        }
    },

    //  TO DO LIST FUNCTIONS
    biz_TodoList_events: function () {
        var self = this;
        $('#close_to_do_sidebar').unbind().on('click', function (ev) { self._closeToDoSidebar(ev); })
        $('.note-options .note-delete a').unbind().on('click', function (ev) { self._deleteNote(ev); })
        $('.note-options .note-edit a').unbind().on('click', function (ev) { self._editNote(ev); })
    },

    _closeToDoSidebar: function (ev) {
        $('.navbar_to_do_list_data').toggleClass('visible')
        $('body.o_web_client').toggleClass('backdrop')
    },

    _deleteNote: function (ev) {
        var deleteButton = $(ev.currentTarget);
        var noteID = deleteButton.data('note-id');
        var noteSection = deleteButton.parents(".note_content")

        rpc('/delete/todo', {
            'noteID': noteID,
        }).then(function (rec) {
            if (rec) {
                noteSection.remove();
            } else {
                // TODO: we can put some alert for issue in deleting the note here
            }
        });
    },

    _editNote: function (ev) {
        var editButton = $(ev.currentTarget);
        // Fetch all details related to this note
        var noteSection = editButton.parents(".note_content")
        var note_id = noteSection.data('note-id');
        var note_title = noteSection.find('.note-details .note-title h2').text();
        var note_description_element = noteSection.find('.note-details .note-description .description-main');
        var note_description = note_description_element.html()

        var note_color_pallet = editButton.data('note-color');

        // Add all details of the note to edit dialog
        var edit_list = $('.to-do-sidebar-body .add-list');
        var edit_list_outer = $('.to-do-sidebar-body .add-list .add-list-outer');
        edit_list.find('input[name="note_id"]').attr('value', note_id);
        edit_list.find('input[name="note_id"]').val(note_id);
        edit_list_outer.find('.note-colors-option label[color-pallet="' + note_color_pallet + '"]').click();

        edit_list_outer.find('.note-title input').val(note_title);
        edit_list_outer.find('.note-description .note-description-input').html(note_description);
        edit_list_outer.find('.note-save-update #note-create').addClass('d-none');
        edit_list_outer.find('.note-save-update #note-update').removeClass('d-none');

        // Open the edit dialog after adding all the note details
        $('.to-do-sidebar-body').find('.add-new-list-btn').click();
    },



    _openToDoList: function () {
        var self = this
        self.showToDoSidebar();
        $('.navbar_to_do_list_data').toggleClass('visible');
        $('body.o_web_client').toggleClass('backdrop');
    },

    showToDoSidebar: function () {
        var self = this;
        $.get('/show/user/todo/list', {}).then(function (data) {
            $(".navbar_to_do_list_data").empty()
            $(".navbar_to_do_list_data").append(data)

            self.biz_TodoList_events();
            var showListSelf = self;
            $(".add-new-list-btn").on('click', function (ev) {
                if ($('.add-list').hasClass('d-none')) {
                    $(ev.currentTarget).addClass('close');
                    $('.add-list').removeClass('d-none');
                    $('.users-to-do-list').addClass('backdrop');
                } else {
                    $(ev.currentTarget).removeClass('close');
                    $('.add-list').addClass('d-none');
                    $('.users-to-do-list').removeClass('backdrop');

                    // empty all details and note id input on closing new note popup
                    var edit_list = $('.to-do-sidebar-body .add-list');
                    var edit_list_outer = $('.to-do-sidebar-body .add-list .add-list-outer');
                    edit_list.find('input[name="note_id"]').attr('value', '');
                    edit_list.find('input[name="note_id"]').val('');
                    edit_list_outer.find('.note-title input').val('');
                    edit_list_outer.find('.note-description .note-description-input').html('');
                    edit_list_outer.find('.note-save-update #note-create').removeClass('d-none');
                    edit_list_outer.find('.note-save-update #note-update').addClass('d-none');
                    edit_list_outer.find('.note-colors-option label[color-pallet="pallet_1"]').click();

                }
            });

            // create to do list task on 'Add' btn click
            $(".note-save-update .note-add").on('click', function (ev) {
                var self = this
                var to_do_body = $(".navbar_to_do_list_data").find('.to-do-sidebar-body');
                var note_id = $(to_do_body).find('input[name="note_id"]').val();
                var user_id = $(to_do_body).find('input[name="user_id"]');
                var note_title = $(to_do_body).find('.note-title .note-title-input').val();
                var note_description_element = $(to_do_body).find('.note-description .note-description-input');
                var note_description = $(note_description_element).html();
                var note_color_pallet = $(to_do_body).find('.note-colors-option input[name="noteColorPallet"]:checked').val();
                var is_update = $(ev.currentTarget).data('update');

                if (!user_id) {
                    return
                }
                var user_id = $(user_id).val();

                if (note_title === '' || note_description === '') {
                    return
                }

                var jsonDict = {
                    'user_id': user_id,
                    'note_title': note_title,
                    'note_description': note_description,
                    'is_update': is_update ? true : false,
                    'note_pallet': note_color_pallet,
                }

                if (is_update) {
                    jsonDict['note_id'] = note_id
                }

                rpc('/create/todo', jsonDict).then(function (rec) {
                    if (is_update) {
                        var existing_note = $('.users-to-do-list .note_content[data-note-id="' + note_id + '"]');
                        existing_note.remove();
                    }
                    $('.users-to-do-list').prepend(rec);
                    showListSelf.biz_TodoList_events();

                    // close note edit dialog
                    $('.to-do-sidebar-body').find('.add-new-list-btn').click();
                    $('.users-to-do-list').animate({ scrollTop: 0 }, "slow");
                });
            });
        })
    },
});


function divertColor(name, session_dict) {
    var result=""
    window.flutter_inappwebview.callHandler('blobToBase64Handler', 'Hello from WebView!', result);
    var is_body_color = session.bg_color
    return is_body_color
}

export default {
    session_dict: session_dict,
    methods: methods
};

            
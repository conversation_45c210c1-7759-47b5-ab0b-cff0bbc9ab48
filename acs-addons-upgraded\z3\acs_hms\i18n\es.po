# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_hms
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0-20210419\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-04 00:54+0000\n"
"PO-Revision-Date: 2021-06-05 01:48-0500\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.4.3\n"
"Last-Translator: \n"
"Language: es\n"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__appointment_count
msgid "# Appointment"
msgstr "# Cita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__appointment_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__appointment_count
msgid "# Appointments"
msgstr "# Citas"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__evaluation_count
msgid "# Evaluations"
msgstr "# Evaluaciones"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__prescription_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__prescription_count
msgid "# Prescriptions"
msgstr "# Recetas"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__treatment_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__treatment_count
msgid "# Treatments"
msgstr "# Tratamientos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__patient_count
#: model:ir.model.fields,field_description:acs_hms.field_res_users__patient_count
msgid "#Patient"
msgstr "#Paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__physician_count
#: model:ir.model.fields,field_description:acs_hms.field_res_users__physician_count
msgid "#Physician"
msgstr "#Profesional"

#. module: acs_hms
#: model:mail.template,report_name:acs_hms.acs_appointment_email
#: model:mail.template,report_name:acs_hms.acs_prescription_email
msgid "${(object.name or '').replace('/','_')}"
msgstr "${(object.name or '').replace('/', '_')}"

#. module: acs_hms
#: model:mail.template,subject:acs_hms.acs_appointment_email
msgid "${object.patient_id.name|safe} Your Appointment Have been Scheduled"
msgstr "${object.patient_id.name|safe} Su cita ha sido programada"

#. module: acs_hms
#: model:mail.template,subject:acs_hms.acs_prescription_email
msgid "${object.patient_id.name|safe} Your Prescription"
msgstr "${object.patient_id.name|safe} Su receta"

#. module: acs_hms
#: model:ir.actions.report,print_report_name:acs_hms.patient_card_report_id
msgid "(object.code or object.name).replace('/','_')"
msgstr "(object.code or object.name).replace('/','_')"

#. module: acs_hms
#: model:ir.actions.report,print_report_name:acs_hms.action_appointment_report
msgid "(object.name or 'Appointment').replace('/','_')"
msgstr "(object.name or 'Cita').replace('/','_')"

#. module: acs_hms
#: model:ir.actions.report,print_report_name:acs_hms.report_acs_medical_advice_id
msgid "(object.name or 'Appointment').replace('/','_')+'_ADVICE'"
msgstr "(object.name or 'Cita').replace('/','_')+'_ADVICE'"

#. module: acs_hms
#: model:ir.actions.report,print_report_name:acs_hms.action_report_acs_evaluation
msgid "(object.name or 'Evaluation').replace('/','_')+'_Evalution'"
msgstr "(object.name or 'Evaluación').replace('/','_')+'_Evaluación'"

#. module: acs_hms
#: model:ir.actions.report,print_report_name:acs_hms.report_hms_prescription_id
msgid "(object.name or 'Prescription').replace('/','_')"
msgstr "(object.name or 'Prescripción').replace('/','_')"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid ""
"<b>Comment(If Any):</b>\n"
"                    <br/>"
msgstr "<b>Comentario (si hay):</b> <br/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_kanban
msgid "<b>Date:</b>"
msgstr "<b>Fecha:</b>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "<b>Disease: </b>"
msgstr "<b>Enfermedad:</b>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid ""
"<b>Doctor’s Stamp/Signature</b>\n"
"                        <br/>"
msgstr ""
"<b>Firma del Profesional</b>\n"
"                    <br/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_kanban
msgid "<b>Patient:</b>"
msgstr "<b>Paciente:</b>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_kanban
msgid "<b>Physician:</b>"
msgstr "<b>Profesional:</b>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_cardreport_document
msgid ""
"<br/>\n"
"                                    <strong>Age</strong> :"
msgstr ""
"<br/>\n"
"                                    <strong>Edad</strong> :"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_cardreport_document
msgid ""
"<br/>\n"
"                                    <strong>Birth Date</strong> :"
msgstr ""
"<br/>\n"
"                                    <strong>Fecha de nacimiento</strong> :"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_cardreport_document
msgid ""
"<br/>\n"
"                                    <strong>Name</strong> :"
msgstr ""
"<br/>\n"
"                                    <strong>Nombre</strong> :"

#. module: acs_hms
#: model:mail.template,body_html:acs_hms.acs_prescription_email
msgid ""
"<div style=\"padding:0px;width:600px;margin:auto;background: #FFFFFF repeat "
"top /100%;color:#777777\">\n"
"    <p>Hello ${object.patient_id.name},</p>\n"
"    <p>Your Prescription details. For more details please refer attached PDF "
"report.</p>\n"
"    <ul>\n"
"        <li>\n"
"            <p>Reference Number: ${object.name}</p><br/>\n"
"        </li>\n"
"        % if object.appointment_id:\n"
"        <li>\n"
"            <p>Appointment ID: ${object.appointment_id.name|safe}</p><br/>\n"
"        </li>\n"
"        % endif\n"
"        % if object.physician_id:\n"
"        <li>\n"
"            <p>Physician Name: ${object.physician_id.name|safe}</p><br/>\n"
"        </li>\n"
"        % endif\n"
"        <li>\n"
"            <p>Prescription Date: ${object.prescription_date}</p><br/>\n"
"        </li>\n"
"    </ul>\n"
"    <p>Please feel free to call anytime for further information or any query."
"</p>\n"
"\n"
"    <p>Best regards.</p><br/>\n"
"</div>\n"
"  \n"
"            "
msgstr ""
"<div style=\"padding:0px;width:600px;margin:auto;background: #FFFFFF repeat "
"top /100%;color:#777777\">\n"
"    <p>Hola ${object.patient_id.name},</p>\n"
"    <p>Aquí su prescripción. Para obtener más detalles, consulte el informe "
"en PDF adjunto.</p>\n"
"    <ul>\n"
"        <li>\n"
"            <p>Número de Referencia: ${object.name}</p><br/>\n"
"        </li>\n"
"        % if object.appointment_id:\n"
"        <li>\n"
"            <p>ID de Cita: ${object.appointment_id.name|safe}</p><br/>\n"
"        </li>\n"
"        % endif\n"
"        % if object.physician_id:\n"
"        <li>\n"
"            <p>Nombre del Médico: ${object.physician_id.name|safe}</p><br/>\n"
"        </li>\n"
"        % endif\n"
"        <li>\n"
"            <p>Día de Prescripción: ${object.prescription_date}</p><br/>\n"
"        </li>\n"
"    </ul>\n"
"    <p>No dude en llamar en cualquier momento para obtener más información o "
"cualquier consulta..</p>\n"
"\n"
"    <p>Atentamente.</p><br/>\n"
"</div>\n"
"  \n"
"            "

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Diastolic BP Chart"
"\"/> Diastolic BP Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Diastolic BP Chart"
"\"/> PA Diastólica Chart"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Hear Rate Chart\"/"
"> Hear Rate Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Hear Rate Chart\"/"
"> FC Chart"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Height Chart\"/> "
"Height Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Height Chart\"></"
"i>Gráfico de altura"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"RR Chart\"/> RR "
"Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"RR Chart\"/> FR "
"Chart"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"SpO2 Chart\"/> "
"SpO2 Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"SpO2 Chart\"/> "
"SpO2 Chart"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Systolic BP Chart"
"\"/> Systolic BP Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Systolic BP Chart"
"\"/> PA Sistólica Chart"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Weight Chart\"/> "
"Temprature"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Weight Chart\"/> "
"Temperatura"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Weight Chart\"/> "
"Weight Chart"
msgstr ""
"<i class=\"fa fa-area-chart\" aria-label=\"Info\" title=\"Weight Chart\"></"
"i>Gráfico de peso"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.physician_kanban_view
msgid ""
"<span class=\"fa fa-circle text-danger\" role=\"img\" aria-label=\"Absent\" "
"title=\"Absent\" name=\"presence_absent\"/>"
msgstr ""
"<span class=\"fa fa-circle text-danger\" role=\"img\" aria-label=\"Absent\" "
"title=\"Absent\" name=\"presence_absent\"/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.physician_kanban_view
msgid ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Present"
"\" title=\"Present\" name=\"presence_present\"/>"
msgstr ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Present"
"\" title=\"Present\" name=\"presence_present\"/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.physician_kanban_view
msgid ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"To define"
"\" title=\"To define\" name=\"presence_to_define\"/>"
msgstr ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"To define"
"\" title=\"To define\" name=\"presence_to_define\"/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid ""
"<span class=\"float-right\" style=\"font-size: 10px;\"><b>Scan to "
"Authenticate.</b></span><br/>"
msgstr ""
"<span class=\"float-right\" style=\"font-size: 10px;\"><b>Escanear para "
"autenticarse.</b></span><br/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Auto Follow-up Date</span>"
msgstr "<span class=\"o_form_label\">Auto Programar Seguimiento</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Consultation Service</span>"
msgstr "<span class=\"o_form_label\">Servicio de Consulta</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Follow-up Days</span>"
msgstr "<span class=\"o_form_label\">Días de seguimiento</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Follow-up Service</span>"
msgstr "<span class=\"o_form_label\">Servicio de seguimiento</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Invoice Policy</span>"
msgstr "<span class=\"o_form_label\">Política de facturas</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Patient Registration Service</span>"
msgstr "<span class=\"o_form_label\">Servicio de Registro de Pacientes</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Stock Location</span>"
msgstr "<span class=\"o_form_label\">Ubicación del stock</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Stock Usage Location</span>"
msgstr "<span class=\"o_form_label\">Ubicación de uso de existencias</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Treatment Registration Service</span>"
msgstr ""
"<span class=\"o_form_label\">Servicio de Registro de Tratamiento</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Chief Complaints</b></u>:</span><br/>"
msgstr "<span><u><b>Principales quejas</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Differential Diagnosis</b></u>:</span><br/>"
msgstr "<span><u><b>Diagnóstico Diferencial</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Disease:</b></u>:</span><br/>"
msgstr "<span><u><b>Enfermedad:</b></u>:</span><br/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>History of Present Illness</b></u>:</span><br/>"
msgstr "<span><u><b>Enfermedad Actual</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Laboratory</b></u>:</span><br/>"
msgstr "<span><u><b>Laboratorio</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Medical Advice</b></u>:</span><br/>"
msgstr "<span><u><b>Consejos médicos</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Notes</b></u>:</span><br/>"
msgstr "<span><u><b>Notas</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Past History</b></u>:</span><br/>"
msgstr "<span><u><b>Antecedentes</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<span><u><b>Radiological</b></u>:</span><br/>"
msgstr "<span><u><b>Radiología</b></u>:</span><br>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid "<span>Appointment: </span>"
msgstr "<span>Cita:</span>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<strong>Advice Date: </strong>"
msgstr "<strong>Fecha de la recomendación:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<strong>Advising Doctor: </strong>"
msgstr "<strong>Médico de cabecera:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<strong>Age: </strong>"
msgstr "<strong>Edad:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "<strong>Age</strong>"
msgstr "<strong>Edad:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
msgid "<strong>Date: </strong>"
msgstr "<strong>Fecha:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "<strong>Date</strong>"
msgstr "<strong>Fecha:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "<strong>Doctor</strong>"
msgstr "<strong>Profesional:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_cardreport_document
msgid "<strong>ID</strong> :"
msgstr "<strong>Cédula</strong> :"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<strong>Medical Advice</strong>"
msgstr "<strong>Resumen de Atención</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
msgid "<strong>Patient Evaluation</strong>"
msgstr "<strong>Evaluación del paciente</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<strong>Patient: </strong>"
msgstr "<strong>Paciente: </strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "<strong>Patient</strong>"
msgstr "<strong>Paciente:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
msgid "<strong>Physician: </strong>"
msgstr "<strong>Profesional:</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "<strong>Prescribed Medicines</strong>"
msgstr "<strong>Medicamentos recetados</strong>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid "<strong>Subject:</strong>"
msgstr "<strong>Propósito:</strong>"

#. module: acs_hms
#: model:mail.template,body_html:acs_hms.acs_appointment_email
msgid ""
"<style>\n"
"span.oe_mail_footer_access {\n"
"    display:block;\n"
"    text-align:center;\n"
"    color:grey;\n"
"}\n"
"a:link {text-decoration: none;}\n"
"a:visited {text-decoration: none;}\n"
"a:hover {text-decoration: underline;}\n"
"a:active {text-decoration: underline;}\n"
"</style>\n"
"\n"
"<div style=\"border-radius: 2px; max-width: 600px!important; height: auto;"
"margin-left: auto;margin-right: auto;background-color:#f9f9f9;padding: 20px;"
"\">\n"
"    <p>Hello ${object.patient_id.name},</p>\n"
"    <p>Your Appointment Have been Scheduled with following details.</p>\n"
"    <ul>\n"
"        <li>\n"
"            <p>Subject: ${object.purpose_id.name}</p><br/>\n"
"        </li>\n"
"        <li>\n"
"            <p>Reference Number: ${object.name}</p><br/>\n"
"        </li>\n"
"        <li>\n"
"            <p>Physician Name: ${object.physician_id.name}</p><br/>\n"
"        </li>\n"
"        <li>\n"
"            <p>Date &amp; Time: ${format_datetime(object.date, tz=(object."
"physician_id.tz or \"UTC\"), dt_format=(object.physician_id.lang."
"date_format))} (Timezone: ${object.physician_id.tz or \"UTC\"})</p><br/>\n"
"        </li>\n"
"    </ul>\n"
"</div>\n"
"\n"
"<div style=\"display: block;text-align: center;\">\n"
"    <table style=\"text-align: center;border-radius: 2px; max-width: 600px!"
"important; height: auto;margin-left: auto;margin-right: auto;background-"
"color:#f9f9f9;\">\n"
"            <tr><td style=\"height:10px;font-size : 14px;\"><br/></td></tr>\n"
"            % if object.company_id.whatsapp:\n"
"            <tr>\n"
"                <td style=\"text-align: center;font-size : 14px;border-"
"collapse: separate;margin-top:10px;\">\n"
"                <strong>Clic y Chatea o Llámanos</strong>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                <a href=\"https://api.whatsapp.com/send?phone=${object."
"company_id.whatsapp}&amp;text=${object.company_id.whatsapptext}\" target="
"\"_blank\"><img alt=\"WhatsApp\" src=\"/acs_birthday_wish/static/src/img/"
"clic_whatsapp.png\" height=\"64px\"/></a>\n"
"                </td>\n"
"            </tr>\n"
"            % endif\n"
"            <tr>\n"
"                <td style=\"text-align: center;font-size:14px;border-"
"collapse: separate;margin-top:0px;\">\n"
"                % if object.company_id.displayaddress:\n"
"                <strong><a href=\"${object.company_id.imagemaplink}\" target="
"\"_blank\">${object.company_id.displayaddress or ''}</a></strong>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                % if object.company_id.imagemap_file:\n"
"                <a href=\"${object.company_id.imagemaplink}\" target=\"_blank"
"\"><img border=\"0\" alt=\"See Map\" src=\"data:image/png;base64,${object."
"company_id.get_imagemap_file()}\" style=\"max-width: 600px;\"/></a>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                % if object.company_id.waze or object.company_id.gmaps :\n"
"                <b>Schedule your departure with your preferred navigation "
"system: </b>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                % if object.company_id.waze:\n"
"                <a href=\"${object.company_id.waze}\" target=\"_blank\"><img "
"alt=\"Waze\" src=\"/acs_birthday_wish/static/src/img/waze.png\" height=\"64px"
"\"/></a>\n"
"                % endif\n"
"                % if object.company_id.gmaps:\n"
"                <a href=\"${object.company_id.gmaps}\" target=\"_blank"
"\"><img alt=\"Google Maps\" src=\"/acs_birthday_wish/static/src/img/gmaps.png"
"\" height=\"64px\"/></a>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">_____________________</"
"td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.logotemplates_file:\n"
"                <a><img border=\"0\" src=\"data:image/png;base64,${object."
"company_id.get_logotemplates_file()}\" style=\"max-width: 600px;\"/></a>\n"
"            % elif object.company_id.name:\n"
"                ${object.company_id.name or ''}\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.phone:\n"
"                % if object.company_id.phone2:\n"
"                    <b><a href=\"tel:${object.company_id.phone}\" target="
"\"_blank\"><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.phone}</span></a>, <a href=\"tel:${object.company_id.phone2}\" "
"target=\"_blank\"><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.phone2}</span></a></b>\n"
"                %else\n"
"                    <b><span><a href=\"tel:${object.company_id.phone}\" "
"target=\"_blank\"><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.phone}</span></a></span></b>\n"
"                % endif\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.hours:\n"
"                <b><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.hours}</span></b>\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.website:\n"
"                <b><a href=\"${object.company_id.website}\" target=\"_blank"
"\"><span style=\"color:#8a89ba;font-size: 14px;\">${object.company_id."
"website}</span></a></b>\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;\">\n"
"            % if object.company_id.social_facebook:\n"
"                <a href=\"${object.company_id.social_facebook}\" target="
"\"_blank\"><img alt=\"Facebook\" src=\"/acs_birthday_wish/static/src/img/"
"facebook.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_instagram:\n"
"                <a href=\"${object.company_id.social_instagram}\" target="
"\"_blank\"><img alt=\"Instagram\" src=\"/acs_birthday_wish/static/src/img/"
"instagram.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_twitter:\n"
"                <a href=\"${object.company_id.social_twitter}\" target="
"\"_blank\"><img alt=\"Twitter\" src=\"/acs_birthday_wish/static/src/img/"
"twitter.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.pinterest:\n"
"                <a href=\"${object.company_id.pinterest}\" target=\"_blank"
"\"><img alt=\"Pinterest\" src=\"/acs_birthday_wish/static/src/img/pinterest."
"png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_linkedin:\n"
"                <a href=\"${object.company_id.social_linkedin}\" target="
"\"_blank\"><img alt=\"LinkedIn\" src=\"/acs_birthday_wish/static/src/img/"
"linkedin.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_youtube:\n"
"                <a href=\"${object.company_id.social_youtube}\" target="
"\"_blank\"><img alt=\"Youtube\" src=\"/acs_birthday_wish/static/src/img/"
"youtube.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.snapchat:\n"
"                <a href=\"${object.company_id.snapchat}\" target=\"_blank"
"\"><img alt=\"Snapchat\" src=\"/acs_birthday_wish/static/src/img/snapchat.png"
"\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;font-size:10px;border-"
"collapse: separate;margin-top:0px;\">Power by <a style=\"display: inline; "
"padding: .2em .6em .3em; font-weight: 700; line-height: 1; color: #fff;text-"
"align: center;white-space: nowrap;vertical-align: baseline; border-"
"radius: .25em; background-color: #8A89BA;\" href=\"https://medic.red\">Medic "
"Red</a></td>\n"
"            </tr>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""
"<style>\n"
"span.oe_mail_footer_access {\n"
"    display:block;\n"
"    text-align:center;\n"
"    color:grey;\n"
"}\n"
"a:link {text-decoration: none;}\n"
"a:visited {text-decoration: none;}\n"
"a:hover {text-decoration: underline;}\n"
"a:active {text-decoration: underline;}\n"
"</style>\n"
"\n"
"<div style=\"border-radius: 2px; max-width: 600px!important; height: auto;"
"margin-left: auto;margin-right: auto;background-color:#f9f9f9;padding: 20px;"
"\">\n"
"    <p>Hola ${object.patient_id.name},</p>\n"
"    <p>Tu cita ha sido agendada con los siguientes detalles.</p>\n"
"    <ul>\n"
"        <li>\n"
"            <p>Motivo: ${object.purpose_id.name}</p><br/>\n"
"        </li>\n"
"        <li>\n"
"            <p>Número de referencia: ${object.name}</p><br/>\n"
"        </li>\n"
"        <li>\n"
"            <p>Nombre del profesional: ${object.physician_id.name}</p><br/>\n"
"        </li>\n"
"        <li>\n"
"            <p>Día &amp; Hora: ${format_datetime(object.date, tz=(object."
"physician_id.tz or \"UTC\"), dt_format=(object.physician_id.lang."
"date_format))} (Timezone: ${object.physician_id.tz or \"UTC\"})</p><br/>\n"
"        </li>\n"
"    </ul>\n"
"</div>\n"
"\n"
"<div style=\"display: block;text-align: center;\">\n"
"    <table style=\"text-align: center;border-radius: 2px; max-width: 600px!"
"important; height: auto;margin-left: auto;margin-right: auto;background-"
"color:#f9f9f9;\">\n"
"            <tr><td style=\"height:10px;font-size : 14px;\"><br/></td></tr>\n"
"            % if object.company_id.whatsapp:\n"
"            <tr>\n"
"                <td style=\"text-align: center;font-size : 14px;border-"
"collapse: separate;margin-top:10px;\">\n"
"                <strong>Clic y Chatea o Llámanos</strong>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                <a href=\"https://api.whatsapp.com/send?phone=${object."
"company_id.whatsapp}&amp;text=${object.company_id.whatsapptext}\" target="
"\"_blank\"><img alt=\"WhatsApp\" src=\"/acs_birthday_wish/static/src/img/"
"clic_whatsapp.png\" height=\"64px\"/></a>\n"
"                </td>\n"
"            </tr>\n"
"            % endif\n"
"            <tr>\n"
"                <td style=\"text-align: center;font-size:14px;border-"
"collapse: separate;margin-top:0px;\">\n"
"                % if object.company_id.displayaddress:\n"
"                <strong><a href=\"${object.company_id.imagemaplink}\" target="
"\"_blank\">${object.company_id.displayaddress or ''}</a></strong>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                % if object.company_id.imagemap_file:\n"
"                <a href=\"${object.company_id.imagemaplink}\" target=\"_blank"
"\"><img border=\"0\" alt=\"See Map\" src=\"data:image/png;base64,${object."
"company_id.get_imagemap_file()}\" style=\"max-width: 600px;\"/></a>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                % if object.company_id.waze or object.company_id.gmaps :\n"
"                <b>Programe su salida con su sistema de navegación "
"preferido: </b>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">\n"
"                % if object.company_id.waze:\n"
"                <a href=\"${object.company_id.waze}\" target=\"_blank\"><img "
"alt=\"Waze\" src=\"/acs_birthday_wish/static/src/img/waze.png\" height=\"64px"
"\"/></a>\n"
"                % endif\n"
"                % if object.company_id.gmaps:\n"
"                <a href=\"${object.company_id.gmaps}\" target=\"_blank"
"\"><img alt=\"Google Maps\" src=\"/acs_birthday_wish/static/src/img/gmaps.png"
"\" height=\"64px\"/></a>\n"
"                % endif\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;\">_____________________</"
"td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.logotemplates_file:\n"
"                <a><img border=\"0\" src=\"data:image/png;base64,${object."
"company_id.get_logotemplates_file()}\" style=\"max-width: 600px;\"/></a>\n"
"            % elif object.company_id.name:\n"
"                ${object.company_id.name or ''}\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.phone:\n"
"                % if object.company_id.phone2:\n"
"                    <b><a href=\"tel:${object.company_id.phone}\" target="
"\"_blank\"><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.phone}</span></a>, <a href=\"tel:${object.company_id.phone2}\" "
"target=\"_blank\"><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.phone2}</span></a></b>\n"
"                %else\n"
"                    <b><span><a href=\"tel:${object.company_id.phone}\" "
"target=\"_blank\"><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.phone}</span></a></span></b>\n"
"                % endif\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.hours:\n"
"                <b><span style=\"color:#8a89ba;font-size: 14px;\">${object."
"company_id.hours}</span></b>\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;font-size:14px;border-collapse: "
"separate;margin-top:0px;\">\n"
"            % if object.company_id.website:\n"
"                <b><a href=\"${object.company_id.website}\" target=\"_blank"
"\"><span style=\"color:#8a89ba;font-size: 14px;\">${object.company_id."
"website}</span></a></b>\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"            <td style=\"text-align: center;\">\n"
"            % if object.company_id.social_facebook:\n"
"                <a href=\"${object.company_id.social_facebook}\" target="
"\"_blank\"><img alt=\"Facebook\" src=\"/acs_birthday_wish/static/src/img/"
"facebook.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_instagram:\n"
"                <a href=\"${object.company_id.social_instagram}\" target="
"\"_blank\"><img alt=\"Instagram\" src=\"/acs_birthday_wish/static/src/img/"
"instagram.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_twitter:\n"
"                <a href=\"${object.company_id.social_twitter}\" target="
"\"_blank\"><img alt=\"Twitter\" src=\"/acs_birthday_wish/static/src/img/"
"twitter.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.pinterest:\n"
"                <a href=\"${object.company_id.pinterest}\" target=\"_blank"
"\"><img alt=\"Pinterest\" src=\"/acs_birthday_wish/static/src/img/pinterest."
"png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_linkedin:\n"
"                <a href=\"${object.company_id.social_linkedin}\" target="
"\"_blank\"><img alt=\"LinkedIn\" src=\"/acs_birthday_wish/static/src/img/"
"linkedin.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.social_youtube:\n"
"                <a href=\"${object.company_id.social_youtube}\" target="
"\"_blank\"><img alt=\"Youtube\" src=\"/acs_birthday_wish/static/src/img/"
"youtube.png\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"\n"
"            % if object.company_id.snapchat:\n"
"                <a href=\"${object.company_id.snapchat}\" target=\"_blank"
"\"><img alt=\"Snapchat\" src=\"/acs_birthday_wish/static/src/img/snapchat.png"
"\" width=\"64px\" height=\"64px\"/></a>\n"
"            % endif\n"
"            </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"text-align: center;font-size:10px;border-"
"collapse: separate;margin-top:0px;\">Power by <a style=\"display: inline; "
"padding: .2em .6em .3em; font-weight: 700; line-height: 1; color: #fff;text-"
"align: center;white-space: nowrap;vertical-align: baseline; border-"
"radius: .25em; background-color: #8A89BA;\" href=\"https://medic.red"
"\">MedicRed</a></td>\n"
"            </tr>\n"
"    </table>\n"
"</div>\n"
"        "

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid ""
"<u><b>Doctor's Stamp/Signature</b></u>\n"
"                        <br/>"
msgstr ""
"<u><b>Firma del Profesional</b></u>\n"
"                        <br/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_tree
msgid "ACS Diseases"
msgstr "Enfermedades"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_tree
msgid "ACS Ethnicity"
msgstr "Etnia"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_tree
msgid "ACS Medication Dosage"
msgstr "Dosis de medicamentos ACS"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "ACS Patient Medication"
msgstr "Medicamentos para pacientes ACS"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
msgid "ACS Prescription Line"
msgstr "Línea de Receta ACS"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_tree
msgid "ACS Prescription Order"
msgstr "Orden de prescripción"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_search
msgid "Abbreviation"
msgstr "Abreviatura"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_needaction
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_needaction
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_needaction
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__is_active
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Active"
msgstr "Activo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__active_component_ids
msgid "Active Component"
msgstr "Componente activo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad  de Excepción"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_state
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_state
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_state
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_type_icon
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_type_icon
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_type_icon
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__status__acute
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_status__acute
msgid "Acute"
msgstr "Agudo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__adverse_reaction
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Adverse Reactions"
msgstr "Reacciones Adversas"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__chromosome
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__chromosome
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
msgid "Affected Chromosome"
msgstr "Cromosoma afectado"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__age
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__age
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__patient_age
msgid "Age"
msgstr "Edad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__age
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__age
msgid "Age when diagnosed"
msgstr "Edad al momento del diagnóstico"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__alert_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__alert_count
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__alert_count
msgid "Alert Count"
msgstr "Recuento de alertas"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient__grpah_data_filter__all
msgid "All"
msgstr "Todos"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Allergic"
msgstr "Alergia"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__is_allergy
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__is_allergy
msgid "Allergic Disease"
msgstr "Enfermedad alérgica"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__allergy_type
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__allergy_type
msgid "Allergy type"
msgstr "Tipo de alergia"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__anytime_invoice
#: model:ir.model.fields,field_description:acs_hms.field_res_company__appointment_anytime_invoice
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__appointment_anytime_invoice
msgid "Allow Invoice Anytime in Appointment"
msgstr "Permitir facturar en cualquier momento de la cita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__allow_substitution
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
msgid "Allow Substitution"
msgstr "Permitir Sustitución"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__allow_substitution
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
msgid "Allow substitution"
msgstr "Permitir Sustitución"

#. module: acs_hms
#: model:res.groups,name:acs_hms.group_allow_consultation_pause
msgid "Allow to Pause Consultation"
msgstr "Permitir pausar la consulta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Allow user to Autheticate prescription by scanning QrCode from report."
msgstr ""
"Permitir al usuario autenticar la prescripción por Scan de código Qr desde "
"el reporte."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_res_users_inherit_form
msgid "Allowed Departments"
msgstr "Departamentos permitidos"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_medicament_group_line__common_dosage_id
#: model:ir.model.fields,help:acs_hms.field_medicament_group_line__dose
#: model:ir.model.fields,help:acs_hms.field_prescription_line__dose
msgid "Amount of medication (eg, 250 mg) per dose"
msgstr "Cantidad de medicamento (por ejemplo, 250 mg) por dosis"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_appointment
#: model:ir.actions.report,name:acs_hms.action_appointment_report
#: model:ir.model,name:acs_hms.model_hms_appointment
#: model:ir.model.fields,field_description:acs_hms.field_account_bank_statement_line__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_account_move__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_account_payment__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_consumable_line__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__appointment_id
#: model:ir.model.fields,field_description:acs_hms.field_stock_move__appointment_id
#: model:ir.model.fields.selection,name:acs_hms.selection__account_move__hospital_invoice_type__appointment
#: model:ir.ui.menu,name:acs_hms.action_main_menu_appointmnet_opd
#: model:ir.ui.menu,name:acs_hms.menu_appointment
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_tree
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_physician_form
msgid "Appointment"
msgstr "Cita"

#. module: acs_hms
#: model:res.groups,name:acs_hms.group_acs_hms_appointment_user
msgid "Appointment (Nurse)"
msgstr "Cita (Enfermera)"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_appointment_cabin
#: model:ir.model,name:acs_hms.model_appointment_cabin
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__name
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__cabin_id
#: model:ir.ui.menu,name:acs_hms.menu_open_appointment_cabin
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_cabin_form_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_cabin_form_tree
msgid "Appointment Cabin"
msgstr "Consultorio de cita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__department_type
msgid "Appointment Department"
msgstr "Departamento de la cita"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_move_form
msgid "Appointment Details"
msgstr "Detalles de la cita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__name
msgid "Appointment Id"
msgstr "Id de cita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__location
msgid "Appointment Location"
msgstr "Ubicación de la cita"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_appointment_purpose
#: model:ir.model,name:acs_hms.model_appointment_purpose
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__name
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__purpose_id
#: model:ir.ui.menu,name:acs_hms.menu_patient_appointment
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_purpose_form_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_purpose_form_tree
msgid "Appointment Purpose"
msgstr "Propósito de la cita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__appointment_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__appointment_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__appointment_ids
#: model:ir.model.fields,field_description:acs_hms.field_hr_department__appointment_ids
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_appointment_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_pivot
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Appointments"
msgstr "Citas"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_attachment_count
msgid "Attachment Count"
msgstr "Archivos Adjuntos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__attachment_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__attachment_ids
msgid "Attachments"
msgstr "Adjuntos"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__aunt
msgid "Aunt"
msgstr "Tía"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__auto_followup_days
msgid "Auto Followup on (Days)"
msgstr "Auto programar días de Seguimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__qty_available
msgid "Available Qty"
msgstr "Cantidad disponible"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__bmi_state
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__bmi_state
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__bmi_state
msgid "BMI State"
msgstr "Estado del IMC"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
msgid "BMI State:"
msgstr "Estado del IMC:"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "BP:"
msgstr "PA:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__birthday_mail_template_id
msgid "Birthday Wishes Template"
msgstr "Plantilla de deseos de cumpleaños"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__bmi
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__bmi
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__bmi
msgid "Body Mass Index"
msgstr "Índice de masa corporal"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
msgid "Body Mass Index:"
msgstr "Índice de masa corporal:"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__brother
msgid "Brother"
msgstr "Hermano"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__cabin_id
msgid "Cabin"
msgstr "Consultorio"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_cancel_reason
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Cancel"
msgstr "Cancelar"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_cancel_reason
msgid "Cancel Appointment"
msgstr "Cancelar reserva"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_acs_cancel_reason
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__cancel_reason
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Cancel Reason"
msgstr "Motivo de Cancelación"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Cancellation Reason..."
msgstr "Por qué el paciente/cliente canceló…"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__state__cancel
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__cancel
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__state__cancel
#: model:ir.model.fields.selection,name:acs_hms.selection__prescription_order__state__canceled
msgid "Cancelled"
msgstr "Cancelado"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__category
#: model:ir.model.fields,field_description:acs_hms.field_resource_calendar__category
msgid "Category"
msgstr "Categoría"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__name
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_search
msgid "Category Name"
msgstr "Nombre de la categoría"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__cod
msgid "Cause of Death"
msgstr "Causa de Muerte"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__is_infectious
msgid "Check if the patient has an infectious transmissible disease"
msgstr "Compruebe si el paciente tiene una enfermedad transmisible infecciosa"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_disease__is_infectious
msgid "Check if the patient has an infectioustransmissible disease"
msgstr ""
"Marque esta casilla si el paciente tiene una enfermedad transmisible "
"infecciosa"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_medication__is_active
msgid "Check if the patient is currently taking the medication"
msgstr "Marque esta casilla si el paciente está tomando un medicamento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_medicament_group_line__prnt
#: model:ir.model.fields,help:acs_hms.field_prescription_line__prnt
msgid "Check this box to print this line of the prescription."
msgstr "Marque esta casilla para imprimir esta línea de la receta."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__chief_complain
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Chief Complaints"
msgstr "Principales quejas"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Chief Complaints..."
msgstr "Principales quejas por las que acude el paciente…"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__status__chronic
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_status__chronic
msgid "Chronic"
msgstr "Crónica"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__classification
msgid "Classification"
msgstr "Clasificación"

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_acs_family_relation
msgid "Click to add Family Relation."
msgstr "Haga clic para agregar Relación familiar."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_medicament_group_view
msgid "Click to add Medicaments Group."
msgstr "Haga clic para agregar un grupo de medicamentos."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_hms_prescription_order_view
msgid "Click to add Prescrition."
msgstr "Haga clic para agregar una prescripción."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_acs_disease_category_view
msgid "Click to add a Disease Categories."
msgstr "Agregar categorías de enfermedades."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_diseases_view
msgid "Click to add a Diseases."
msgstr "Agregar una Enfermedad."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_acs_ethnicity_view
msgid "Click to add a Ethnicity."
msgstr "Agregar una etnia."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_hms_genetic_disease
msgid "Click to add a Genetic Disease."
msgstr "Agregar Enfermedad Genética"

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_medical_alerts
msgid "Click to add a Medical Alert."
msgstr "Haga clic para agregar una alerta médica."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_acs_patient_evaluation
msgid "Click to add a Patient Evaluation."
msgstr "Haga clic para agregar una evaluación de paciente"

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_referring_doctors
msgid "Click to add a Referring Doctor."
msgstr "Haga clic para agregar un médico de referencia."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_hms_patient_disease
msgid "Click to add a Systematic Examination."
msgstr "Haga clic para agregar un examen sistémico."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.acs_action_form_hospital_treatment
msgid "Click to add a Treatment."
msgstr "Agregar un tratamiento."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_appointment_cabin
msgid "Click to add an Appointment Cabin."
msgstr "Agregar un consultorio de citas."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_appointment_purpose
msgid "Click to add an Appointment Purpose."
msgstr "Agregar un propósito de cita."

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_appointment
msgid "Click to add an Appointment."
msgstr "Agregar una cita."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Clinical Assesment"
msgstr "Objetivo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__code
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__code
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__code
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_search
msgid "Code"
msgstr "Código"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Cofigure days to set Auto followup days on appointment."
msgstr "Configure los días para programar auto seguimiento a la cita."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__short_comment
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__short_comment
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
msgid "Comment"
msgstr "Comentario"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Company"
msgstr "Compañía"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__state__done
msgid "Completed"
msgstr "Completado"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__age
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__age
msgid "Computed patient age at the moment of the evaluation"
msgstr "Edad del paciente en el momento de la evaluación"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.menu_appointment_cofig
#: model:ir.ui.menu,name:acs_hms.menu_hms_cofig
#: model:ir.ui.menu,name:acs_hms.menu_hms_patient_cofig
#: model:ir.ui.menu,name:acs_hms.menu_pres_cofig
msgid "Configuration"
msgstr "Configuración"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__confirm
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Confirm"
msgstr "Confirmar"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__consultation_type__consultation
#: model:ir.model.fields.selection,name:acs_hms.selection__product_template__hospital_product_type__consultation
msgid "Consultation"
msgstr "Consulta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Consultation Appointment"
msgstr "Cita para Consulta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Consultation Done"
msgstr "Consulta realizada"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__consultation_product_id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__consultation_product_id
msgid "Consultation Invoice Product"
msgstr "Producto de consulta a facturar"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_res_company__consultation_product_id
#: model:ir.model.fields,help:acs_hms.field_res_config_settings__consultation_product_id
msgid "Consultation Product"
msgstr "Producto de consulta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__product_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__consultaion_service_id
#: model:product.product,name:acs_hms.hms_consultation_service_0
#: model:product.template,name:acs_hms.hms_consultation_service_0_product_template
msgid "Consultation Service"
msgstr "Servicio de consulta"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__product_id
msgid "Consultation Services"
msgstr "Servicios de consulta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__appointment_duration
msgid "Consultation Time"
msgstr "Tiempo de consulta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__appointment_duration_timer
msgid "Consultation Timer"
msgstr "Temporizador de consulta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__consultation_type
msgid "Consultation Type"
msgstr "Tipo de consulta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__consumable_line_ids
msgid "Consumable Line"
msgstr "Línea de consumible"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__consumable_invoice_id
msgid "Consumables Invoice"
msgstr "Factura de consumibles"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Consumed Products"
msgstr "Productos consumidos"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__course_completed
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Course Completed"
msgstr "Curso Terminado"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__cousin
msgid "Cousin"
msgstr "Primo"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
msgid "Create"
msgstr "Crear"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Create Consumable's Invoice"
msgstr "Crear factura de consumibles"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Create Evaluation"
msgstr "Crear Evaluación"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Create Invoice"
msgstr "Crear Factura"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_partner_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_res_users_inherit_form
msgid "Create Patient"
msgstr "Crear paciente"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Create Patient Registration Invoicing"
msgstr "Crear facturación de registro de pacientes"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_res_users_inherit_form
msgid "Create Physician"
msgstr "Crear médico"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Create Prescription"
msgstr "Crear Receta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__create_uid
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__create_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__create_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__create_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__create_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__create_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__create_date
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__create_date
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__create_date
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__create_date
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__create_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__create_date
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__create_date
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__create_date
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__create_date
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__create_date
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__create_date
msgid "Created on"
msgstr "Creado en"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_prescription_line__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its "
"children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""
"Cantidad actual de productos.\n"
"En un contexto con una única ubicación de stock, esto incluye los productos "
"almacenados en esta ubicación o cualquiera de sus elementos secundarios.\n"
"En un contexto con un único almacén, esto incluye las mercancías almacenadas "
"en la ubicación de stock de este almacén o en cualquiera de sus elementos "
"secundarios.\n"
"en la Ubicación de Stock del Almacén de esta Tienda, o cualquiera de sus "
"hijos.\n"
"De lo contrario, esto incluye las mercancías almacenadas en cualquier "
"ubicación de stock con tipo \"interno\"."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "D/D & Advice"
msgstr "A&R"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__date
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__date
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Date"
msgstr "Fecha"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__date_to
msgid "Date To"
msgstr "Fecha Para"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hr_employee_public__birthday
msgid "Date of Birth"
msgstr "Fecha de nacimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__diagnosed_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__date
msgid "Date of Diagnosis"
msgstr "Fecha del diagnóstico"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__registration_date
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__registration_date
msgid "Date of Registration"
msgstr "Fecha de registro"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid "Date. :"
msgstr "Fecha:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__days
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__days
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
msgid "Days"
msgstr "Días"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__appointment_duration
msgid "Default Consultation Duration"
msgstr "Duración de Consulta por Defecto"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__auto_followup_days
msgid "Default Followup on (Days)"
msgstr "Días de Seguimiento por Defecto"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hr_department
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__department_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__department_id
#: model:ir.model.fields,field_description:acs_hms.field_resource_calendar__department_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_schedule_search
msgid "Department"
msgstr "Departamento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_resource_calendar__department_id
msgid "Department for which the schedule is applicable."
msgstr "Departamento para el que se aplica el horario."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid "Department:"
msgstr "Departamento:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__department_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__department_ids
#: model:ir.model.fields,field_description:acs_hms.field_res_users__department_ids
msgid "Departments"
msgstr "Departamentos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__description
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medical_alert_form
msgid "Description"
msgstr "Descripción"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
msgid "Details"
msgstr "Detalles"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__radiological_report
msgid "Details of the Radiological Report"
msgstr "Detalles del Informe Radiológico"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__lab_report
msgid "Details of the lab report."
msgstr "Detalles del informe del laboratorio."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__diagnosis_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Diagnosis"
msgstr "Diagnóstico"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__diastolic_bp
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__diastolic_bp
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__diastolic_bp
msgid "Diastolic BP"
msgstr "PA Diastólica"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__differencial_diagnosis
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Differential Diagnosis"
msgstr "Diagnóstico Diferencial"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Differential Diagnosis..."
msgstr "Anote su diagnóstico diferencial…"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__discontinued
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Discontinued"
msgstr "Descontinuado"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_form
msgid "Diseas"
msgstr "Enfermedades"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__disease
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__diseases_ids
msgid "Disease"
msgstr "Enfermedad"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_acs_disease_category_view
#: model:ir.ui.menu,name:acs_hms.menu_disease_category
msgid "Disease Categories"
msgstr "Categorías de enfermedades"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__disease_gene
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_genetic_risk_form
msgid "Disease Gene"
msgstr "Gen de la enfermedad"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_disease_gene
msgid "Disease Genetic"
msgstr "Enfermedad Genética"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__patient_diseases_ids
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Disease History"
msgstr "Historia de Enfermedad"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_diseases__name
msgid "Disease name"
msgstr "Nombre de la enfermedad"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_diseases_view
#: model:ir.model,name:acs_hms.model_hms_diseases
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__diseases_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_diseases_ids
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__diseases_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__diseases_ids
#: model:ir.ui.menu,name:acs_hms.diseases_menu
#: model:ir.ui.menu,name:acs_hms.hms_diseases
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Diseases"
msgstr "Enfermedades"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_diseases_category
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_tree
msgid "Diseases Category"
msgstr "Categoría Enfermedades"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Diseases History"
msgstr "Historial de Enfermedades"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_account_move__display_name
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__display_name
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__display_name
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__display_name
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__display_name
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__display_name
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__display_name
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__display_name
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__display_name
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__display_name
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_consumable_line__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hr_department__display_name
#: model:ir.model.fields,field_description:acs_hms.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__display_name
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__display_name
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__display_name
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__display_name
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__display_name
#: model:ir.model.fields,field_description:acs_hms.field_product_template__display_name
#: model:ir.model.fields,field_description:acs_hms.field_res_company__display_name
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:acs_hms.field_res_partner__display_name
#: model:ir.model.fields,field_description:acs_hms.field_res_users__display_name
#: model:ir.model.fields,field_description:acs_hms.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:acs_hms.field_stock_move__display_name
msgid "Display Name"
msgstr "Nombre "

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__resource_calendar__category__doctor
#: model:res.groups,name:acs_hms.group_hms_doctor
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_disease_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Doctor"
msgstr "Profesional"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Doctor's Appointments"
msgstr "Citas del profesional"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_schedule_search
msgid "Doctor's Schedule"
msgstr "Agenda del profesional"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__attach_count
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__attach_count
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Documents"
msgstr "Documentos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__dominance
msgid "Dominance"
msgstr "Dominancia"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__state__done
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__done
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Done"
msgstr "Hecho"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__dose
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__dose
msgid "Dosage"
msgstr "Dosificación"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_medicament_dosage__code
msgid "Dosage Code,for example: SNOMED 229798009 = 3 times per day"
msgstr ""
"Código de dosificación, por ejemplo: SNOMED 229798009 = 3 veces por día"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__common_dosage_id
msgid "Dosage Frequency"
msgstr "Frecuencia de dosis"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_medicament_dosage__abbreviation
msgid "Dosage abbreviation, such as tid in the US or tds in the UK"
msgstr "Abreviatura de Dosis, como TID (3 veces x día)"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__common_dosage_id
msgid "Dosage/Frequency"
msgstr "Dosis/Frecuencia"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
msgid "Dose Qty."
msgstr "Dosis QTy."

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__state__draft
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__draft
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__state__draft
#: model:ir.model.fields.selection,name:acs_hms.selection__prescription_order__state__draft
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Draft"
msgstr "Borrador"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__allergy_type__da
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__allergy_type__da
msgid "Drug Allergy"
msgstr "Alergia a medicamento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_prescription_line__common_dosage_id
#: model:ir.model.fields,help:acs_hms.field_prescription_line__form_id
#: model:ir.model.fields,help:acs_hms.field_prescription_line__route_id
#: model:ir.model.fields,help:acs_hms.field_product_product__common_dosage_id
#: model:ir.model.fields,help:acs_hms.field_product_template__common_dosage_id
msgid "Drug form, such as tablet or gel"
msgstr "Presentación farmacéutica, tales como tabletas o gel"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__planned_duration
msgid "Duration"
msgstr "Duración"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__date_end
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__end_date
msgid "End Date"
msgstr "Fecha final"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__end_date
msgid "End of treatment date"
msgstr "Fecha de fin del tratamiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__manual_prescription_qty
msgid "Enter Prescription Qty Manually."
msgstr "Introduzca ctd. de prescripción manualmente."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__ethnic_group_id
msgid "Ethnic group"
msgstr "Grupo étnico"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_acs_ethnicity_view
#: model:ir.model,name:acs_hms.model_acs_ethnicity
msgid "Ethnicity"
msgstr "Etnicidad"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_acs_patient_evaluation_popup
#: model:ir.actions.report,name:acs_hms.action_report_acs_evaluation
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__evaluation_id
msgid "Evaluation"
msgstr "Evaluación"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__evaluation_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__evaluation_ids
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Evaluations"
msgstr "Evaluaciones"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__info
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__notes
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Extra Info"
msgstr "Información Extra"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__family_member_ids
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Family"
msgstr "Familia"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Family Disease History"
msgstr "Antecedentes de Enfermedades Familiares"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hms_patient_family_diseases
msgid "Family Diseases"
msgstr "Enfermedades familiares"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__family_history_ids
msgid "Family Diseases History"
msgstr "Antecedentes de Enfermedades Familiares"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_acs_family_member
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__related_patient_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_family_member_form
msgid "Family Member"
msgstr "Miembro de la familia"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_family_member__related_patient_id
msgid "Family Member Name"
msgstr "Nombre de Familiar"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_acs_family_relation
#: model:ir.model,name:acs_hms.model_acs_family_relation
#: model:ir.ui.menu,name:acs_hms.menu_family_relation
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_family_relation_tree
msgid "Family Relation"
msgstr "Relación familiar"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__father
msgid "Father"
msgstr "Padre"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid "Filters"
msgstr "Filtros"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__finding
msgid "Findings"
msgstr "Hallazgos"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Findings from treatment.."
msgstr "Hallazgos del tratamiento…"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_family_diseases__relative
msgid ""
"First degree = siblings, mother and father; second degree = Uncles, nephews "
"and Nieces; third degree = Grandparents and cousins"
msgstr ""
"En primer grado = hermanos, la madre y el padre; segundo grado = Tíos, "
"sobrinos y sobrinas; tercer grado = abuelos y primos"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__consultation_type__followup
msgid "Follow Up"
msgstr "Seguimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__follow_date
msgid "Follow Up Date"
msgstr "Fecha de revisión"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__followup_product_id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__followup_product_id
msgid "Follow-up Invoice Product"
msgstr "Producto de factura de seguimiento"

#. module: acs_hms
#: model:product.product,name:acs_hms.hms_followup_service_0
#: model:product.template,name:acs_hms.hms_followup_service_0_product_template
msgid "Follow-up Service"
msgstr "Servicio de seguimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_channel_ids
msgid "Followers (Channels)"
msgstr "Seguidores (Canales)"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Followup Appointment"
msgstr "Cita de Seguimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__followup_days
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__followup_days
msgid "Followup Days"
msgstr "Días de seguimiento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_res_company__followup_product_id
#: model:ir.model.fields,help:acs_hms.field_res_config_settings__followup_product_id
msgid "Followup Product"
msgstr "Producto de seguimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__followup_service_id
msgid "Followup Service"
msgstr "Servicio de Seguimiento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__activity_type_icon
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__activity_type_icon
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__activity_type_icon
#: model:ir.model.fields,help:acs_hms.field_prescription_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__allergy_type__fa
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__allergy_type__fa
msgid "Food Allergy"
msgstr "Alergia alimentaria"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__form_id
msgid "Form"
msgstr "Presentación"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__abbreviation
#: model:ir.model.fields,field_description:acs_hms.field_product_product__common_dosage_id
#: model:ir.model.fields,field_description:acs_hms.field_product_template__common_dosage_id
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_search
msgid "Frequency"
msgstr "Frecuencia"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__gene
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
msgid "Gene"
msgstr "Gen"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__gene_id
msgid "Gene ID"
msgstr "Gene ID"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hr_department__department_type__general
msgid "General"
msgstr "General"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "General Details"
msgstr "Detalles generales"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
msgid "General Information"
msgstr "Información general"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_hms_genetic_disease
#: model:ir.ui.menu,name:acs_hms.menu_hms_genetic_disease
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_genetic_disease_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_genetic_disease_tree
msgid "Genetic Disease"
msgstr "Enfermedad Genética"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_family_disease_tree
msgid "Genetic Family Diseases"
msgstr "Enfermedades genéticas familiares"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Genetic Risk"
msgstr "Riesgo genético"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__genetic_risks_ids
msgid "Genetic Risks"
msgstr "Riesgos Genéticos"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
msgid "Get Lines"
msgstr "Obtener líneas"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__grandfather
msgid "Grandfather"
msgstr "Abuelo"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__grandmother
msgid "Grandmother"
msgstr "Abuela"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_schedule_search
msgid "Group By..."
msgstr "Agrupar por..."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__name
msgid "Group Name"
msgstr "Nombre del grupo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__grpah_data_filter
msgid "Grpah Filter Type"
msgstr "Tipo de filtro del Gráfico"

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.menu_hms
msgid "HMS"
msgstr "SGH"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__hr
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__hr
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__hr
msgid "HR"
msgstr "FC"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "HR:"
msgstr "FC:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__healed_date
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__status__healed
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_status__healed
msgid "Healed"
msgstr "Curada"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__healed_date
msgid "Healed Date"
msgstr "Fecha curada"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_disease_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Healing Date"
msgstr "Fecha de curación"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__hr
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__hr_old
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__hr
#: model:ir.model.fields,help:acs_hms.field_hms_patient__hr
msgid "Heart Rate"
msgstr "Frecuencia cardíaca"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__height
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__height
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__height
msgid "Height"
msgstr "Altura"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__height
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__height
#: model:ir.model.fields,help:acs_hms.field_hms_patient__height
msgid "Height in cm"
msgstr "Altura en cm"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
msgid "Height:"
msgstr "Alto:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__present_illness
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "History of Present Illness"
msgstr "Enfermedad actual"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "History of Present Illness..."
msgstr "Todo lo que refiere el paciente que hace relación a sus síntomas…"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_res_company
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__company_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__company_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__company_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__company_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__company_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Hospital"
msgstr "Hospital"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hr_department__department_type
msgid "Hospital Department"
msgstr "Departamento del Hospital"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_partner_form
msgid "Hospital Info"
msgstr "Información Clínica"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_account_bank_statement_line__hospital_invoice_type
#: model:ir.model.fields,field_description:acs_hms.field_account_move__hospital_invoice_type
#: model:ir.model.fields,field_description:acs_hms.field_account_payment__hospital_invoice_type
msgid "Hospital Invoice Type"
msgstr "Tipo de factura hospitalaria"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_product_product__hospital_product_type
#: model:ir.model.fields,field_description:acs_hms.field_product_template__hospital_product_type
msgid "Hospital Product Type"
msgstr "Tipo de producto hospitalario"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__hr_presence_state
msgid "Hr Presence State"
msgstr "Estado de Presencia Hr"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_diseases__classification__icd10
msgid "ICD-10"
msgstr "ICD-10"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_diseases__classification__vaccine
msgid "ICD-11"
msgstr "ICD-11"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_diseases__classification__icd9
msgid "ICD-9"
msgstr "ICD-9"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_account_move__id
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__id
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__id
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__id
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__id
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__id
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__id
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__id
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__id
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__id
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_consumable_line__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__id
#: model:ir.model.fields,field_description:acs_hms.field_hr_department__id
#: model:ir.model.fields,field_description:acs_hms.field_hr_employee_public__id
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__id
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__id
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__id
#: model:ir.model.fields,field_description:acs_hms.field_product_template__id
#: model:ir.model.fields,field_description:acs_hms.field_res_company__id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__id
#: model:ir.model.fields,field_description:acs_hms.field_res_partner__id
#: model:ir.model.fields,field_description:acs_hms.field_res_users__id
#: model:ir.model.fields,field_description:acs_hms.field_resource_calendar__id
#: model:ir.model.fields,field_description:acs_hms.field_stock_move__id
msgid "ID"
msgstr "Identificación"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__activity_exception_icon
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__activity_exception_icon
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__activity_exception_icon
#: model:ir.model.fields,help:acs_hms.field_prescription_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_needaction
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_unread
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_needaction
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_unread
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_needaction
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_unread
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_needaction
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado hay nuevos mensajes que requieren su atención."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_has_error
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_has_sms_error
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_has_error
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_has_sms_error
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_has_error
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_has_sms_error
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_has_error
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra marcado, algunos mensajes tienen error de envío."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__pricelist_id
msgid "If you change the pricelist, related invoice will be affected."
msgstr ""
"Si cambia la lista de precios, la factura relacionada se verá afectada."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__image_128
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__image_128
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__image_128
msgid "Image"
msgstr "Imagen"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__status__improving
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_status__improving
msgid "Improving"
msgstr "Mejorando"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "In Consultation"
msgstr "En Consulta"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__in_consultation
msgid "In consultation"
msgstr "En consulta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Infectious"
msgstr "Infecciosa"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__is_infectious
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__is_infectious
msgid "Infectious Disease"
msgstr "Enfermedad infecciosa"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__info
msgid "Information"
msgstr "Información"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__inverse_relation_id
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__inverse_relation_id
msgid "Inverse Relation"
msgstr "Relación inversa"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__invoice_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__invoice_id
msgid "Invoice"
msgstr "Factura"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Invoice Anytime"
msgstr "Factura en cualquier momento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__no_invoice
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_tree
msgid "Invoice Exempt"
msgstr "Exento de facturas"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__advance_invoice
#: model:ir.model.fields,field_description:acs_hms.field_res_company__appo_invoice_advance
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__appo_invoice_advance
msgid "Invoice before Confirmation in Appointment"
msgstr "Factura antes de la confirmación en la cita"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Invoice in Advance"
msgstr "Factura por adelantado"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_is_follower
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_is_follower
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_is_follower
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__is_referring_doctor
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__is_referring_doctor
#: model:ir.model.fields,field_description:acs_hms.field_res_partner__is_referring_doctor
#: model:ir.model.fields,field_description:acs_hms.field_res_users__is_referring_doctor
msgid "Is Refereinng Physician"
msgstr "Es médico de referencia"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: acs_hms
#: model:res.groups,name:acs_hms.group_hms_jr_doctor
msgid "Jr Doctor"
msgstr "Jr Doctor"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__lab_report
msgid "Lab Report"
msgstr "Reporte de Lab"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Laboratory"
msgstr "Laboratorio"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Laboratory Report..."
msgstr "Anote los resultados de laboratorio relevantes…"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__lactation
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__lactation
msgid "Lactation"
msgstr "Lactancia"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__last_evaluation_id
msgid "Last Appointment"
msgstr "Última cita"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Last Appointment:"
msgstr "Última cita:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_account_move____last_update
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason____last_update
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity____last_update
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member____last_update
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation____last_update
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert____last_update
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation____last_update
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin____last_update
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose____last_update
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene____last_update
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_consumable_line____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hr_department____last_update
#: model:ir.model.fields,field_description:acs_hms.field_hr_employee_public____last_update
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage____last_update
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group____last_update
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line____last_update
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line____last_update
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order____last_update
#: model:ir.model.fields,field_description:acs_hms.field_product_template____last_update
#: model:ir.model.fields,field_description:acs_hms.field_res_company____last_update
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:acs_hms.field_res_partner____last_update
#: model:ir.model.fields,field_description:acs_hms.field_res_users____last_update
#: model:ir.model.fields,field_description:acs_hms.field_resource_calendar____last_update
#: model:ir.model.fields,field_description:acs_hms.field_stock_move____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__write_uid
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__write_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__write_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__write_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__write_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__write_date
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__write_date
#: model:ir.model.fields,field_description:acs_hms.field_appointment_cabin__write_date
#: model:ir.model.fields,field_description:acs_hms.field_appointment_purpose__write_date
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__write_date
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__write_date
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__write_date
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__write_date
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__write_date
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__write_date
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__write_date
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__limit
msgid "Limit"
msgstr "Límite"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_group_tree
msgid "Line"
msgstr "Línea"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hms_consumable_line
msgid "List of Consumables"
msgstr "Lista de consumibles"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__location
msgid "Location"
msgstr "Ubicación"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Location to manage consumed products in Consultation."
msgstr "Ubicación para gestionar los productos consumidos en Consulta."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Location to take consumed products in Consultation."
msgstr "Ubicación para tomar los productos consumidos en la consulta."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_disease_gene__location
msgid "Locus of the chromosome"
msgstr "Locus del cromosoma"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "Logo"
msgstr "Logo"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__bmi_state__low_weight
msgid "Low Weight"
msgstr "Bajo peso"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
msgid "Main Category"
msgstr "Categoría Principal"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_product_product__manual_prescription_qty
#: model:ir.model.fields,field_description:acs_hms.field_product_template__manual_prescription_qty
msgid "Manual Prescription Qty"
msgstr "Ctd. de prescripción manual"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__manual_quantity
msgid "Manual Total Qty"
msgstr "Ctd. Total Manual"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__xory__m
msgid "Maternal"
msgstr "Maternal"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__xory
msgid "Maternal or Paternal"
msgstr "Materno o paterno"

#. module: acs_hms
#: model:ir.actions.report,name:acs_hms.report_acs_medical_advice_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__medical_advice
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Medical Advice"
msgstr "Resumen Clínico"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Medical Advice..."
msgstr "Anote sus recomendaciones…"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medical_alert_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medical_alert_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medical_alert_tree
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Medical Alert"
msgstr "Alerta Médica"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_acs_medical_alert
msgid "Medical Alert for Patient"
msgstr "Alerta Médica para el Paciente"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_medical_alerts
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__medical_alert_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__medical_alert_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__medical_alert_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__medical_alert_ids
#: model:ir.ui.menu,name:acs_hms.menu_medical_alerts
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medical_alert_form
msgid "Medical Alerts"
msgstr "Alertas médicas"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__urgency__medical_emergency
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Medical Emergency"
msgstr "Urgencia Médica"

#. module: acs_hms
#: model:res.groups,name:acs_hms.group_hms_medical_officer
msgid "Medical Officer"
msgstr "Oficial Médico"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "Medical Prescription:"
msgstr "Prescripción médica"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_medicament_dosage
msgid "Medicament Dosage"
msgstr "Dosis del medicamento"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_medicament_group
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__group_id
#: model:ir.ui.menu,name:acs_hms.menu_medicine_hms_medicament_group
#: model:ir.ui.menu,name:acs_hms.menuitem_action_presc_medicament_group
msgid "Medicament Group"
msgstr "Grupo de medicamentos"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_medicament_group_line
msgid "Medicament Group Line"
msgstr "Línea del Grupo de Medicamentos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__medicament_group_line_ids
msgid "Medicament line"
msgstr "Línea de medicamento"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_medicament_group_view
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__group_id
msgid "Medicaments Group"
msgstr "Grupo de Medicamentos"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_medicament_dosage
#: model:ir.ui.menu,name:acs_hms.menu_medicine_medicament_dosage
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_form
msgid "Medication Dosage"
msgstr "Dosis de medicación"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__template
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
msgid "Medication Template"
msgstr "Plantilla de medicación"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__medication_ids
msgid "Medications"
msgstr "Medicamentos"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Medicine"
msgstr "Medicina"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_group_form
msgid "Medicine Group"
msgstr "Grupo de medicamentos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__product_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_tree
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Medicine Name"
msgstr "Nombre de Medicamento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_has_error
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_has_error
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_has_error
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__disease_severity__mild
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_severity__mild
msgid "Mild"
msgstr "Leve"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__allergy_type__ma
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__allergy_type__ma
msgid "Misc Allergy"
msgstr "Miscelánea alergias"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__allergy_type__mc
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__allergy_type__mc
msgid "Misc Contraindication"
msgstr "Otras Contraindicaciones"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__disease_severity__moderate
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_severity__moderate
msgid "Moderate"
msgstr "Moderado"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__mother
msgid "Mother"
msgstr "Madre"

#. module: acs_hms
#: model:res.groups,name:acs_hms.group_allow_multi_department
#: model_terms:ir.ui.view,arch_db:acs_hms.view_res_users_inherit_form
msgid "Multi Department"
msgstr "Multi departamento"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "My Appointments"
msgstr "Mis citas"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "My Treatments"
msgstr "Mis tratamientos"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "NOTE: Details are shown based on Patient Evaluation."
msgstr "NOTA: Detalles son mostrados en base a la Evaluación del Paciente."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "NOTE: Details are shown based on last Completed Patient Evaluation."
msgstr ""
"NOTA: Detalles son mostrados en base a la última Evaluación completa del "
"Paciente."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__name
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__name
#: model:ir.model.fields,field_description:acs_hms.field_acs_medical_alert__name
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__name
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__name
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__name
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__name
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_cabin_form_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_appointment_purpose_form_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medical_alert_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_dosage_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_medicament_group_form
msgid "Name"
msgstr "Nombre"

#. module: acs_hms
#: model:ir.model.constraint,message:acs_hms.constraint_acs_ethnicity_name_uniq
#: model:ir.model.constraint,message:acs_hms.constraint_medicament_dosage_name_uniq
msgid "Name must be unique!"
msgstr "El nombre debe ser único!"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_diseases__protein
msgid "Name of the protein(s) affected"
msgstr "Nombre de la(s) proteína(s) afectada(s)"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__nephew
msgid "Nephew"
msgstr "Sobrino"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente plazo de actividad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_summary
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_summary
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_summary
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_type_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_type_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_type_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__niece
msgid "Niece"
msgstr "Sobrina"

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.acs_action_form_hospital_treatment
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_acs_disease_category_view
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_acs_ethnicity_view
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_appointment_cabin
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_appointment_purpose
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_diseases_view
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_hms_prescription_order_view
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_medical_alerts
#: model_terms:ir.actions.act_window,help:acs_hms.act_open_medicament_group_view
#: model_terms:ir.actions.act_window,help:acs_hms.action_acs_family_relation
#: model_terms:ir.actions.act_window,help:acs_hms.action_acs_patient_evaluation
#: model_terms:ir.actions.act_window,help:acs_hms.action_appointment
#: model_terms:ir.actions.act_window,help:acs_hms.action_hms_genetic_disease
#: model_terms:ir.actions.act_window,help:acs_hms.action_hms_patient_disease
#: model_terms:ir.actions.act_window,help:acs_hms.action_referring_doctors
msgid "No Record Found"
msgstr "Registro no encontrado"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.acs_no_details
msgid "No Related Details Available. Please check check it again."
msgstr ""
"No hay detalles relacionados disponibles. Por favor, compruebe el código de "
"nuevo."

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__bmi_state__normal
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__urgency__normal
msgid "Normal"
msgstr "Normal"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Not Done"
msgstr "No se ha hecho"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hr_department__note
msgid "Note"
msgstr "Nota"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_ethnicity__notes
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__notes
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_ethnicity_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Notes"
msgstr "Notas"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_disease_form
msgid "Notes about the disease"
msgstr "Notas sobre la enfermedad"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Notes..."
msgstr "Estas notas las verá el paciente si tiene portal de usuario…"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_needaction_counter
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_needaction_counter
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_needaction_counter
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_has_error_counter
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_has_error_counter
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_has_error_counter
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_medicament_group_line__quantity
#: model:ir.model.fields,help:acs_hms.field_prescription_line__quantity
msgid "Number of units of the medicament. Example : 30 capsules of amoxicillin"
msgstr ""
"Número de unidades del medicamento. Ejemplo: 30 cápsulas de amoxicilina"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__message_unread_counter
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__message_unread_counter
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__message_unread_counter
#: model:ir.model.fields,help:acs_hms.field_prescription_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes no leidos"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__resource_calendar__category__nurse
#: model:res.groups,name:acs_hms.group_hms_nurse
msgid "Nurse"
msgstr "Enfermera"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_schedule_search
msgid "Nurse's Schedule"
msgstr "Horario de la enfermera"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__diastolic_bp_old
msgid "OLD Diastolic BP"
msgstr "PD ANTERIOR"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__hr_old
msgid "OLD HR"
msgstr "FC ANTERIOR"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__rr_old
msgid "OLD RR"
msgstr "FR ANTERIOR"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__spo2_old
msgid "OLD SpO2"
msgstr "SpO2 ANTERIOR"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__systolic_bp_old
msgid "OLD Systolic BP"
msgstr "PS ANTERIOR"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__temp_old
msgid "OLD Temp"
msgstr "Temp. ANTERIOR"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__bmi_state__obesity
msgid "Obesity"
msgstr "Obesidad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__long_name
msgid "Official Long Name"
msgstr "Nombre completo Oficial"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_disease_gene__name
msgid "Official Name"
msgstr "Nombre Oficial"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__old_prescription_id
msgid "Old Prescription"
msgstr "Receta anterior"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Other Information"
msgstr "Otra información"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__outside_appointment
msgid "Outside Appointment"
msgstr "Cita Externa"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__acs_patient_evaluation__bmi_state__over_weight
msgid "Over Weight"
msgstr "Sobre el peso"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__spo2
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__spo2_old
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__spo2
#: model:ir.model.fields,help:acs_hms.field_hms_patient__spo2
msgid "Oxygen Saturation, percentage of oxygen bound to hemoglobin"
msgstr "Saturación de oxígeno, porcentaje de oxígeno ligado a la hemoglobina"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_diseases_category__parent_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_category_search
msgid "Parent Category"
msgstr "Categoría padre"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__past_history
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Past History"
msgstr "Antecedentes"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Past History..."
msgstr "Los Antecedentes Personales Patológicos…"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__medical_history
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__medical_history
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Past Medical History"
msgstr "Antecedentes Médicos"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__past_history
msgid "Past history of any diseases."
msgstr "Antecedentes sobre cualquier enfermedad."

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__xory__f
msgid "Paternal"
msgstr "Paterno"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hms_patient
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_genetic_risk__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__patient_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__patient_id
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Patient"
msgstr "Paciente"

#. module: acs_hms
#: model:ir.actions.report,name:acs_hms.patient_card_report_id
msgid "Patient Card"
msgstr "Carnet del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hr_department__patient_department
msgid "Patient Department"
msgstr "Departamento del Paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_diastolic_bp_line_graph
msgid "Patient Diastolic Bp Line Graph"
msgstr "Línea gráfica de PA diastólica del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__patient_disease_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_disease_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_disease_tree
msgid "Patient Disease"
msgstr "Enfermedad del paciente"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hms_patient_disease
msgid "Patient Diseases"
msgstr "Enfermedades del paciente"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_acs_patient_evaluation
#: model:ir.model,name:acs_hms.model_acs_patient_evaluation
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_tree
msgid "Patient Evaluation"
msgstr "Evaluación de Paciente"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_pivot
msgid "Patient Evaluations"
msgstr "Evaluaciones del paciente"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_patient_evaluation_graph_1
msgid "Patient Evalution Graph"
msgstr "Gráfico de evaluación del paciente"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_family_disease_form
msgid "Patient Genetic Family Diseases"
msgstr "Enfermedades Genéticas de la Familia del Paciente"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hms_patient_genetic_risk
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_genetic_risk_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_genetic_risk_tree
msgid "Patient Genetic Risks"
msgstr "Riegos Genéticos del Paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_height_line_graph
msgid "Patient Height Line Graph"
msgstr "Línea gráfica de altura del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_hr_line_graph
msgid "Patient Hr Line Graph"
msgstr "Línea gráfica de FC del paciente"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_hms_patient_medication_view
#: model:ir.model,name:acs_hms.model_hms_patient_medication
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_tree
msgid "Patient Medication"
msgstr "Medicación del paciente"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__patient_id
msgid "Patient Name"
msgstr "Nombre del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__patient_registration_product_id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__patient_registration_product_id
msgid "Patient Registration Invoice Product"
msgstr "Factura del producto de registro del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__group_patient_registartion_invoicing
#: model:res.groups,name:acs_hms.group_patient_registartion_invoicing
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Patient Registration Invoicing"
msgstr "Facturación de Registro de Paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_rr_line_graph
msgid "Patient Rr Line Graph"
msgstr "Línea gráfica de FR del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_spo2_line_graph
msgid "Patient Spo2 Line Graph"
msgstr "Línea gráfica de SpO2 del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_systolic_bp_line_graph
msgid "Patient Systolic Bp Line Graph"
msgstr "Línea gráfica de PA sistólica del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_temp_line_graph
msgid "Patient Temp Line Graph"
msgstr "Línea gráfica de temperatura del paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__patient_weight_line_graph
msgid "Patient Weight Line Graph"
msgstr "Línea gráfico de peso del paciente"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_disease__age
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__age
msgid "Patient age at the moment of the diagnosis. Can be estimative"
msgstr "Edad del paciente en el momento del diagnóstico. Puede ser estimativo"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_hms_prescription_document
msgid "Patient is Pregnant"
msgstr "Paciente está embarazada"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid "Patient:"
msgstr "Paciente:"

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.action_menu_patient
msgid "Patients"
msgstr "Pacientes"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__pause
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Pause"
msgstr "Pausa"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__pause_date_start
msgid "Pause Start Date"
msgstr "Pausa fecha de inicio"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__pause_date_end
msgid "Pause end Date"
msgstr "Pausa fecha de finalización"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__pause_duration
msgid "Paused Time"
msgstr "Tiempo en pausa"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_physician_form
msgid "Personal Information"
msgstr "Información personal"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hms_physician
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__physician_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__physician_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__physician_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__doctor
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__physician_id
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group__physician_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Physician"
msgstr "Profesional"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_medication__doctor
msgid "Physician who prescribed the medicament"
msgstr "Médico que le recetó el medicamento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_disease__physician_id
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__physician_id
msgid "Physician who treated or diagnosed the patient"
msgstr "Médico que trató o diagnosticó al paciente"

#. module: acs_hms
#: model_terms:ir.actions.act_window,help:acs_hms.action_resource_calendar
msgid "Physician working hours and time table."
msgstr "El profesional está trabajando en ese horario."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__physician_id
msgid "Physician's Name"
msgstr "Nombre del médico"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid "Physician:"
msgstr "Profesional:"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_resource_calendar__physician_ids
#: model:ir.ui.menu,name:acs_hms.action_menu_doctors
msgid "Physicians"
msgstr "Médicos"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Pregancy Warning"
msgstr "Advertencia de embarazo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__pregnancy_warning
msgid "Pregnancy Warning"
msgstr "Advertencia de embarazo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__pregnancy_warning
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__pregnancy_warning
msgid "Pregnancy warning"
msgstr "Advertencia de embarazo"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__prescription_order__state__prescription
msgid "Prescribed"
msgstr "Recetado"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Prescribed Medicine"
msgstr "Medicina prescrita"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__physician_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Prescribing Doctor"
msgstr "Médico prescriptor"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__prescription_line_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__prescription_id
#: model:ir.ui.menu,name:acs_hms.hos_pres_root
#: model:ir.ui.menu,name:acs_hms.hos_prescption_inner_root
#: model:ir.ui.menu,name:acs_hms.hos_prescription
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Prescription"
msgstr "Prescripción"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Prescription Authentication"
msgstr "Autenticación de la Prescripción"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__prescription_date
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Prescription Date"
msgstr "Fecha de prescripción"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
msgid "Prescription ID"
msgstr "ID Receta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_tree
msgid "Prescription Line"
msgstr "Línea de receta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
msgid "Prescription Lines"
msgstr "Lineas de Receta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__notes
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Prescription Notes"
msgstr "Notas de Receta Médica"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__name
msgid "Prescription Number"
msgstr "Número de receta"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_prescription_order__name
msgid "Prescription Number of this prescription"
msgstr "Número de prescripción para esta receta"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.act_open_hms_prescription_order_view
#: model:ir.actions.report,name:acs_hms.report_hms_prescription_id
#: model:ir.model,name:acs_hms.model_prescription_order
msgid "Prescription Order"
msgstr "Orden de prescripción"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_prescription_line
msgid "Prescription Order Line"
msgstr "Línea de orden de receta"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_prescription_pivot
msgid "Prescription Orders"
msgstr "Seguimiento de la Receta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__prescription_line_ids
msgid "Prescription line"
msgstr "Línea de receta"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__prescription_ids
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_prescription_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_physician_form
msgid "Prescriptions"
msgstr "Prescripciones"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Preview Documents"
msgstr "Vista previa de documentos"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__pricelist_id
msgid "Pricelist"
msgstr "Tarifa"

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.menu_product_pricelist_main
msgid "Pricelists"
msgstr "Tarifas"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__attending_physician_ids
msgid "Primary Doctors"
msgstr "Doctor principal"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__is_primary_surgeon
#: model_terms:ir.ui.view,arch_db:acs_hms.view_physician_search
msgid "Primary Surgeon"
msgstr "Cirujano de cabecera"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__prnt
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__prnt
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
msgid "Print"
msgstr "Imprimir"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__acs_prescription_qrcode
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__acs_prescription_qrcode
msgid "Print Authetication QrCode on Presctiprion"
msgstr "Imprimir Autenticación QR en Prescripción"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__product_template__hospital_product_type__procedure
msgid "Procedure"
msgstr "Procedimiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__product_id
msgid "Product"
msgstr "Producto"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_diseases__protein
#: model_terms:ir.ui.view,arch_db:acs_hms.view_diseases_search
msgid "Protein involved"
msgstr "Proteína involucrada"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleado Público"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__purpose_id
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Purpose"
msgstr "Propósito"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__qr_image
msgid "QR Code"
msgstr "Código QR"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "Qty"
msgstr "Ctd"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_tree
msgid "Qty Dose"
msgstr "Ctd. Dosis"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__qty_per_day
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__qty_per_day
msgid "Qty Per Day"
msgstr "Ctd Por día"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
msgid "Qty. Per Day"
msgstr "Ctd. Por día"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_search
msgid "Quantity"
msgstr "Cantidad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__rr
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__rr
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__rr
msgid "RR"
msgstr "FR"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "RR:"
msgstr "FR:"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Radiological"
msgstr "Imagenología"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__radiological_report
msgid "Radiological Report"
msgstr "Informe radiológico"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Radiological Report..."
msgstr "Anotaciones importantes sobre el informe o estudios realizados…"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_cancel_reason__cancel_reason
msgid "Reason"
msgstr "Motivo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_medication__discontinued_reason
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_patient_medication_search
msgid "Reason for discontinuation"
msgstr "Motivo para la interrupción del tratamiento"

#. module: acs_hms
#: model:res.groups,name:acs_hms.group_hms_receptionist
msgid "Receptionist"
msgstr "Recepcionista"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Refer. To"
msgstr "Referir a"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_partner_form
msgid "Refering Doctor"
msgstr "Médico de referencia"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_referring_doctors
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__ref_doctor_ids
#: model:ir.ui.menu,name:acs_hms.menu_physician_referring_physicians
#: model:ir.ui.menu,name:acs_hms.menu_referring_doctors
#: model_terms:ir.ui.view,arch_db:acs_hms.view_res_partner_filter
msgid "Referring Doctors"
msgstr "Médicos de Referencia"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_account_bank_statement_line__ref_physician_id
#: model:ir.model.fields,field_description:acs_hms.field_account_move__ref_physician_id
#: model:ir.model.fields,field_description:acs_hms.field_account_payment__ref_physician_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__ref_physician_id
#: model:ir.model.fields,help:acs_hms.field_account_bank_statement_line__ref_physician_id
#: model:ir.model.fields,help:acs_hms.field_account_move__ref_physician_id
#: model:ir.model.fields,help:acs_hms.field_account_payment__ref_physician_id
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__ref_physician_id
msgid "Referring Physician"
msgstr "Médico referente"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Referring physician"
msgstr "Médico referente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__invoice_id
msgid "Registration Invoice"
msgstr "Factura de registro"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_res_company__patient_registration_product_id
#: model:ir.model.fields,help:acs_hms.field_res_company__treatment_registration_product_id
#: model:ir.model.fields,help:acs_hms.field_res_config_settings__patient_registration_product_id
#: model:ir.model.fields,help:acs_hms.field_res_config_settings__treatment_registration_product_id
msgid "Registration Product"
msgstr "Producto de registro"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__registration_product_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__registration_product_id
#: model:product.product,name:acs_hms.hms_registration_service_0
#: model:product.template,name:acs_hms.hms_registration_service_0_product_template
msgid "Registration Service"
msgstr "Servicio de registro"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_acs_cancel_reason
msgid "Reject Reason"
msgstr "Motivo de rechazo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__physician_ids
#: model:ir.model.fields,field_description:acs_hms.field_res_users__physician_ids
msgid "Related Physician"
msgstr "Médico relacionado"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_member__relation_id
msgid "Relation"
msgstr "Relación"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_family_diseases__relative
msgid "Relative"
msgstr "Relativo"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
msgid "Reset to Draft"
msgstr "Cambiar a borrador"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__rr
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__rr_old
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__rr
#: model:ir.model.fields,help:acs_hms.field_hms_patient__rr
msgid "Respiratory Rate"
msgstr "Frecuencia respiratoria"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__responsible_id
msgid "Responsible Jr. Doctor"
msgstr "Responsable Jr. Doctor"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__activity_user_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__activity_user_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__activity_user_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__route_id
msgid "Route"
msgstr "Vía"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__state__running
msgid "Running"
msgstr "Corriendo"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.action_menu_working_schedule
msgid "Schedule"
msgstr "Agenda"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Search Appointments"
msgstr "Buscar Citas"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Search Treatments"
msgstr "Buscar Tratamientos"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_diseases__category
msgid ""
"Select the category for this disease This is usuallyassociated to the "
"standard. For instance, the chapter on the ICD-10will be the main category "
"for the disease"
msgstr ""
"Seleccione la categoría para esta enfermedad. Esto generalmente está "
"asociado a la norma. Por ejemplo, el capítulo sobre la CIE-10 será la "
"categoría principal para la enfermedad"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_form
msgid "Send by Email"
msgstr "Enviar por mail"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_family_relation__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid ""
"Set days to consider next appointment as follow-up if less than given days."
msgstr ""
"Establezca los días para considerar la próxima cita como seguimiento si son "
"menos de los días dados."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Set this service as default Consultation Service."
msgstr "Establezca este servicio como servicio de consulta predeterminado."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Set this service as default Follow-up Service."
msgstr "Establezca este servicio como servicio de seguimiento predeterminado."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Set this service as default service for Patient Registration Invoice."
msgstr ""
"Establezca este servicio como servicio predeterminado para factura de "
"registro de pacientes."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Set this service as default service for Treatment Registration Invoice."
msgstr ""
"Establezca este servicio como servicio predeterminado para la factura de "
"registro de tratamiento."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Set to manage invoicing option on appointment."
msgstr "Establézcalo para administrar la opción de facturación en la cita."

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.menu_hms_cofig_settings
msgid "Settings"
msgstr "Ajustes"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__disease_severity__severe
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_severity__severe
msgid "Severe"
msgstr "Grave"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__disease_severity
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__disease_severity
msgid "Severity"
msgstr "Severidad"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_medicament_group_line__short_comment
#: model:ir.model.fields,help:acs_hms.field_prescription_line__short_comment
msgid "Short comment on the specific drug"
msgstr "Breve comentario sobre el medicamento específico"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_medication__discontinued_reason
msgid "Short description for discontinuing the treatment"
msgstr "Breve descripción para descontinuar el tratamiento"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_appointment_document
msgid ""
"Should you have any questions please contact us at your convenience.<br/>\n"
"                <br/>\n"
"                Best regards,<br/>"
msgstr ""
"Si tiene alguna pregunta, por favor contáctenos a su conveniencia.<br/>\n"
"                <br/>\n"
"                Atentamente,<br/>"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Show Evaluation Charts"
msgstr "Mostrar Gráficos de Evaluación"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_patient_medication__adverse_reaction
msgid "Side effects or adverse reactions that the patient experienced"
msgstr "Efectos secundarios o reacciones adversas que experimentó el paciente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_physician__signature
#: model_terms:ir.ui.view,arch_db:acs_hms.view_physician_form
msgid "Signature"
msgstr "Firma"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__sister
msgid "Sister"
msgstr "Hermana"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__spo2
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__spo2
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__spo2
msgid "SpO2"
msgstr "SpO2"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "SpO2:"
msgstr "SpO2:"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_diseases__code
msgid "Specific Code for the Disease (eg, Code for ICD-10)"
msgstr ""
"Código Específico para la Enfermedad (por ejemplo, Código para la CIE-10)"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
msgid "Sr.No"
msgstr "No.Rx"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Start"
msgstr "Inicio"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__date_start
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__state
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__state
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__state
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__state
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__state
msgid "State"
msgstr "Estado"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__activity_state
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__activity_state
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__activity_state
#: model:ir.model.fields,help:acs_hms.field_prescription_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__status
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__disease_status
msgid "Status of the disease"
msgstr "Estado de la enfermedad"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__appointment_stock_location_id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__appointment_stock_location_id
msgid "Stock Location for Consumed Products in Appointment"
msgstr "Ubicación de stock para productos consumidos en cita"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de Stock"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_hms_patient_disease
msgid "Systematic Examination"
msgstr "Evaluación sistémica"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__systolic_bp
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__systolic_bp
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__systolic_bp
msgid "Systolic BP"
msgstr "PA Sistólica "

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_patient_evaluation_popup_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
msgid "Systolic/Diastolic BP"
msgstr "PA"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__temp
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__temp
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__temp
msgid "Temp"
msgstr "Temp."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "Temp:"
msgstr "Temp:"

#. module: acs_hms
#: model:ir.model.constraint,message:acs_hms.constraint_acs_family_relation_name_uniq
msgid "The Relation must be unique!"
msgstr "¡La Relación debe ser única!"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__chief_complain
msgid ""
"The concise statement describing the symptom, problem, condition, diagnosis, "
"physician-recommended return, or other reason for a medical encounter."
msgstr ""
"La declaración concisa que describe el síntoma, problema, condición, "
"diagnóstico, retorno recomendado por el médico u otra razón para un "
"encuentro médico."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__differencial_diagnosis
msgid ""
"The process of weighing the probability of one disease versus that of other "
"diseases possibly accounting for a patient's illness."
msgstr ""
"El proceso de sopesar la probabilidad de una enfermedad frente a la de otras "
"enfermedades que posiblemente representan la enfermedad de un paciente."

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__medical_advice
msgid ""
"The provision of a formal professional opinion regarding what a specific "
"individual should or should not do to restore or preserve health."
msgstr ""
"La provisión de una opinión profesional formal con respecto a lo que un "
"individuo específico debe o no hacer para restaurar o preservar la salud."

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient__grpah_data_filter__month
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid "This Month"
msgstr "Este mes"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient__grpah_data_filter__week
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid "This Week"
msgstr "Esta semana"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient__grpah_data_filter__year
msgid "This Year"
msgstr "Este año"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_res_company__birthday_mail_template_id
msgid "This will set the default mail template for birthday wishes."
msgstr ""
"Esto establecerá la plantilla de correo predeterminada para los deseos de "
"cumpleaños."

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
msgid "Till Now"
msgstr "Hasta Ahora"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__to_invoice
msgid "To Invoice"
msgstr "A facturar"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient__grpah_data_filter__today
#: model_terms:ir.ui.view,arch_db:acs_hms.patient_evalution_kanban_view
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Today"
msgstr "Hoy"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Today's Prescription"
msgstr "Receta de hoy"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_group_line__quantity
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_line_tree
msgid "Total Qty"
msgstr "Ctd Total"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_medicament_dosage__qty_per_day
msgid "Total Qty Per Day"
msgstr "Ctd Total por Día"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_prescription_table
msgid "Total Quantity"
msgstr "Cantidad Total"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.acs_action_form_hospital_treatment
#: model:ir.model,name:acs_hms.model_hms_treatment
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__treatment_id
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__treatment_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__treatment_id
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__treatment_id
#: model:ir.model.fields.selection,name:acs_hms.selection__account_move__hospital_invoice_type__treatment
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_prescription_order_search
msgid "Treatment"
msgstr "Tratamiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__department_type
msgid "Treatment Department"
msgstr "Departamento del Tratamiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient_disease__description
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__description
msgid "Treatment Description"
msgstr "Descripción del tratamiento"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__treatment_id
#: model:ir.model.fields,help:acs_hms.field_hms_patient_disease__treatment_id
msgid "Treatment Id"
msgstr "Id del tratamiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__group_treatment_invoicing
#: model:res.groups,name:acs_hms.group_treatment_invoicing
#: model_terms:ir.ui.view,arch_db:acs_hms.res_config_settings_view_form
msgid "Treatment Invoicing"
msgstr "Facturación del tratamiento"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__treatment_registration_product_id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__treatment_registration_product_id
msgid "Treatment Registration Invoice Product"
msgstr "Producto de la factura de registro del tratamiento"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_tree
msgid "Treatment Sheet"
msgstr "Hoja de tratamiento"

#. module: acs_hms
#: model:ir.ui.menu,name:acs_hms.action_menu_treatment
#: model:ir.ui.menu,name:acs_hms.action_menu_treatment_sheet
#: model_terms:ir.ui.view,arch_db:acs_hms.view_acs_treatment_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_physician_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_treatment_pivot
msgid "Treatments"
msgstr "Tratamientos"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms.field_prescription_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__status__unchanged
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_status__unchanged
msgid "Unchanged"
msgstr "Sin cambios"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_family_diseases__relative__uncle
msgid "Uncle"
msgstr "Tío"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_treatment_search
msgid "Under Lactation"
msgstr "Lactando"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__unique_code
msgid "Unique UID"
msgstr "UID único"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_prescription_line__quantity
#: model:product.product,uom_name:acs_hms.hms_consultation_service_0
#: model:product.product,uom_name:acs_hms.hms_followup_service_0
#: model:product.product,uom_name:acs_hms.hms_registration_service_0
#: model:product.template,uom_name:acs_hms.hms_consultation_service_0_product_template
#: model:product.template,uom_name:acs_hms.hms_followup_service_0_product_template
#: model:product.template,uom_name:acs_hms.hms_registration_service_0_product_template
msgid "Units"
msgstr "Unidades"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_unread
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_unread
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_unread
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de mensajes sin leer"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__urgency
msgid "Urgency Level"
msgstr "Nivel de urgencia"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__urgency__urgent
msgid "Urgent"
msgstr "Urgente"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_search
msgid "Urgent Appointment"
msgstr "Cita Urgente"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_res_company__appointment_usage_location_id
#: model:ir.model.fields,field_description:acs_hms.field_res_config_settings__appointment_usage_location_id
msgid "Usage Location for Consumed Products in Appointment"
msgstr "Ubicación de uso de los productos consumidos en la cita"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hospital_hms_treatment_form
msgid "View Invoice"
msgstr "Ver factura"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__waiting_duration
msgid "Wait Time"
msgstr "Tiempo de espera"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__waiting_duration_timer
msgid "Wait Timer"
msgstr "Tiempo de espera"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_appointment__state__waiting
#: model_terms:ir.ui.view,arch_db:acs_hms.view_hms_appointment_form
msgid "Waiting"
msgstr "Esperando"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__waiting_date_start
msgid "Waiting Start Date"
msgstr "Fecha de inicio de espera"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__waiting_date_end
msgid "Waiting end Date"
msgstr "Fecha de finalización de espera"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__website_message_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__website_message_ids
#: model:ir.model.fields,field_description:acs_hms.field_hms_treatment__website_message_ids
#: model:ir.model.fields,field_description:acs_hms.field_prescription_order__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__website_message_ids
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__website_message_ids
#: model:ir.model.fields,help:acs_hms.field_hms_treatment__website_message_ids
#: model:ir.model.fields,help:acs_hms.field_prescription_order__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: acs_hms
#: model:ir.model.fields,field_description:acs_hms.field_acs_patient_evaluation__weight
#: model:ir.model.fields,field_description:acs_hms.field_hms_appointment__weight
#: model:ir.model.fields,field_description:acs_hms.field_hms_patient__weight
msgid "Weight"
msgstr "Peso"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_acs_patient_evaluation__weight
#: model:ir.model.fields,help:acs_hms.field_hms_appointment__weight
#: model:ir.model.fields,help:acs_hms.field_hms_patient__weight
msgid "Weight in KG"
msgstr "Peso en KG"

#. module: acs_hms
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_evaluation_document
#: model_terms:ir.ui.view,arch_db:acs_hms.report_acs_medical_advice_document
msgid "Weight:"
msgstr "Peso:"

#. module: acs_hms
#: model:ir.model,name:acs_hms.model_resource_calendar
msgid "Working Schedule"
msgstr "Programación de trabajo"

#. module: acs_hms
#: model:ir.actions.act_window,name:acs_hms.action_resource_calendar
#: model:ir.ui.menu,name:acs_hms.menu_working_schedule
msgid "Working Times"
msgstr "Horarios de trabajo"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_patient_disease__status__worsening
#: model:ir.model.fields.selection,name:acs_hms.selection__hms_treatment__disease_status__worsening
msgid "Worsening"
msgstr "Empeorando"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_hms_diseases__chromosome
msgid "chromosome number"
msgstr "Número de cromosoma"

#. module: acs_hms
#: model:ir.model.fields,help:acs_hms.field_disease_gene__gene_id
msgid "default code from NCBI Entrez database."
msgstr "Código por defecto desde NCBI Entrez database."

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__disease_gene__dominance__d
msgid "dominant"
msgstr "Dominante"

#. module: acs_hms
#: model:ir.model.fields.selection,name:acs_hms.selection__disease_gene__dominance__r
msgid "recessive"
msgstr "Recesivo"

// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
@mixin form-chatter-designs{
    .o_action_manager {
        .o_view_controller.o_form_view {
            .o_content {
                display: block;
                height: 100%;
                .o_form_renderer:not(.o_form_nosheet){
                    flex-flow: wrap !important;
                }
                .o_form_sheet_bg {
                    .o_form_statusbar {
                        margin: 0;
                    }
                    width: 100%;
                    min-width: 100% !important;
                    // padding: 0;
                }
            }
            .o-mail-Form-chatter {
                width: 100%;
                margin: 15px;
                padding-bottom: 1rem !important;
                flex-basis: 100%;
                scrollbar-width: none; // hide scrollbar in firefox
                &::-webkit-scrollbar {
                    display: none;
                }

                &.o_Chatter {
                    height: 100%;
                }
                .o_Chatter_scrollPanel {
                    scrollbar-width: none; // hide scrollbar in firefox
                    &::-webkit-scrollbar {
                        display: none;
                    }
                }
            }
        }

    }
    // check this in repsonsive
    .o_form_view {
        .o-mail-Form-chatter {
            border: 0;
            max-width: 100% !important;
            // padding: 0 !important;
            margin-bottom: 1rem;
        }
    }
}

body.o_web_client {
    .o_Message{
        background-color: transparent;
    }
    .o_Attachment{
        &.o-has-card-details{
            color: #121212;
        }
    }
    .o-mail-Form-chatter{
        .o_ChatterContainer{
            .o_ChatterTopbar_actions{
                *{
                    border: 0px !important;
                }
                .btn{
                    padding: 0.6rem 1rem !important;
                    background-color: transparent !important;
                    border: none !important;
                    color:var(--biz-theme-primary-color) !important;
                }
            }
        }
    }
    &.chatter_bottom {
        .o_form_view_container{
            .o_control_panel{
                margin-bottom: 8px !important;
            }
        }
        &.hasAttachment{
            .o_form_view_container{
                .o_form_sheet_bg{
                    .o_list_table{
                        width: auto;
                    }
                }
            }
        }
        &:not(.hasAttachment){
            @include form-chatter-designs;
            .o_view_controller.o_form_view{
                flex-direction: column !important;
                overflow: auto;
                .o_form_view_container{
                    width:100% !important;
                }
            }
            .oe_chatter{
                --formView-sheetBg-padding-x: 16px;
                padding-left: var(--formView-sheetBg-padding-x) !important;
                padding-right: var(--formView-sheetBg-padding-x) !important;
                padding-top: 8px !important;
                padding-bottom: 8px;
            }
        }
    }

    &.top_menu_vertical, &.top_menu_vertical_mini, &.top_menu_vertical_mini_2{
        @include media-breakpoint-down(xl) {
            &.chatter_right {
                .o_form_renderer{
                    .o-mail-Form-chatter{
                        padding-left: 16px !important;
                        padding-right: 16px !important;
                        padding-top: 8px !important;
                    }
                }
            }
        }
        @include media-breakpoint-down(xxl) {
            &.chatter_right {
                @include form-chatter-designs;
            }
        }
        @include media-breakpoint-up(xxl){
            .o_action_manager{
                .o_view_controller.o_form_view{
                    .o-mail-Form-chatter{
                        .o_ChatterTopbar_actions{
                            flex-wrap: wrap;
                            & > div{
                                flex-wrap: wrap;
                            }
                        }
                        &.o-aside{
                            flex-shrink: unset !important;
                        }
                    }
                }
            }
        }
        &.pinned{
            @include media-breakpoint-down(xxl){
                .o_action_manager{
                    .o_view_controller.o_form_view{
                        flex-direction: column !important;
                        overflow: auto;
                        .o_form_view_container{
                            width: 100%
                        }
                        .o-mail-Form-chatter{
                            width: 100%
                        }
                    }
                }
                
            }
        }
        &:not(.pinned){
            @include media-breakpoint-down(xxl){
                .o_action_manager{
                    .o_view_controller.o_form_view{
                        flex-direction: column !important;
                        overflow: auto;
                        .o_form_view_container{
                            width: 100%
                        }
                        .o-mail-Form-chatter{
                            width: 100%
                        }
                    }
                }
                
            }
        }
    }
  
    &.top_menu_horizontal{
        @include media-breakpoint-down(lg) {
            &.chatter_right {
                @include form-chatter-designs;
                .o-mail-Form-chatter{
                    margin-top: 15px !important;
                }
            }
        }
        @include media-breakpoint-down(xxl){
            .o_action_manager{
                .o_view_controller.o_form_view{
                    flex-direction: column !important;
                    overflow: auto;
                    .o_form_view_container{
                        width: 100%
                    }
                    .o-mail-Form-chatter{
                        width: 100%
                    }
                }
            }
            
        }
    }
    &.tree_form_split_view{
        .o_view_controller.tree-form-viewer {
            > .o_content > .o_form_view {
                display: block;
                height: 100%;

                .o_form_sheet_bg {
                    .o_form_statusbar {
                        margin: 0;
                    }
                    width: 100%;
                    padding: 0;
                }

                .o-mail-Form-chatter {
                    min-height: 100%;
                    width: 100%;
                    // margin-left: 15px;
                    margin: 0;
                    padding-bottom: 1rem;
                    flex-basis: 100%;
                    padding-left: 1rem;
                    scrollbar-width: none; // hide scrollbar in firefox
                    &::-webkit-scrollbar {
                        display: none;
                    }

                    .o_Chatter {
                        height: 100%;
                    }

                    .o_Chatter_scrollPanel {
                        scrollbar-width: none; // hide scrollbar in firefox
                        &::-webkit-scrollbar {
                            display: none;
                        }
                    }
                }
            }
        }
    } 
}
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_certification
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-14 06:05+0000\n"
"PO-Revision-Date: 2022-03-14 06:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.certificate_report_document
msgid "<i><u><strong>Certificate</strong></u></i>"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_needaction
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_needaction
msgid "Action Needed"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_ids
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_ids
msgid "Activities"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_state
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_state
msgid "Activity State"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_type_icon
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_attachment_count
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: acs_certification
#: model:ir.actions.report,name:acs_certification.report_certificate_management
#: model:ir.ui.menu,name:acs_certification.menu_certificate
#: model:ir.ui.menu,name:acs_certification.menu_certificate_management
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_form
msgid "Certificate"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__certificate_content
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__certificate_content
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_form
msgid "Certificate Content"
msgstr ""

#. module: acs_certification
#: model:res.groups,name:acs_certification.group_certificate_manager
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_search
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_tree
msgid "Certificate Management"
msgstr ""

#. module: acs_certification
#: model:ir.model,name:acs_certification.model_certificate_tag
msgid "Certificate Tags"
msgstr ""

#. module: acs_certification
#: model:ir.actions.act_window,name:acs_certification.action_certificate_template_view
#: model:ir.model,name:acs_certification.model_certificate_template
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__template_id
#: model:ir.ui.menu,name:acs_certification.menu_certificate_template
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_template_form
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_template_search
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_template_tree
msgid "Certificate Template"
msgstr ""

#. module: acs_certification
#: model:ir.actions.act_window,name:acs_certification.action_certificate_management
#: model_terms:ir.ui.view,arch_db:acs_certification.view_partner_form
msgid "Certificates"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__tag_ids
msgid "Classify and analyze your Certificates"
msgstr ""

#. module: acs_certification
#: model_terms:ir.actions.act_window,help:acs_certification.action_certificate_template_view
msgid "Click to add Certificate Template."
msgstr ""

#. module: acs_certification
#: model_terms:ir.actions.act_window,help:acs_certification.action_certificate_management
msgid "Click to add a Certificate."
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__color
msgid "Color Index"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__company_id
msgid "Company"
msgstr ""

#. module: acs_certification
#: model:ir.ui.menu,name:acs_certification.menu_certificate_configuration
msgid "Configuration"
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_template_form
msgid "Content"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__create_uid
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__create_uid
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__create_uid
msgid "Created by"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__create_date
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__create_date
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__create_date
msgid "Created on"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__date
msgid "Date"
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.certificate_report_document
msgid "Date. :"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__display_name
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__display_name
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__display_name
msgid "Display Name"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields.selection,name:acs_certification.selection__certificate_management__state__done
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_form
msgid "Done"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields.selection,name:acs_certification.selection__certificate_management__state__draft
msgid "Draft"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_follower_ids
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_follower_ids
msgid "Followers"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_partner_ids
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__activity_type_icon
#: model:ir.model.fields,help:acs_certification.field_certificate_template__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_form
msgid "Get Data"
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.certificate_report_document
msgid "Given By:"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__has_message
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__has_message
msgid "Has Message"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__id
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__id
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__id
msgid "ID"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_exception_icon
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__activity_exception_icon
#: model:ir.model.fields,help:acs_certification.field_certificate_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_needaction
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_unread
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_needaction
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_has_error
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_has_sms_error
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_has_error
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_is_follower
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_digest_digest__kpi_acs_certification_total_value
msgid "Kpi Acs Certification Total Value"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management____last_update
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag____last_update
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template____last_update
msgid "Last Modified on"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__write_uid
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__write_uid
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__write_uid
msgid "Last Updated by"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__write_date
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__write_date
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__write_date
msgid "Last Updated on"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_has_error
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_ids
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_ids
msgid "Messages"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__my_activity_date_deadline
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__name
#: model:ir.model.fields,field_description:acs_certification.field_certificate_tag__name
msgid "Name"
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.certificate_report_document
msgid "Name :"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_digest_digest__kpi_acs_certification_total
msgid "New Certification"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_calendar_event_id
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_date_deadline
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_summary
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_type_id
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_needaction_counter
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_has_error_counter
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_needaction_counter
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_has_error_counter
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__message_unread_counter
#: model:ir.model.fields,help:acs_certification.field_certificate_template__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__partner_id
msgid "Partner"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__partner_id
msgid "Partner to whome certificate asigned"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__print_header_in_report
msgid "Print Header"
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.certificate_report_document
msgid "Reg No:"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__activity_user_id
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_has_sms_error
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__state
msgid "Status"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__activity_state
#: model:ir.model.fields,help:acs_certification.field_certificate_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: acs_certification
#: model_terms:ir.ui.view,arch_db:acs_certification.view_certificate_management_search
msgid "Tag"
msgstr ""

#. module: acs_certification
#: model:ir.model.constraint,message:acs_certification.constraint_certificate_tag_name_uniq
msgid "Tag name already exists !"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__tag_ids
msgid "Tags"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__name
msgid "Template"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__activity_exception_decoration
#: model:ir.model.fields,help:acs_certification.field_certificate_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_unread
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_unread
msgid "Unread Messages"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__message_unread_counter
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__user_id
msgid "User"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__user_id
msgid "User who provided certificate"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,field_description:acs_certification.field_certificate_management__website_message_ids
#: model:ir.model.fields,field_description:acs_certification.field_certificate_template__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: acs_certification
#: model:ir.model.fields,help:acs_certification.field_certificate_management__website_message_ids
#: model:ir.model.fields,help:acs_certification.field_certificate_template__website_message_ids
msgid "Website communication history"
msgstr ""

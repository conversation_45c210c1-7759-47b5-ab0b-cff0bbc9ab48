<!-- # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
# See LICENSE file for full copyright and licensing details. -->
<templates id="template" xml:space="preserve">
	<t t-name="SearchModalPopup" >
		<div class="modal" id="search_bar_modal">
			<div class="modal-dialog modal-dialog-scrollable">
				<div class="modal-content p-0">
					<!-- <PERSON><PERSON>er -->
					<div class="modal-header d-block">
						<div class="d-flex px-3 align-items-center justify-content-between">
							<h4 class="modal-title">Search..</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal">
								<!-- <span class="ri ri-close-line align-middle"></span> -->
							</button>
						</div>
					</div>
					<div class="modal-body px-0">
						<!-- Radio buttons for Menu and Records options -->
						<div class="d-flex align-items-center mb-4 px-3">
							<div class="form-check form-check-inline">
								<input class="form-check-input" type="radio" name="searchOption" id="searchMenu" value="menu" checked="checked"/>
								<label class="form-check-label" for="searchMenu" style="padding-top: 1px;">Menu</label>
							</div>
							<!-- T10246 start -->
							<div class="form-check form-check-inline">
								<input class="form-check-input" type="radio" name="searchOption" id="searchRecords" value="records"/>
								<label class="form-check-label" for="searchRecords" style="padding-top: 1px;">Records</label>
							</div>
							<!-- T10246 end -->
						</div>

						<div class="d-flex align-items-center mb-4 px-3">
							<div class="load-active-menu-selector w-25">
								<t t-call="seletor-acvtive-menu-items"/>
								<t t-call="seletor-acvtive-records-global-search"/>
							</div>
							<input type="text" id="searchPagesInput" 
								class="form-control form-control-size borderless auto-complete" autocomplete="off" 
								placeholder="Search in Menu..." value="" style="font-size: 16px; padding-left: 1px !important;"/>
						</div>
						<ul id="searchPagesResults" class="auto-complete-result mb-0 px-3">
						</ul>
						<div class="searchNoResult px-3 d-none">
							<h3>No Results Found!</h3>
						</div>
					</div>
				</div>
			</div>
		</div>
	</t>

	<t t-name="seletor-acvtive-menu-items">
		<div class="form-group mb-0">
		  <select class="form-control border-0 border-bottom p-0 text-truncate" id="active-menu-categories" style="font-size: 16px; padding-left: 1px !important;">
			<option id="0" value="all">All</option>
			<t t-foreach="menuService.getApps()" t-as="menu" t-key="menu.name">
				<option t-att-id="menu.id" t-att-value="menu.name"><t t-esc="menu.name"/></option>
			</t>
		  </select>
		</div>
	</t>

	<t t-name="seletor-acvtive-records-global-search">
		<div class="form-group mb-0">
			<select class="form-control border-0 border-bottom p-0 text-truncate d-none" id="active-records-global-search" style="font-size: 16px; padding-left: 1px !important;">
			</select>
		</div>
	</t>

	<!-- A Menu search result -->
	<t t-name="spiffy_theme_backend.MenuSearchResults">
		<t t-foreach="results" t-as="result" t-key="result.id">
			<t t-set="menu" t-value="widget._menuInfo(result)" />
			<li class="search_list_content list-unstyled">
				<t t-if="menu.actionPath" t-set="url" t-value="'/odoo/' + menu.actionPath"/>
				<t t-else=" " t-set="url" t-value="'/odoo/action-' + menu.actionID"/>
				<!-- <a
					t-attf-class="autoComplete_highlighted text-small text-muted mb-0"
					t-attf-href="#menu_id={{menu.id}}&amp;action={{menu.actionID}}"
					t-att-data-menu-id="menu.id"
					t-att-data-action-id="menu.actionID"
					t-att-data-action-path="url"
					t-esc="result"
					role="menuitem"
				/> -->
				<a
					t-attf-class="autoComplete_highlighted text-small text-muted mb-0"
					t-att-href="url"
					t-att-data-menu-id="menu.id"
					t-att-data-action-id="menu.actionID"
					t-esc="result"
					role="menuitem"
				/>
			</li>
		</t>
	</t>

	<!-- T10246 start -->
	<!-- A Records search result -->
	<t t-name="spiffy_theme_backend.RecordSearchResults">
		<!-- <t t-foreach="results" t-as="result" t-key="result.id">
			<li class="search_list_content list-unstyled">
				<a
					t-attf-class="autoComplete_highlighted text-small text-muted mb-0"
					t-attf-href="/web/#id={{result['id']}}&amp;model={{result['model']}}&amp;view_type=form"
					t-att-data-rec-id="result['id']"
					t-att-data-action-id="result['actionID']"
					t-esc="result['display_name']"
					role="button"
				/>
			</li>
		</t> -->
		<t t-foreach="results" t-as="result" t-key="result.id">
			<t t-if="result['model']" t-set="url" t-value="'/odoo/' + result['model'] + '/' + result['id']"/>
			<t t-else=" " t-set="url" t-value="'/odoo/action-' +  result['actionID'] + '/' + result['id']"/>
			<li class="search_list_content list-unstyled">
				<a
					t-attf-class="autoComplete_highlighted text-small text-muted mb-0"
					t-att-href="url"
					t-att-data-rec-id="result['id']"
					t-att-data-action-id="result['actionID']"
					t-att-data-action-path="result['actionPath']"
					t-esc="result['display_name']"
					role="button"
				/>
			</li>
		</t>
	</t>
	<!-- T10246 end -->
	
	<t t-inherit="web.NavBar" t-inherit-mode="extension" >
		<xpath expr="//nav[hasclass('o_main_navbar')]" position="before">
			<!-- SPIFFY MULTI TAB START -->
			<div class="multi_tab_section"/>
			<!-- SPIFFY MULTI TAB END -->
			<div class="fav_app_island d-none">
				<span class="ri ri-apps-2-line fav_app_island_btn"/>
				<div class="fav_apps d-flex alige-items-center justify-content-center">
				</div>
			</div>
			<div class="float-end d-flex align-items-center header_menu_right_content flex-wrap">
				<li class="bookmark_list list-unstyled"></li>

				<li class="bookmark_section list-unstyled">
					<div class="dropdown">
						<button type="button" class="btn btn-light dropdown-toggle demo_btn" data-bs-toggle="dropdown" title="Add Bookmark">
							<span class="fa fa-bookmark-o" />
						</button>
						<div class="dropdown-menu bookmark_page_add">
							<p class="">Add Bookmark</p>
							<div class="form-group">
								<input type="text" class="form-control" name="bookmark_page_name" id="bookmark_page_name" />
							</div>
							<div class="dropdown-divider"></div>
							<div class="d-flex">
								<a class="btn btn-primary add_bookmark">Add</a>
								<a class="btn btn-secondary me-0 cancel_bookmark">Cancel</a>
							</div>
						</div>
					</div>
				</li>
				<li class="fav_app_drawer list-unstyled">
					<button type="button" class="btn btn-light fav_app_drawer_btn" title="Favourite Apps">
						<span class="ri ri-star-line"/>
					</button>
				</li>

				<li class="magnifier_section list-unstyled">
					<div class="">
						<button type="button" class="btn btn-light magnifier_btn demo_btn" data-bs-target="#magnifier" data-bs-toggle="collapse" title="Magnifier">
							<span class="fa fa-search-plus" />
						</button>
						<div id="magnifier" class="collapse position-absolute bg-white zoom-style-box">
							<div class="d-flex align-items-center">
								<span class="zoom_value ps-2">100</span>
								<span>%</span>
								<div class="zoom-style d-flex align-items-center mx-2">
									<a class="btn btn-small minus">-</a>
									<a class="btn btn-small plus">+</a>
								</div>
								<a class="fa fa-repeat zoom-reset btn btn-secondary reset"></a>
							</div>
						</div>
					</div>
				</li>
				<li class="search_view list-unstyled">
					<button type="button" class="btn btn-light search_bar demo_btn" accesskey="H" title="Search">
						<span class="fa fa-search" />
					</button>
				</li>
				<li class="fullscreen_section list-unstyled">
					<a class="btn btn-light full_screen demo_btn" accesskey="F" title="FullScreen">
						<span class="fa fa-expand" />
					</a>
				</li>
			</div>
			<div class="header_menu_right_content_toggler">
				<button type="button" class="btn btn-primary bookmark_panel_toggle" title="Bookmark Panel">
					<span class="ri ri-arrow-left-s-line"/>
				</button>
			</div>
			<!-- SEARCH MODAL -->
			<t t-call="SearchModalPopup"/>
			<div class="dynamic_data" />
			<div class="new_systray d-none">
				<div class="o_app_drawer d-block d-lg-none">
					<li class="list-unstyled">
						<a class="appDrawerToggle me-3" accesskey="A">
							<span class="ri ri-apps-2-line"/>
						</a>
					</li>
				</div>
				<div class="company_logo_brand d-none d-md-flex align-items-center">
					<div class="o_company_logo d-none d-md-block">
						<t t-if="currentCompany">
							<img class="img img-fluid company_logo" t-attf-src="/web/image?model=res.company&amp;field=backend_menubar_logo&amp;id={{currentCompany.id}}"/>
							<img class="img img-fluid company_logo_icon d-none" t-attf-src="/web/image?model=res.company&amp;field=backend_menubar_logo_icon&amp;id={{currentCompany.id}}"/>
						</t>
					</div>
					<div class="o_brand_name">
						<DropdownItem
							t-if="!env.isSmall and currentApp"
							t-esc="currentApp.name"
							class="'o_menu_brand d-flex'"
							onSelected="() => this.onNavBarDropdownItemSelection(currentApp)"
							attrs="{ href: getMenuItemHref(currentApp), 'data-menu-xmlid': currentApp.xmlid, 'data-section': currentApp.id }"
						/>
					</div>
				</div>
				<!-- Systray -->
				<div class="o_menu_systray" role="menu">
					<t t-foreach="systrayItems" t-as="item" t-key="item.key">
						<!-- This ensures the correct order of the systray items -->
						<div t-att-data-index="item.index"/>
						<ErrorHandler onError="error => this.handleItemError(error, item)">
							<t t-component="item.Component" t-props="item.props"/>
						</ErrorHandler>
					</t>
				</div>
			</div>
			<div class="navbar_to_do_list_data" />
		</xpath>
		<xpath expr="//nav[hasclass('o_main_navbar')]" position="replace">
			<nav class="o_main_navbar" t-on-dropdown-item-selected="onNavBarDropdownItemSelection" data-command-category="navbar">
				<div class="o_app_drawer">
					<li class="list-unstyled">
						<a class="appDrawerToggle me-3" accesskey="A">
							<span class="ri ri-apps-2-line"/>
						</a>
					</li>
				</div>
				<div class="o_company_logo">
					<t t-if="currentCompany">
						<img class="img img-fluid company_logo" t-attf-src="/web/image?model=res.company&amp;field=backend_menubar_logo&amp;id={{currentCompany.id}}"/>
						<img class="img img-fluid company_logo_icon d-none" t-attf-src="/web/image?model=res.company&amp;field=backend_menubar_logo_icon&amp;id={{currentCompany.id}}"/>
					</t>
				</div>

				<!-- Systray -->
				<div class="o_menu_systray" role="menu">

					<t t-foreach="systrayItems" t-as="item" t-key="item.key">
						<!-- This ensures the correct order of the systray items -->
						<div t-att-data-index="item.index"/>
						<ErrorHandler onError="error => this.handleItemError(error, item)">
							<t t-component="item.Component" t-props="item.props"/>
						</ErrorHandler>
					</t>
				</div>

				<!-- Apps Menu -->
				<t t-call="web.NavBar.AppsMenu">
					<t t-set="apps" t-value="menuService.getApps()" />
				</t>

				<div class="favorite_apps_section d-none"></div>

				<!-- <div class="mobile-header-toggle d-lg-none d-block">
					<a id="mobileMenuToggleBtn" class="menu-button">
						<span class="ri ri-menu-line align-middle"></span>
					</a>
				</div> -->

				<!-- App Brand -->
				<DropdownItem
					t-if="!env.isSmall and currentApp"
					t-esc="currentApp.name"
					class="'o_menu_brand d-flex'"
					onSelected="() => this.onNavBarDropdownItemSelection(currentApp)"
					attrs="{ href: getMenuItemHref(currentApp), 'data-menu-xmlid': currentApp.xmlid, 'data-section': currentApp.id }"
				/>

				<!-- Current App Sections -->
				<t t-if="currentAppSections.length and !env.isSmall and !isIpad" t-call="web.NavBar.SectionsMenu">
					<t t-set="sections" t-value="currentAppSections" />
				</t>
				<!-- Mobile App sections -->
				<t t-call="spiffy.Mobile.SectionsMenu">
					<t t-set="sections" t-value="currentAppSections" />
				</t>
			</nav>
		</xpath>
	</t>

	<t t-name="spiffy.Mobile.SectionsMenu" >
		<div class="o_menu_sections d-none">
			<a id="mobileMenuclose" class="menu-button" style="display: none;">
				<span class="ri ri-close-line align-middle"></span>
			</a>
			<t t-foreach="sections" t-as="first_level_menu" t-key="first_level_menu.id">
				<t t-if="!first_level_menu.childrenTree.length">
					<t t-call="spiffy.Menu.link">
						<t t-set="inNavbar" t-value="true"/>
						<t t-set="menu" t-value="first_level_menu"/>
					</t>
				</t>
				<t t-else="">
					<li class="list-unstyled">
						<a href="#" class="dropdown-toggle o-no-caret o_menu_header_lvl_1" t-att-data-menu-xmlid="first_level_menu.xmlid" data-bs-toggle="dropdown" data-display="static" role="button" aria-expanded="false">
							<t t-esc="first_level_menu.name"/>
						</a>
						<div class="dropdown-menu" role="menu">
							<t t-foreach="first_level_menu.childrenTree" t-as="second_level_menu" t-key="second_level_menu.id">
								<t t-call="spiffy.Menu.link">
									<t t-set="depth" t-value="1"/>
									<t t-set="menu" t-value="second_level_menu"/>
								</t>
							</t>
						</div>
					</li>
				</t>
			</t>
		</div>
	</t>

	<t t-name="spiffy.Menu.link" >
		<t t-set="depth" t-value="(depth || 0) + 1"/>
	
		<t t-if="!menu.childrenTree.length">
			<a role="menuitem" t-attf-href="#menu_id={{menu.id}}&amp;action={{menu.actionID ? menu.actionID : ''}}"
					t-att-data-menu="menu.id"
					t-att-data-action-model="menu.actionModel"
					t-att-data-action-id="menu.actionID"
					t-att-data-menu-xmlid="menu.xmlid"
					t-attf-class="{{inNavbar ? '' : 'dropdown-item '}}o_menu_entry_lvl_{{depth}}"
					>
					<span><t t-esc="menu.name"/></span>
			</a>
		</t>
		<t t-else="">
			<div t-attf-class="dropdown-header o_menu_header_lvl_{{depth}}">
				<span><t t-esc="menu.name"/></span>
			</div>
			<t t-foreach="menu.childrenTree" t-as="menu" t-key="menu.id">
				<t t-call="spiffy.Menu.link"/>
			</t>
		</t>
	</t>


	<t t-inherit="web.NavBar.AppsMenu.Sidebar" t-inherit-mode="extension" >
		 <xpath expr="//div[hasclass('o_sidebar_topbar')]" position="replace">
			<div class="o_sidebar_topbar d-flex align-items-center justify-content-between flex-shrink-0 py-0 fs-4">
              <small class="d-flex align-items-center justify-content-between ms-2">
              </small>
              <button class="o_sidebar_close oi oi-close btn d-flex align-items-center h-100 bg-transparent border-0 fs-2 text-reset" aria-label="Close menu" title="Close menu" t-on-click.stop="_closeAppMenuSidebar"/>
          </div>
		</xpath>
		
	</t>

	<t t-inherit="web.NavBar.AppsMenu" t-inherit-mode="extension" >
		<xpath expr="//Dropdown" position="replace">
			<div class="o_navbar_apps_menu all-apps-menus">
				<!-- The dynamic menu app is loaded in this div -->
			</div>
			<div class="o_navbar_apps_menu">
				<div class="appdrawer_section">
					<div class="top_navbar_section mb-3 mb-md-5">
						<a class="close_fav_app_btn d-none">
							<span class="ri ri-arrow-left-s-line"/>
						</a>
					</div>
					<div class="container">
						<div class="input-group">
							<div class="input-group-prepend d-flex">
								<span class="input-group-text">
									<i class="ri ri-search-line"></i>
								</span>
							</div>
							<input type="text" id="app_menu_search" class="form-control" placeholder="Search"/>
						</div>
						<div class="apps-list mt-3 mt-md-5">
							<!-- The dynamic menu app is loaded in this div -->
							<div class="spiffy-app-group"></div>
							<div id="searched_main_apps" class="d-none flex-wrap"></div>
							<div id="search_result"></div>
						</div>
						<div class="apps-list favourite_apps mt-3 mt-md-5 d-none">
							<div class="row m-0">
								<t t-foreach="menuService.getApps()" t-as="app" t-key="app.id">
									<div class="app-box mb16">
										<a role="menuitem" t-attf-href="#menu_id={{app.id}}&amp;action={{app.actionID ? app.actionID : ''}}" class="d-block o_app text-center text-center" t-att-data-menu-id="app.id" t-att-data-menu-xmlid="app.xmlID" t-att-data-action-id="app.actionID">
											<div class="app-image mb8">
											</div>
											<div class="app-name">
												<t t-esc="app.name" />
											</div>
										</a>
										<div class="fav_app_select d-none ">
											<span class="ri ri-check-line"/>
										</div>
									</div>
								</t>
							</div>
							<div id="searched_main_apps" class="d-none flex-wrap"></div>
							<div id="search_result"></div>
						</div>
					</div>
				</div>
			</div>
		</xpath>
		<!--Mobile view-->
		<xpath expr="//a" position="replace">
			<a href="/odoo" class="o_menu_toggle border-0" t-att-class="{'hasImage': currentApp?.webIconData}" accesskey="h" t-ref="menuApps" t-on-click.prevent="_openAppMenuSidebar">
				<t t-call="web.NavBar.AppsMenu.Sidebar"/>
			</a>
			<div class="o_navbar_apps_menu">
				<!-- The dynamic menu app is loaded in this div -->
				<div class="all-apps-menus"></div>
				<div class="appdrawer_section">
					<div class="top_navbar_section mb-3 mb-md-5">
						<a class="close_fav_app_btn d-none">
							<span class="ri ri-arrow-left-s-line"/>
						</a>
					</div>
					<div class="container">
						<div class="input-group">
							<div class="input-group-prepend d-flex">
								<span class="input-group-text">
									<i class="ri ri-search-line"></i>
								</span>
							</div>
							<input type="text" id="app_menu_search" class="form-control" placeholder="Search"/>
						</div>
						<div class="apps-list mt-3 mt-md-5">
							<!-- The dynamic menu app is loaded in this div -->
							<div class="spiffy-app-group"></div>
							<div id="searched_main_apps" class="d-none flex-wrap"></div>
							<div id="search_result"></div>
						</div>
					</div>
				</div>
			</div>
		</xpath>
	</t>

	<t t-name="AllmenuRecursive" >
		<li class="nav-item">
			<t t-if="menu.childrenTree.length">
				<p class="bg-muted">
					<a class="d-flex align-items-center w-100" data-bs-toggle="collapse" t-attf-data-bs-target="#child_menu_{{menu.id}}" t-att-data-menu-xmlid="menu.xmlid">
						<span><t t-esc="menu.name"/></span>
						<span class="ri ri-arrow-right-s-line ms-auto"/>
					</a>
				</p>
			</t>
			<t t-else="">
				<a t-att-data-menu="menu.id" t-attf-class="child_menus" t-att-href="getMenuItemHref(menu)" t-att-data-menu-xmlid="menu.xmlid" t-attf-data-action-id="{{menu.actionID ? menu.actionID : ''}}">
					<span class="app_name">
						<t t-esc="menu.name" />
					</span>
				</a>
			</t>
			<div t-attf-id="child_menu_{{menu.id}}" class="collapse" >
				<ul t-if="menu.childrenTree.length" class="show">
					<t t-foreach="menu.children" t-as="menuid" t-key="menuid">
						<t t-set="menu" t-value="menuService.getMenuAsTree(menuid)"/>
						<t t-call="AllmenuRecursive" />
					</t>
				</ul>
			</div>
		</li>
	</t>
	
	<t t-inherit="web.BurgerMenu" t-inherit-mode="extension" >
		<xpath expr="//button[hasclass('o_mobile_menu_toggle')]" position="attributes">
			<attribute name="class" add="d-lg-none px-2 me-1" remove="d-md-none pe-3" separator=" "/>
		</xpath>
		<!-- <xpath expr="//button[hasclass('o_mobile_menu_toggle')]/i[hasclass('oi')]" position="replace">
			<i class="ri ri-menu-line align-middle"/>
		</xpath> -->
	</t>
	
</templates>
// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
$spiffy_fonts: (
    lato: 'Lato',
    montserrat: 'Montserrat',
    open_sans: 'OpenSans',
    os<PERSON>: '<PERSON>',
    raleway: '<PERSON><PERSON><PERSON>',
    <PERSON><PERSON>: '<PERSON><PERSON>',
    poppins: '<PERSON><PERSON><PERSON>',
    rubik: '<PERSON><PERSON><PERSON>',
    inter: '<PERSON>',
    josefin_sans: 'JosefinSans',
    varela_round: 'VarelaRound',
    manrope: 'Manrope',
    Nunito_Sans: 'NunitoSans',
    almarai: 'Almarai',
    tajawal: '<PERSON><PERSON><PERSON>',
    cairo: 'Cairo'
) !default;

$spiffy-fonts-path: '../../lib';

@each $font, $name in $spiffy_fonts {
    @font-face{
        font-family: "#{$name}";
        src: url("#{$spiffy-fonts-path}/#{$name}/#{$name}-Regular.ttf") format("truetype");
    }
}

body{
    @each $font, $name in $spiffy_fonts {
        &.font_family_#{$font} {
            font-family: #{$name};
        }
    }
    .dynamic_data{
        #font_family{
            .text-style-design{
                font-size: 16px;
            }
            @each $font, $name in $spiffy_fonts {
                .font_family_#{$font} {
                    .text-style-design{
                        font-family: #{$name};
                    }
                }
            }
        }
    }
    h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
        font-family: inherit;
    }
}
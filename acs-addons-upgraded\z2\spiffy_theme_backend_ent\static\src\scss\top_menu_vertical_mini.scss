// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
.background-blur{
    position: fixed;
    z-index: -1;
    background-color: rgba(0, 0, 0, 0.6) !important;
    color: var(--biz-theme-body-text-color);
    box-shadow: var(--box-shadow-common);
    left: 126px;
    right: 0;
    top: 70px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(10px);
}
.top_menu_vertical_mini{
    .top-menu-vertical-mini {
        position: fixed;
        top: 70px;
        left: -220px;
        width: 220px;
        height: calc(100vh - 70px);
        background-color: #fff;
        border-left: 1px solid #ddd;
        z-index: -1;
        flex-direction: column;
        padding: 20px;
        padding-left: 24px !important;
        transition: left 0.5s !important;
        display: flex !important;
        flex-direction: row;
        flex-wrap: wrap;
        &.header-background {
            left: var(--header-vertical-mini-menu-width) !important;
            opacity: 1 !important;
        }
    }
    .app_icon{
        img,.fa,.ri{
            padding: 10px;
            height: 44px;
            max-width: 44px;
            min-width: 44px;         
        }
        color: var(--header-vertical-mini-text-color) !important;
        img {
            width: 24px;
            object-fit: cover;
        }
        .group-count {
            display: none;
        }
    }
    .group_menu_icon {
        img,.fa,.ri{
            padding: 10px;
            height: 44px;
            max-width: 44px;
            min-width: 44px;      
        }
    }
    .spiffy-menu-group {
        .menu-link-for-apps, .app_icon, .app-image{
            img,.fa,.ri{
                padding: 10px;
                height: 44px;
                max-width: 44px;
                min-width: 44px;        
            }
            color: var(--header-vertical-mini-text-color) !important;
            img {
                width: 24px;
                object-fit: cover;
            }
            .group-count {
                display: none;
            }
        }
        .submenu-group {
            position: fixed;
            top: 70px;
            left: -220px;
            max-width: 220px;
            background-color: #fff;
            z-index: 1000;
            flex-direction: column;
            padding: 20px;
            border-right: none;
            padding-left: 24px !important;
            transition: left 0.5s !important;
            display: flex !important;
            flex-direction: row;
            flex-wrap: wrap;
            max-height: calc(100vh - 70px);
            overflow-y: auto;
            overflow-x: hidden;
            &.show {
                left: var(--header-vertical-mini-menu-width) !important;
                opacity: 1 !important;
            }

            .main-group, .spiffy-main-group{
                width: 85px;

            }

            .group-menu-icon{
                margin-bottom: 16px;
                margin: 15px;
                background-color: var(--biz-theme-light-color);
                border-radius: 5px;
                transition: background-color 0.3s ease, box-shadow 0.3s ease;

            }
            .menu-name {
                color: var(--header-vertical-mini-text-color);
                font-weight: bold;
                text-align: center;
                font-size: 11px;
            }
            .menu-name{
                word-wrap: break-word;
                width: 80px;
            }


            .spiffy-menu-group {
                .group-menu-icon {
                    width: 45px !important;  
                    height: 45px; 
                    // padding: 10px;
                    margin: 15px;
                    background-color: var(--biz-theme-light-color);
                    border-radius: 5px;
                    transition: background-color 0.3s ease, box-shadow 0.3s ease;
                    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); 
                }

                .menu-name {
                    color: var(--header-vertical-mini-text-color);
                    font-weight: bold;
                    text-align: center;
                    font-size: 12px;
                }

                .submenu-link,
                .dropdown-btn {
                    display: flex;
                    flex-direction: column; 
                    align-items: center; 

                }
            }

            .header-sub-menus-group {
                position: absolute;
                height: calc(100vh - 70px);
                overflow: auto;
                background-color: #fff;
                color: #1b1b1b;
                top: -1px;
                width: 260px;
                box-shadow: 0 13px 30px 0 rgba(0, 0, 0, 0.19);
                clip-path: inset(-20px -30px -20px 0px);
                left: 0;
                padding: 2.25rem;
                z-index: 1100;
                opacity: 0;
                transition: left 0.5s !important;
                border-top-left-radius: 0 !important;
                border-bottom-left-radius: 0 !important;
                border-top-right-radius: var(--border-radius-lg) !important;
                border-bottom-right-radius: var(--border-radius-lg) !important;
                border: solid 1px #c4dbeb;
                border-left: none;
                display: flex !important;
                flex-direction: column;
                &.show {
                    left: 100%;
                    opacity: 1;
                }

                p.bg-muted {
                    font-size: 13px !important;
                    padding: 6px 16px !important;
                    background-color: var(--header-vertical-mini-bg-color) !important;
                    color: var(--header-vertical-mini-text-color) !important;

                    a {
                        color: var(--header-vertical-mini-text-color) !important;

                        * {
                            color: var(--header-vertical-mini-text-color) !important;
                        }
                    }

                    > a:not(.collapsed) {
                        .ri::before {
                            content: "\ea4e" !important;
                        }
                    }
                }

                * {
                    color: #1b1b1b;
                }

                .submenu_active {
                    color: var(--biz-theme-primary-color) !important;
                }
            }
        }

    }
    .break-line{
        display: none;
    }
}

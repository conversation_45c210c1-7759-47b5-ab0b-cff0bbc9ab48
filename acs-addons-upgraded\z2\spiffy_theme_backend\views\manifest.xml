<?xml version="1.0" encoding="UTF-8"?>
<!-- # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
# See LICENSE file for full copyright and licensing details. -->
<odoo>
<template id="service_worker" name="PWA service worker">
    'use strict';
        var base_cache_ver = 'base-cache-1';
        var assets = ['/','/pwa/offline'];
        self.addEventListener('install', function(event) {
            self.skipWaiting();
            event.waitUntil(
                caches.open(base_cache_ver).then(cache =>{
                    cache.addAll(assets);
                })
            )
        });
        self.addEventListener('activate', function(event) {
            event.waitUntil(
                caches.keys().then(function(keys) {
                    return Promise.all(keys.map(function(key) {
                        if (key !== base_cache_ver) {
                            return caches.delete(key);
                        }
                    }));
                })
            );
        });
        self.addEventListener('fetch', function(event) {
            event.respondWith(
                fetch(event.request).then(function (response) {
                    return response;
                })
                .catch(error => {
                    return caches.match('/pwa/offline');
            }));
        });
    </template>

    <template id="biz_web_layout_inheirt" name="Web Main layout" inherit_id="web.layout">
        <xpath expr="//head" position="inside">
            <!-- <t t-call-assets="backend.webclientmulti"/> -->
            <t t-set="company" t-value="request.env.company.sudo()"/>
            <t t-set="enable_pwa" t-value="company.enable_pwa"/>
            <t t-if="enable_pwa">
                <link rel="manifest" t-att-href="'/spiffy_theme_backend/%s/manifest.json' % (company.id) "/>
                <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
                <meta name="apple-mobile-web-app-title" content="PWA"/>
                
                <link rel="apple-touch-icon" t-att-href="'/web/image/res.company/%s/image_512_pwa/152x152' % (company.id)"/>
                <meta name="mobile-web-app-capable" content="yes"/>
            </t>
        </xpath>
        <xpath expr="//head//link" position="replace">
            <t t-set="company" t-value="request.env.company.sudo()"/>
            <link type="image/x-icon" rel="shortcut icon" t-att-href="x_icon or '/web/image/res.company/%s/spiffy_favicon' % (company.id)"/>
        </xpath>
    </template>

</odoo>
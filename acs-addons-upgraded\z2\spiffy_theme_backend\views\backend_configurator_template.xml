<?xml version="1.0" encoding="UTF-8"?>
<!-- # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
# See LICENSE file for full copyright and licensing details. -->
<odoo>
	<template id="template_backend_config_data" name="Dynamic Data template Backend">
		<div class="row g-0 align-items-start h-100 theme-config-sidebar">
			<button type="button" class="bg-transparent backend_configurator_close float-end m-0 w-auto  border-0"
				data-dismiss="modal">
				<img src="/spiffy_theme_backend/static/src/image/close.png" class="img img-fluid" />
			</button>
			<ul class="nav flex-shrink-0 col-4 config-sidebar-tab-panel ">
				<div class="w-100">
					<h3 class="mb-2 py-3 ps-3">Theme Settings</h3>
				</div>
				<li class="nav-item mb-1">
					<a href="#general_settings" class="nav-link active" data-bs-toggle="tab">General Settings</a>
				</li>
				<li class="nav-item mb-1">
					<a href="#theme_color_pallets" class="nav-link" data-bs-toggle="tab">Colors</a>
				</li>
				<li class="nav-item mb-1">
					<a href="#app_drawer" class="nav-link" data-bs-toggle="tab">App Drawer</a>
				</li>
				<!-- <li class="nav-item mb-1">
					<a href="#top_menu_vertical" class="nav-link" data-bs-toggle="tab">Top Menu Vertical</a>
				</li> -->
				<li class="nav-item mb-1">
					<a href="#font_family" class="nav-link" data-bs-toggle="tab">Font Settings</a>
				</li>
				<li class="nav-item mb-1">
					<a href="#theme_loaders_style" class="nav-link" data-bs-toggle="tab">Loaders</a>
				</li>
			</ul>
			<div class="tab-content flex-grow-1 col-8 d-flex flex-column justify-content-between h-100 config-sidebar-tab-content">
				<div id="general_settings" class="tab-pane active">
					<div>
						<h3 class="p-3">General Settings</h3>
					</div>
					<div class="container g-4">
						<h5 class="py-3 ">Theme Style</h5>
						<div class="row border-style pb4 theme-style-row">
							<div class="col-md-4 mb-3">
								<div class="form_check_content">
									<div class="form-check">
									<input type="radio" class="form-check-input" id="theme_style_rounded"
										name="theme_style" value="biz_theme_rounded"
										t-att-checked="config_vals.theme_style == 'biz_theme_rounded'" />
									<label class="custom-control-label" for="theme_style_rounded">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-style-rounded.png" alt="Rounded Theme" />
									</label>
								</div>
								</div>
							</div>

							<div class="col-md-4 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="theme_style_standard"
										name="theme_style" value="biz_theme_standard"
										t-att-checked="config_vals.theme_style == 'biz_theme_standard'" />
									<label class="custom-control-label" for="theme_style_standard">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-style-standard.png" alt="Standard Theme" />
									</label>
								</div>
								</div>
								
							</div>

							<div class="col-md-4 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="theme_style_square"
										name="theme_style" value="biz_theme_square"
										t-att-checked="config_vals.theme_style == 'biz_theme_square'" />
									<label class="custom-control-label" for="theme_style_square">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-style-square.png" alt="Square Theme" />
									</label>
								</div>
								</div>
								
							</div>
						</div>

						<h5 class="py-3 ">Menu Position</h5>
						<div class="row border-style pb4 menu-position-row">
							<div class="col-md-6 mb-3 menu-position">
								<div class="form_check_content ">
									<div class="form-check">
										<input type="radio" class="form-check-input top_menu_style" id="top_menu_style_horizontal"
											name="top_menu_position" value="top_menu_horizontal"
											t-att-checked="config_vals.top_menu_position == 'top_menu_horizontal'" />
										<label class="custom-control-label" for="top_menu_style_horizontal">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-menu_horizontal.png" alt="Menu horizontal" />
										</label>
									</div>
								</div>
							</div>
							<div class="col-md-6 mb-3 menu-position">
								<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input top_menu_style" id="top_menu_style_vertical"
											name="top_menu_position" value="top_menu_vertical"
											t-att-checked="config_vals.top_menu_position == 'top_menu_vertical'" />
										<label class="custom-control-label" for="top_menu_style_vertical">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-menu-vertical-center.png" alt="Menu Vertical" />
										</label>
									</div>
								</div>
							</div>
							<div class="col-md-6 mb-3 menu-position">
								<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input top_menu_style" id="top_menu_style_vertical_mini"
											name="top_menu_position" value="top_menu_vertical_mini"
											t-att-checked="config_vals.top_menu_position == 'top_menu_vertical_mini'" />
										<label class="custom-control-label" for="top_menu_style_vertical_mini">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-menu-vertical_mini.png" alt="Menu Vertical" />
										</label>
									</div>
								</div>
							</div>
							<div class="col-md-6 mb-3 menu-position">
								<div class="form_check_content">
									<div class="form-check">
										<input type="radio"  class="form-check-input top_menu_style" id="top_menu_style_vertical_mini_2"
										name="top_menu_position" value="top_menu_vertical_mini_2"
										t-att-checked="config_vals.top_menu_position == 'top_menu_vertical_mini_2'"/>
									
									<label class="custom-control-label" for="top_menu_style_vertical_mini_2">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-menu-vertical-left.png" alt="Menu Vertical 2" />
									</label>
									</div>
								</div>
							</div>
							<div class="col-12 mb-3">
								<div t-attf-class="vertical-mini-menu-options {{'' if config_vals.top_menu_position == 'top_menu_vertical_mini' else 'd-none'}}">
									<div t-attf-class="row menu-bg-image-content ">
										<div class="col-12 pb-3">
											<div class="py-3">
												<b>Header Background Image</b>
											</div>
											<div class="custom-file mb-3">
												<input type="file" id="menu_bg_image"
													t-att-value="config_vals.menu_bg_image"
													data-file_types="jpg|jpeg|gif|png"
													class="app_bg_img_menu listify-file-upload custom-file-input form-control"
													onchange="document.getElementById('blah').src = window.URL.createObjectURL(this.files[0])" />
												<label class="custom-file-label" for="menu_bg_image"></label>
												<img id="blah" alt="your image" class="img img-fluid mt-3"
													t-att-src="'/web/image/backend.config/%s/menu_bg_image' %(config_vals.id)" />
											</div>
										</div>
									</div>

									<div t-attf-class="custom_color_config ">
										<div class="pb-2 d-flex">
											<div class="w-50">Background Color</div>
											<input type="color" class="border border-dark" id="custom_header_bg" name="Custom Background" t-att-value="config_vals.header_vertical_mini_bg_color"/>
										</div>
									</div>

									<div t-attf-class="custom_color_config ">
										<div class="pb-2 d-flex">
											<div class="w-50">Text Color</div>
											<input type="color" class="border border-dark" id="custom_header_text" name="Custom text" t-att-value="config_vals.header_vertical_mini_text_color"/>
										</div>
									</div>
								</div>

							</div>
							<div class="col-12 mb-3">
								<div t-attf-class="vertical-mini-menu-options-2 {{'' if config_vals.top_menu_position == 'top_menu_vertical_mini_2' else 'd-none'}}">
									<div t-attf-class="row menu-bg-image-content ">
										<div class="col-12 pb-3">
											<div class="py-3">
												<b>Header Background Image</b>
												<div class="row border-style pb4 pt-4">
													<div class="col-md-6 mb-3">
														<div class="form_check_content">
															<div class="form-check">
																<input type="radio" class="form-check-input" id="top_menu_vertical_bg1"
																	name="top_menu_bg_vertical_mini_2" value="top_menu_vertical_bg1"
																	t-att-checked="config_vals.top_menu_bg_vertical_mini_2 == 'top_menu_vertical_bg1'" />
																<label class="custom-control-label ms-3" for="top_menu_vertical_bg1">
																	<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/top-menu-v2-bg-one.png" alt="Menu horizontal" />
																</label>
															</div>
														</div>
													</div>
													<div class="col-md-6 mb-3">
														<div class="form_check_content">
															<div class="form-check">
																<input type="radio" class="form-check-input" id="top_menu_vertical_bg2"
																	name="top_menu_bg_vertical_mini_2" value="top_menu_vertical_bg2"
																	t-att-checked="config_vals.top_menu_bg_vertical_mini_2 == 'top_menu_vertical_bg2'" />
																<label class="custom-control-label ms-3" for="top_menu_vertical_bg2">
																	<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/top-menu-v2-bg-two.png" alt="Menu horizontal" />
																</label>
															</div>
														</div>
													</div>
													<div class="col-md-6 mb-3">
														<div class="form_check_content">
															<div class="form-check">
																<input type="radio" class="form-check-input" id="top_menu_vertical_bg3"
																	name="top_menu_bg_vertical_mini_2" value="top_menu_vertical_bg3"
																	t-att-checked="config_vals.top_menu_bg_vertical_mini_2 == 'top_menu_vertical_bg3'" />
																<label class="custom-control-label ms-3" for="top_menu_vertical_bg3">
																	<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/top-menu-v2-bg-three.png" alt="Menu horizontal" />
																</label>
															</div>
														</div>
													</div>
													<div class="col-md-6 mb-3">
														<div class="form_check_content">
															<div class="form-check">
																<input type="radio" class="form-check-input" id="top_menu_vertical_bg4"
																	name="top_menu_bg_vertical_mini_2" value="top_menu_vertical_bg4"
																	t-att-checked="config_vals.top_menu_bg_vertical_mini_2 == 'top_menu_vertical_bg4'" />
																<label class="custom-control-label ms-3" for="top_menu_vertical_bg4">
																	<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/top-menu-v2-bg-four.png" alt="Menu horizontal" />
																</label>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="border-style pb-3 row">
							<div class="d-flex">
								<div class="row">
									<div class="col-12">
										<div class="form-switch p-0">
											<input type="checkbox" class="form-check-input" id="apply_menu_shape_style"
												t-att-checked="config_vals.apply_menu_shape_style" />
											<label class="form-check-label" for="apply_menu_shape_style"></label>
										</div>
									</div>
								</div>
								<h5 class="pt-3 pb-0 mb-0 ms-2">Apply Menu Shape Style</h5>
							</div>
							<div t-attf-class="apply_menu_shape {{'' if config_vals.apply_menu_shape_style else 'd-none'}}">
								<h5 t-attf-class="py-3">Menu Shape Style</h5>
								<div t-attf-class="row pb4 theme-style-row">
									<div class="col-md-4 mb-3">
										<div class="form_check_content">
											<div class="form-check">
												<input type="radio" class="form-check-input" id="shape_style_rounded"
													name="shape_style" value="biz_shape_rounded"
													t-att-checked="config_vals.shape_style == 'biz_shape_rounded'" />
												<label class="custom-control-label" for="shape_style_rounded">
													<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/shape-style-rounded.png" alt="Rounded Theme" />
												</label>
											</div>
										</div>
										<p class="text-center mt-2 mb-0">Rounded</p>
									</div>

									<div class="col-md-4 mb-3 ps-md-0">
										<div class="form_check_content">
											<div class="form-check">
												<input type="radio" class="form-check-input" id="shape_style_standard"
													name="shape_style" value="biz_shape_circle"
													t-att-checked="config_vals.shape_style == 'biz_shape_circle'" />
												<label class="custom-control-label" for="shape_style_standard">
													<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/shape-style-standard.png" alt="Standard Theme" />
												</label>
											</div>
										</div>
										<p class="text-center mt-2 mb-0">Circle</p>
									</div>

									<div class="col-md-4 mb-3 ps-md-0">
										<div class="form_check_content">
											<div class="form-check">
												<input type="radio" class="form-check-input" id="shape_style_square"
													name="shape_style" value="biz_shape_square"
													t-att-checked="config_vals.shape_style == 'biz_shape_square'" />
												<label class="custom-control-label" for="shape_style_square">
													<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/shape-style-square.png" alt="Square Theme" />
												</label>
											</div>
										</div>
										<p class="text-center mt-2 mb-0">Square</p>
									</div>
								</div>
								<h5 class="pb-3 ">Menu Shape Background Color</h5>
								<div class="row pb4">
									<div class="col-12">
										<div t-attf-class="custom_color_config ">
											<div class="pb-2 d-flex">
												<div class="w-50 pt-3">Background Color</div>
												<div class="background-color">
													<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/background-color.svg" alt="Backgroun Color" />
													<input type="color" class="border border-dark" id="menu_shape_bg" name="Menu Shape Background Color" t-att-value="config_vals.menu_shape_bg_color"/>
												</div>
											</div>
										</div>
										<div class="slidecontainer col-12 mt-3 d-flex align-items-center">
											<div class="text-nowrap me-2 w-50">Background Color Opacity</div>
											<input type="range" min="0.1" max="1" step="0.1" t-att-value="config_vals.menu_shape_bg_color_opacity" class="slider" id="menu_shape_bg_color_opacity"/>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- This configuration is added in header, with refresh button -->
						<!-- <h5 class="py-3 ">Chatter Position</h5>
						<div class="row border-style pb4 chatter-style-row">
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="chatter_style_right"
										name="chatter_position" value="chatter_right"
										t-att-checked="config_vals.chatter_position == 'chatter_right'" />
									<label class="custom-control-label" data-icon="VtGdhRvADaS" for="chatter_style_right">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-chatter-right.png" alt="Chatter Right" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
									<div class="form-check">
									<input type="radio" class="form-check-input" id="chatter_style_bottom"
										name="chatter_position" value="chatter_bottom"
										t-att-checked="config_vals.chatter_position == 'chatter_bottom'" />
									<label class="custom-control-label" for="chatter_style_bottom">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-chatter-bottom.png" alt="Chatter Bottom" />
									</label>
								</div>
								</div>
								
							</div>
						</div> -->

						<h5 class="py-3 ">List View Density</h5>
						<div class="row border-style pb4 list-density-row">
							<div class="col-md-6 mb-3">
								<div class="form_check_content h-100">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="list_comfortable"
											name="list_view_density" value="list_comfortable"
											t-att-checked="config_vals.list_view_density == 'list_comfortable'" />
										<label class="custom-control-label" for="list_comfortable" title="Comfortable">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/list_comfortable.png" alt="List Comfortable" />
										</label>
									</div>
								</div>
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content h-100">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="list_compact"
											name="list_view_density" value="list_compact"
											t-att-checked="config_vals.list_view_density == 'list_compact'" />
										<label class="custom-control-label" for="list_compact" title="Compact">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/list_compact.png" alt="List Compact" />
										</label>
									</div>
								</div>
							</div>
						</div>
		
						<h5 class="py-3 ">Input Style</h5>
						<div class="row border-style pb4 form-mode-row">
							<div class="col-md-4 mb-3">
								<div class="form_check_content h-100">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="input_borderless"
											name="input_style" value="input_borderless"
											t-att-checked="config_vals.input_style == 'input_borderless'" />
										<label class="custom-control-label" for="input_borderless" title="Borderless">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/input-style-borderless.png" alt="Borderless" />
										</label>
									</div>
								</div>
							</div>
							<div class="col-md-4 mb-3 ps-md-0">
								<div class="form_check_content h-100">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="input_bottom_border"
											name="input_style" value="input_bottom_border"
											t-att-checked="config_vals.input_style == 'input_bottom_border'" />
										<label class="custom-control-label" for="input_bottom_border" title="Border Bottom">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/input-style-bottom-border.png" alt="Border Bottom" />
										</label>
									</div>
								</div>
							</div>
							<div class="col-md-4 mb-3 ps-md-0">
								<div class="form_check_content h-100">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="input_bordered"
											name="input_style" value="input_bordered"
											t-att-checked="config_vals.input_style == 'input_bordered'" />
										<label class="custom-control-label" for="input_bordered" title="Bordered">
											<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/input-style-bordered.png" alt="Bordered" />
										</label>
									</div>
								</div>
							</div>
						</div>

						<h5 class="py-3 ">List/Tree view Sticky Header and Footer</h5>
						<div class="row border-style pb4 list-sticky-header-row">
							<div class="col-12 mb-3">
								<div class="form-switch p-0 mb-3">
									<input type="checkbox" class="form-check-input" id="list_view_sticky_header"
										t-att-checked="config_vals.list_view_sticky_header" />
									<label class="form-check-label" for="list_view_sticky_header"></label>
								</div>
							</div>
						</div>

						<h5 class="py-3 ">Tree Form Split View</h5>
						<div class="row border-style pb4 tree-form-split-row">
							<div class="col-12 mb-3">
								<div class="form-switch p-0 mb-3">
									<input type="checkbox" class="form-check-input" id="tree_form_split_view"
										t-att-checked="config_vals.tree_form_split_view" />
									<label class="form-check-label" for="tree_form_split_view">Use Split view ?</label>
								</div>
							</div>
						</div>

						<h5 class="py-3 ">Show Attachments in List View</h5>
						<div class="row border-style pb4 show-attach-row">
							<div class="col-12 mb-3">
								<div class="form-switch p-0 mb-3">
									<input type="checkbox" class="form-check-input" id="attachment_in_tree_view"
										t-att-checked="config_vals.attachment_in_tree_view" />
									<label class="form-check-label" for="attachment_in_tree_view"></label>
								</div>
							</div>
						</div>

						<h5 class="py-3">Tab Styles</h5>
						<div class="row border-style pb4 tab-style-row">
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="tab_style_1" name="tab"
										value="tab_style_1" t-att-checked="config_vals.tab == 'tab_style_1'" />
									<label class="custom-control-label" for="tab_style_1">
										<div class="tab-style-one">
											<a class=" btn btn-custom demo_btn"></a>
										</div>
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="tab_style_2" name="tab"
										value="tab_style_2" t-att-checked="config_vals.tab == 'tab_style_2'" />
									<label class="custom-control-label" for="tab_style_2">
										<div class="tab-style-two">
											<a class=" btn btn-custom demo_btn"></a>
										</div>
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="tab_style_3" name="tab"
										value="tab_style_3" t-att-checked="config_vals.tab == 'tab_style_3'" />
									<label class="custom-control-label" for="tab_style_3">
										<div class="tab-style-three">
											<a class=" btn btn-custom demo_btn"></a>
										</div>
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="tab_style_4" name="tab"
										value="tab_style_4" t-att-checked="config_vals.tab == 'tab_style_4'" />
									<label class="custom-control-label" for="tab_style_4">
										<div class="tab-style-four">
											<a class=" btn btn-custom demo_btn"></a>
										</div>
									</label>
								</div>
								</div>
								
							</div>
						</div>

						<h5 class="py-3">Checkbox Styles</h5>
						<div class="row border-style pb4 checkbox-style-row">
							<div class="col-9 mb-3  ">
								<div class="row mx-3 g-0 mb-2">
									<div class="offset-2 col-5">
										<h4 class="d-flex justify-content-center">Active</h4>
									</div>
									<div class="col-5">
										<h4 class="d-flex justify-content-center">Deactive</h4>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="checkbox_style_1"
												name="checkbox" value="checkbox_style_1" 
												t-att-checked="config_vals.checkbox == 'checkbox_style_1'" />
											<label class="custom-control-label" for="checkbox_style_1">
											</label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-checkbox1 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-checkbox1"></div>
										</div>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="checkbox_style_2"
											name="checkbox" value="checkbox_style_2"
											t-att-checked="config_vals.checkbox == 'checkbox_style_2'" />
											<label class="custom-control-label" for="checkbox_style_2"></label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-checkbox2 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-checkbox2"></div>
										</div>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="checkbox_style_3"
												name="checkbox" value="checkbox_style_3"
												t-att-checked="config_vals.checkbox == 'checkbox_style_3'" />
											<label class="custom-control-label" for="checkbox_style_3"></label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-checkbox3 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-checkbox3"></div>
										</div>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="checkbox_style_4"
												name="checkbox" value="checkbox_style_4"
												t-att-checked="config_vals.checkbox == 'checkbox_style_4'" />
											<label class="custom-control-label" for="checkbox_style_4"></label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-checkbox4 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-checkbox4"></div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<h5 class="py-3">Radio Styles</h5>
						<div class="row border-style pb4 radio-style-row">
							<div class="col-9 mb-3">
								<div class="row mx-3 g-0 mb-2">
									<div class="offset-2 col-5">
										<h4 class="d-flex justify-content-center">Active</h4>
									</div>
									<div class="col-5">
										<h4 class="d-flex justify-content-center">Deactive</h4>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="radio_style_1" name="radio"
												value="radio_style_1" t-att-checked="config_vals.radio == 'radio_style_1'" />
											<label class="custom-control-label" for="radio_style_1"></label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-radio1 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-radio1"></div>
										</div>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="radio_style_2" name="radio"
												value="radio_style_2" t-att-checked="config_vals.radio == 'radio_style_2'" />
											<label class="custom-control-label" for="radio_style_2"></label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-radio2 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-radio2"></div>
										</div>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="radio_style_3" name="radio"
												value="radio_style_3"
												t-att-checked="config_vals.radio == 'radio_style_3'" />
											<label class="custom-control-label" for="radio_style_3"></label>
										</div>
										<div class="col-5">
											<div class="pt-1 biz-custom-radio3 active"></div>
										</div>
										<div class="col-4">
											<div class="pt-1 biz-custom-radio3"></div>
										</div>
									</div>
								</div>
								<div class="col-12 mb-3 me-0 custom-control custom-radio custom-control-inline  form-check box-shadow">
									<div class="row ms-3 w-100">
										<div class="col-3">
											<input type="radio" class="form-check-input" id="radio_style_4" name="radio"
												value="radio_style_4" t-att-checked="config_vals.radio == 'radio_style_4'" />
											<label class="custom-control-label" for="radio_style_4"></label>
										</div>
										<div class="col-5">
											<div class="biz-custom-radio4 active"></div>
										</div>
										<div class="col-4">
											<div class="biz-custom-radio4"></div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<h5 class="py-3">Popup Animation</h5>
						<div class="row border-style pb4 popup-animate-style-row">
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="popup_style_1" name="popup"
										value="popup_style_1" t-att-checked="config_vals.popup == 'popup_style_1'" />
									<label class="custom-control-label" for="popup_style_1">
										<span class="text-style-design">Fade</span>	
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="popup_style_2" name="popup"
										value="popup_style_2" t-att-checked="config_vals.popup == 'popup_style_2'" />
									<label class="custom-control-label" for="popup_style_2">
										<span class="text-style-design">Vertical Flip</span>	
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="popup_style_3" name="popup"
										value="popup_style_3" t-att-checked="config_vals.popup == 'popup_style_3'" />
									<label class="custom-control-label" for="popup_style_3">
										<span class="text-style-design">BounceIn</span>	
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="popup_style_4" name="popup"
										value="popup_style_4" t-att-checked="config_vals.popup == 'popup_style_4'" />
									<label class="custom-control-label" for="popup_style_4">
										<span class="text-style-design">Shrink</span>
									</label>
								</div>
								</div>
								
							</div>
						</div>

						<h5 class="py-3">Separator Styles</h5>
						<div class="row border-style pb4 separator-styles-row">
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="separator_style_1" name="separator"
										value="separator_style_1"
										t-att-checked="config_vals.separator == 'separator_style_1'" />
									<label class="custom-control-label flex-fill" for="separator_style_1">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-separator-style1.png" alt="Separator 1" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="separator_style_2" name="separator"
										value="separator_style_2"
										t-att-checked="config_vals.separator == 'separator_style_2'" />
									<label class="custom-control-label flex-fill" for="separator_style_2">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-separator-style2.png" alt="Separator 2" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3">
								<div class="form_check_content"><div class="form-check">
									<input type="radio" class="form-check-input" id="separator_style_3" name="separator"
										value="separator_style_3"
										t-att-checked="config_vals.separator == 'separator_style_3'" />
									<label class="custom-control-label flex-fill" for="separator_style_3">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-separator-style3.png" alt="Separator 3" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="separator_style_4" name="separator"
										value="separator_style_4"
										t-att-checked="config_vals.separator == 'separator_style_4'" />
									<label class="custom-control-label flex-fill" for="separator_style_4">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/theme-separator-style4.png" alt="Separator 4" />
									</label>
								</div>
								</div>
								
							</div>
						</div>
					</div>
				</div>

				<div id="app_drawer" class="tab-pane">
					<div>
						<h3 class="mb-3 py-3 ps-3">App Drawer</h3>
					</div>
					<div class="container g-4">
						<h5 class="py-3">Color Combinations</h5>
						<div class="row pb4">
							<div class="col-12">
								<div class="form-switch p-0 mb-3">
									<input type="checkbox" class="form-check-input" id="use_custom_drawer_color"
										t-att-checked="config_vals.use_custom_drawer_color" />
									<label class="form-check-label" for="use_custom_drawer_color">Set Your Own Drawer
										Colors</label>
								</div>
							</div>
						</div>
						<div t-attf-class="predefined_color_pallets border-bottom mb-3 {{'d-none' if config_vals.use_custom_drawer_color else none}}">
							<div class="row m-0">
								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_1"
												name="drawer_color_pallets" value="drawer_pallet_1"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_1'" />
											<label class="custom-control-label" for="drawer_color_pallet_1">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_1.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_2"
												name="drawer_color_pallets" value="drawer_pallet_2"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_2'" />
											<label class="custom-control-label" for="drawer_color_pallet_2">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_2.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_3"
												name="drawer_color_pallets" value="drawer_pallet_3"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_3'" />
											<label class="custom-control-label" for="drawer_color_pallet_3">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_3.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_4"
												name="drawer_color_pallets" value="drawer_pallet_4"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_4'" />
											<label class="custom-control-label" for="drawer_color_pallet_4">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_4.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_5"
												name="drawer_color_pallets" value="drawer_pallet_5"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_5'" />
											<label class="custom-control-label" for="drawer_color_pallet_5">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_5.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_6"
												name="drawer_color_pallets" value="drawer_pallet_6"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_6'" />
											<label class="custom-control-label" for="drawer_color_pallet_6">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_6.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_7"
												name="drawer_color_pallets" value="drawer_pallet_7"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_7'" />
											<label class="custom-control-label" for="drawer_color_pallet_7">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_7.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_8"
												name="drawer_color_pallets" value="drawer_pallet_8"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_8'" />
											<label class="custom-control-label" for="drawer_color_pallet_8">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_8.png" />
											</label>
										</div>
									</div>
									
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_9"
												name="drawer_color_pallets" value="drawer_pallet_9"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_9'" />
											<label class="custom-control-label" for="drawer_color_pallet_9">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_9.png" />
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_10"
												name="drawer_color_pallets" value="drawer_pallet_10"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_10'" />
											<label class="custom-control-label" for="drawer_color_pallet_10">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_10.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_11"
												name="drawer_color_pallets" value="drawer_pallet_11"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_11'" />
											<label class="custom-control-label" for="drawer_color_pallet_11">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_11.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_12"
												name="drawer_color_pallets" value="drawer_pallet_12"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_12'" />
											<label class="custom-control-label" for="drawer_color_pallet_12">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_12.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_13"
												name="drawer_color_pallets" value="drawer_pallet_13"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_13'" />
											<label class="custom-control-label" for="drawer_color_pallet_13">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_13.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_14"
												name="drawer_color_pallets" value="drawer_pallet_14"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_14'" />
											<label class="custom-control-label" for="drawer_color_pallet_14">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_14.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_15"
												name="drawer_color_pallets" value="drawer_pallet_15"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_15'" />
											<label class="custom-control-label" for="drawer_color_pallet_15">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_15.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_16"
												name="drawer_color_pallets" value="drawer_pallet_16"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_16'" />
											<label class="custom-control-label" for="drawer_color_pallet_16">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_16.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_17"
												name="drawer_color_pallets" value="drawer_pallet_17"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_17'" />
											<label class="custom-control-label" for="drawer_color_pallet_17">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_17.png" />
											</label>
										</div>
									</div>
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_18"
												name="drawer_color_pallets" value="drawer_pallet_18"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_18'" />
											<label class="custom-control-label" for="drawer_color_pallet_18">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_18.png" />
											</label>
										</div>
									</div>
									
								</div>

								<div class="col-md-6 mb-3 me-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="drawer_color_pallet_19"
												name="drawer_color_pallets" value="drawer_pallet_19"
												t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_19'" />
											<label class="custom-control-label" for="drawer_color_pallet_19">
												<img style="" class="img img-fluid"
													src="/spiffy_theme_backend/static/src/image/pallet_19.png" />
											</label>
										</div>
									</div>
								</div>
								
							</div>
						</div>

						<div t-attf-class="custom_color_config border-bottom {{'' if config_vals.use_custom_drawer_color else 'd-none'}}">
							<div class="pb-2 d-flex">
								<div class="w-50">Background Color</div>
								<div class="background-color">
									<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/background-color.svg" alt="Backgroun Color" />
									<input type="color" class=" border border-dark" id="custom_drawer_bg" name="Custom background" t-att-value="config_vals.appdrawer_custom_bg_color"/>
								</div>
							</div>
							<div class="pb-2 d-flex">
								<div class="w-50">Text Color</div>
								<div class="background-color">
									<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/background-color.svg" alt="Backgroun Color" />
									<input type="color" class=" border border-dark" id="custom_drawer_text" name="Custom text" t-att-value="config_vals.appdrawer_custom_text_color"/>
								</div>
							</div>
						</div>

						<div class="app-drawer-bg-image-option d-flex align-items-center form-check mb-3 mt-4 p-0">
							<div class="form-switch p-0">
								<input type="checkbox" class="form-check-input" id="apply_light_bg" name="example"
									t-att-checked="config_vals.apply_light_bg_img" />
								<label class="form-check-label" for="apply_light_bg">Apply Background Image</label>
							</div>
						</div>

						<div t-attf-class="row app-drawer-bg-image-content {{'' if config_vals.apply_light_bg_img else 'd-none'}}">
							<div class="col-12 pb-3">
								<div class="pb-3">
									<b>Body Image</b>
								</div>
								<div class="custom-file mb-3">
									<input type="file" id="light_bg_image"
										t-att-value="config_vals.light_bg_image"
										data-file_types="jpg|jpeg|gif|png"
										class="app_bg_img_light listify-file-upload custom-file-input form-control"
										onchange="document.getElementById('blah').src = window.URL.createObjectURL(this.files[0])" />
									<label class="custom-file-label" for="light_bg_image"></label>
									<img id="blah" alt="your image" class="img img-fluid mt-3"
										t-att-src="'/web/image/backend.config/%s/light_bg_image' %(config_vals.id)" />
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- <div id="top_menu_vertical" class="tab-pane">
					<div>
						<h3 class="mb-3 py-3 ps-3">Top Menu Vertical</h3>
					</div>
					<div class="container g-4">
						<h5 class="py-3">Select Background Image</h5>
						<div class="col-md-6 mb-3 me-0">
							<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="drawer_color_pallet_1"
										name="drawer_color_pallets" value="drawer_pallet_1"
										t-att-checked="config_vals.drawer_color_pallet == 'drawer_pallet_1'" />
									<label class="custom-control-label" for="drawer_color_pallet_1">
										<img style="" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/background.jpeg" />
									</label>
								</div>
							</div>
						</div>
					</div>
				</div> -->


				<div id="font_family" class="tab-pane">
					<div>
						<h3 class="mb-3 py-3 ps-3">Font Setting</h3>
					</div>
					<div class="container g-4">
						<div class="font_family mb-3">
							<div class="row border-style pb4">
								<h5 class="py-3">Font Family</h5>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_1"
												name="font_family" value="lato"
												t-att-checked="config_vals.font_family == 'lato'" />
											<label class="font_family_lato custom-control-label" for="font_family_1">
												<span class="text-style-design">Lato</span>	
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_2"
												name="font_family" value="montserrat"
												t-att-checked="config_vals.font_family == 'montserrat'" />
											<label class="font_family_montserrat custom-control-label" for="font_family_2">
												<span class="text-style-design">Montserrat</span>	
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_3"
												name="font_family" value="open_sans"
												t-att-checked="config_vals.font_family == 'open_sans'" />
											<label class="font_family_open_sans custom-control-label" for="font_family_3">
												<span class="text-style-design">Open Sans</span>	
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_4"
												name="font_family" value="oswald"
												t-att-checked="config_vals.font_family == 'oswald'" />
											<label class="font_family_oswald custom-control-label" for="font_family_4">
												<span class="text-style-design">Oswald</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_5"
												name="font_family" value="raleway"
												t-att-checked="config_vals.font_family == 'raleway'" />
											<label class="font_family_raleway custom-control-label" for="font_family_5">
												<span class="text-style-design">Raleway</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_6"
												name="font_family" value="roboto"
												t-att-checked="config_vals.font_family == 'roboto'" />
											<label class="font_family_roboto custom-control-label" for="font_family_6">
												<span class="text-style-design">Roboto</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_7"
												name="font_family" value="poppins"
												t-att-checked="config_vals.font_family == 'poppins'" />
											<label class="font_family_poppins custom-control-label" for="font_family_7">
												<span class="text-style-design">Poppins</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_8"
												name="font_family" value="rubik"
												t-att-checked="config_vals.font_family == 'rubik'" />
											<label class="font_family_rubik custom-control-label" for="font_family_8">
												<span class="text-style-design">Rubik</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_9"
												name="font_family" value="inter"
												t-att-checked="config_vals.font_family == 'inter'" />
											<label class="font_family_inter custom-control-label" for="font_family_9">
												<span class="text-style-design">Inter</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_10"
												name="font_family" value="josefin_sans"
												t-att-checked="config_vals.font_family == 'josefin_sans'" />
											<label class="font_family_josefin_sans custom-control-label" for="font_family_10">
												<span class="text-style-design">Josefin Sans</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_11"
												name="font_family" value="varela_round"
												t-att-checked="config_vals.font_family == 'varela_round'" />
											<label class="font_family_varela_round custom-control-label" for="font_family_11">
												<span class="text-style-design">Varela Round</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_12"
												name="font_family" value="manrope"
												t-att-checked="config_vals.font_family == 'manrope'" />
											<label class="font_family_manrope custom-control-label" for="font_family_12">
												<span class="text-style-design">Manrope</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_13"
												name="font_family" value="Nunito_Sans"
												t-att-checked="config_vals.font_family == 'Nunito_Sans'" />
											<label class="font_family_Nunito_Sans custom-control-label" for="font_family_13">
												<span class="text-style-design">Nunito Sans</span>
											</label>
										</div>
									</div>
								</div>
								<!-- ARABIC FONTS -->
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_14"
												name="font_family" value="tajawal"
												t-att-checked="config_vals.font_family == 'tajawal'" />
											<label class="font_family_tajawal custom-control-label" for="font_family_14">
												<span class="text-style-design">Tajawal</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_16"
												name="font_family" value="cairo"
												t-att-checked="config_vals.font_family == 'cairo'" />
											<label class="font_family_cairo custom-control-label" for="font_family_16">
												<span class="text-style-design">Cairo</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_family_15"
												name="font_family" value="almarai"
												t-att-checked="config_vals.font_family == 'almarai'" />
											<label class="font_family_almarai custom-control-label" for="font_family_15">
												<span class="text-style-design">Almarai</span>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="row border-style pb-4">
								<h5 class="py-3">Font Size</h5>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_size_1" name="font_size"
												value="font_small" t-att-checked="config_vals.font_size == 'font_small'" />
											<label class="custom-control-label" for="font_size_1">
												<span class="font_small text-style-design">Small</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_size_2" name="font_size"
												value="font_medium" t-att-checked="config_vals.font_size == 'font_medium'" />
											<label class="custom-control-label" for="font_size_2">
												<span class="font_medium text-style-design">Medium</span>
											</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
										<div class="form-check">
											<input type="radio" class="form-check-input" id="font_size_3" name="font_size"
												value="font_large" t-att-checked="config_vals.font_size == 'font_large'" />
											<label class="custom-control-label" for="font_size_3">
												<span class="font_large text-style-design">Large</span>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						
					</div>
				</div>

				<div id="theme_color_pallets" class="tab-pane">
					<div>
						<h3 class="mb-3 py-3 ps-3">Colors</h3>
					</div>
					<div class="container g-4">
						<div class="form-switch p-0 mb-3">
							<input type="checkbox" class="form-check-input" id="use_custom_color_config"
								t-att-checked="config_vals.use_custom_colors" />
							<label class="form-check-label" for="use_custom_color_config">Set Your Own Colors</label>
						</div>
						<div t-attf-class="predefined_color_pallets mb-3 {{'d-none' if config_vals.use_custom_colors else none}}">
							<div class="row m-0">
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_1"
											name="color_pallets" value="pallet_1"
											t-att-checked="config_vals.color_pallet == 'pallet_1'" />
										<label class="custom-control-label" for="color_pallet_1">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_1.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_2"
											name="color_pallets" value="pallet_2"
											t-att-checked="config_vals.color_pallet == 'pallet_2'" />
										<label class="custom-control-label" for="color_pallet_2">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_2.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_3"
											name="color_pallets" value="pallet_3"
											t-att-checked="config_vals.color_pallet == 'pallet_3'" />
										<label class="custom-control-label" for="color_pallet_3">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_3.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_4"
											name="color_pallets" value="pallet_4"
											t-att-checked="config_vals.color_pallet == 'pallet_4'" />
										<label class="custom-control-label" for="color_pallet_4">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_4.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_5"
											name="color_pallets" value="pallet_5"
											t-att-checked="config_vals.color_pallet == 'pallet_5'" />
										<label class="custom-control-label" for="color_pallet_5">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_5.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_6"
											name="color_pallets" value="pallet_6"
											t-att-checked="config_vals.color_pallet == 'pallet_6'" />
										<label class="custom-control-label" for="color_pallet_6">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_6.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_7"
											name="color_pallets" value="pallet_7"
											t-att-checked="config_vals.color_pallet == 'pallet_7'" />
										<label class="custom-control-label" for="color_pallet_7">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_7.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_8"
											name="color_pallets" value="pallet_8"
											t-att-checked="config_vals.color_pallet == 'pallet_8'" />
										<label class="custom-control-label" for="color_pallet_8">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_8.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_9"
											name="color_pallets" value="pallet_9"
											t-att-checked="config_vals.color_pallet == 'pallet_9'" />
										<label class="custom-control-label" for="color_pallet_9">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_9.png" />
										</label>
									</div>
									</div>
									
								</div>

								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_10"
											name="color_pallets" value="pallet_10"
											t-att-checked="config_vals.color_pallet == 'pallet_10'" />
										<label class="custom-control-label" for="color_pallet_10">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_10.png" />
										</label>
									</div>
									</div>
									
								</div>



								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_11"
											name="color_pallets" value="pallet_11"
											t-att-checked="config_vals.color_pallet == 'pallet_11'" />
										<label class="custom-control-label" for="color_pallet_11">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_11.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_12"
											name="color_pallets" value="pallet_12"
											t-att-checked="config_vals.color_pallet == 'pallet_12'" />
										<label class="custom-control-label" for="color_pallet_12">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_12.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_13"
											name="color_pallets" value="pallet_13"
											t-att-checked="config_vals.color_pallet == 'pallet_13'" />
										<label class="custom-control-label" for="color_pallet_13">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_13.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_14"
											name="color_pallets" value="pallet_14"
											t-att-checked="config_vals.color_pallet == 'pallet_14'" />
										<label class="custom-control-label" for="color_pallet_14">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_14.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_15"
											name="color_pallets" value="pallet_15"
											t-att-checked="config_vals.color_pallet == 'pallet_15'" />
										<label class="custom-control-label" for="color_pallet_15">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_15.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_16"
											name="color_pallets" value="pallet_16"
											t-att-checked="config_vals.color_pallet == 'pallet_16'" />
										<label class="custom-control-label" for="color_pallet_16">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_16.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_17"
											name="color_pallets" value="pallet_17"
											t-att-checked="config_vals.color_pallet == 'pallet_17'" />
										<label class="custom-control-label" for="color_pallet_17">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_17.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3 ps-md-0">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_18"
											name="color_pallets" value="pallet_18"
											t-att-checked="config_vals.color_pallet == 'pallet_18'" />
										<label class="custom-control-label" for="color_pallet_18">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_18.png" />
										</label>
									</div>
									</div>
									
								</div>
								<div class="col-md-6 mb-3">
									<div class="form_check_content">
									<div class="form-check">
										<input type="radio" class="form-check-input" id="color_pallet_19"
											name="color_pallets" value="pallet_19"
											t-att-checked="config_vals.color_pallet == 'pallet_19'" />
										<label class="custom-control-label" for="color_pallet_19">
											<img style="" class="img img-fluid"
												src="/spiffy_theme_backend/static/src/image/pallet_19.png" />
										</label>
									</div>
									</div>
									
								</div>
							</div>
						</div>
						<!-- set custom colors -->
						<div t-attf-class="custom_color_config {{'' if config_vals.use_custom_colors else 'd-none'}}">
							<!-- primary -->
							<div class="col-12 pb-3 ps-md-0">
								<div class="pb-2 d-flex">
									<div class="w-50 pt-3">Primary Color</div>
									<div class="background-color">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/background-color.svg" alt="Backgroun Color" />
										<input type="color" class="primary_bg_color border border-dark" id="primary_bg" name="Primary background" t-att-value="config_vals.light_primary_bg_color"/>
									</div>
								</div>
								<div class="pb-2 d-flex">
									<div class="w-50 pt-3">Text Color</div>
									<div class="background-color">
										<img class="img img-fluid" src="/spiffy_theme_backend/static/src/image/background-color.svg" alt="Backgroun Color" />
										<input type="color" class="primary_text_color border border-dark" id="primary_text" name="Primary text" t-att-value="config_vals.light_primary_text_color"/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div id="theme_loaders_style" class="tab-pane">
					<div>
						<h3 class="mb-3 py-3 ps-3">Choose your loader</h3>
					</div>
					<div class="container g-4 loader_style mb-3 ps-3">
						<div class="row m-0">
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_1" name="loader_style"
										value="loader_style_1"
										t-att-checked="config_vals.loader_style == 'loader_style_1'" />
									<label class="custom-control-label" for="loader_style_1">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_1.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_2" name="loader_style"
										value="loader_style_2"
										t-att-checked="config_vals.loader_style == 'loader_style_2'" />
									<label class="custom-control-label" for="loader_style_2">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_2.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_3" name="loader_style"
										value="loader_style_3"
										t-att-checked="config_vals.loader_style == 'loader_style_3'" />
									<label class="custom-control-label" for="loader_style_3">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_3.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_4" name="loader_style"
										value="loader_style_4"
										t-att-checked="config_vals.loader_style == 'loader_style_4'" />
									<label class="custom-control-label" for="loader_style_4">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_4.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_5" name="loader_style"
										value="loader_style_5"
										t-att-checked="config_vals.loader_style == 'loader_style_5'" />
									<label class="custom-control-label" for="loader_style_5">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_5.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_6" name="loader_style"
										value="loader_style_6"
										t-att-checked="config_vals.loader_style == 'loader_style_6'" />
									<label class="custom-control-label" for="loader_style_6">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_6.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_7" name="loader_style"
										value="loader_style_7"
										t-att-checked="config_vals.loader_style == 'loader_style_7'" />
									<label class="custom-control-label" for="loader_style_7">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_7.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_8" name="loader_style"
										value="loader_style_8"
										t-att-checked="config_vals.loader_style == 'loader_style_8'" />
									<label class="custom-control-label" for="loader_style_8">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_8.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_9" name="loader_style"
										value="loader_style_9"
										t-att-checked="config_vals.loader_style == 'loader_style_9'" />
									<label class="custom-control-label" for="loader_style_9">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_9.gif" />
									</label>
								</div>
								</div>
								
							</div>
							<div class="col-md-6 mb-3 ps-md-0">
								<div class="form_check_content">
								<div class="form-check">
									<input type="radio" class="form-check-input" id="loader_style_10" name="loader_style"
										value="loader_style_10"
										t-att-checked="config_vals.loader_style == 'loader_style_10'" />
									<label class="custom-control-label" for="loader_style_10">
										<img style="width: 50px;" class="img img-fluid"
											src="/spiffy_theme_backend/static/src/image/loader_10.gif" />
									</label>
								</div>
								</div>
								
							</div>
						</div>
					</div>
				</div>

				<div class="d-flex justify-content-center py-3 discard_button_style">
					<a href="#" class="btn btn-primary selected_value border-0 m-0 btn-block w-50 mx-2" data-dismiss="modal">Apply</a>

					<a href="#" class="btn btn-primary backend_configurator_close border-0 m-0 btn-block w-50 mx-2" data-dismiss="modal">Discard</a>
				</div>
			</div>
		</div>
	</template>
</odoo>
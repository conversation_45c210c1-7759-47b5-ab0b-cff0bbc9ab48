# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_hms_base
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0-20210419\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-01 17:16+0000\n"
"PO-Revision-Date: 2021-09-01 12:24-0500\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0\n"
"Last-Translator: \n"
"Language: es\n"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__accesses_count
msgid "# Access Rights"
msgstr "# Derechos de acceso"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__groups_count
msgid "# Groups"
msgstr "# Grupos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__invoice_count
msgid "# Invoices"
msgstr "# Facturas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__rules_count
msgid "# Record Rules"
msgstr "# Reglas de registro"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__whatsapp_announcement_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__whatsapp_announcement_count
msgid "#whatsapp Announcement Count"
msgstr "#Whatsapp recuento de anuncios"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__whatsapp_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__whatsapp_count
msgid "#whatsapp Count"
msgstr "#Whatsapp Recuento"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.patient_kanban_view
msgid "<b>Age:</b>"
msgstr "<b>Edad:</b>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.patient_kanban_view
#: model_terms:ir.ui.view,arch_db:acs_hms_base.physician_kanban_view
msgid "<b>Code:</b>"
msgstr "<b>Código:</b>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.patient_kanban_view
msgid "<b>Gender:</b>"
msgstr "<b>Género:</b>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.patient_kanban_view
msgid "<b>Primary Physician:</b>"
msgstr "<b>Médico de cabecera:</b>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.physician_kanban_view
msgid "<b>Specialty:</b>"
msgstr "<b>Especialidad:</b>"

#. module: acs_hms_base
#: model:mail.template,body_html:acs_hms_base.email_template_birthday_wish
msgid ""
"<p style=\"margin:0px;font-size:13px;font-family:&quot;Lucida Grande&quot;, "
"Helvetica, Verdana, Arial, sans-serif;\">Estimado ${object.name},</p>\n"
"<img src=\"/acs_hms_base/static/scr/img/birthday1.gif\" style=\"border-style:"
"none;vertical-align:middle;\">\n"
"<p style=\"margin:0px;font-size:13px;font-family:&quot;Lucida Grande&quot;, "
"Helvetica, Verdana, Arial, sans-serif;\"> Deseándole lo mejor mientras "
"celebra su gran día. Feliz cumpleaños a todos nosotros!</p>\n"
"            "
msgstr ""
"<p style=\"margin:0px;font-size:13px;font-family:&quot;Lucida Grande&quot;, "
"Helvetica, Verdana, Arial, sans-serif;\">Estimado ${object.name},</p>\n"
"<img src=\"/acs_hms_base/static/scr/img/birthday1.gif\" style=\"border-style:"
"none;vertical-align:middle;\">\n"
"<p style=\"margin:0px;font-size:13px;font-family:&quot;Lucida Grande&quot;, "
"Helvetica, Verdana, Arial, sans-serif;\"> Deseándole lo mejor mientras "
"celebra su gran día. Feliz cumpleaños a todos nosotros!</p>\n"
"            "

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Birthday-wish template</span>"
msgstr "<span class=\"o_form_label\">Plantilla deseo de cumpleaños</span>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
msgid ""
"<span class=\"o_stat_text\">Inv:</span>\n"
"                                <span class=\"o_stat_text\">Due:</span>"
msgstr ""
"<span class=\"o_stat_text\">Fact:</span>\n"
"                                <span class=\"o_stat_text\">Debe:</span>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.report_invoice_document_inherit
msgid "<strong>Patient Code:</strong>"
msgstr "<strong>Código del paciente:</strong>"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.report_invoice_document_inherit
msgid "<strong>Patient Name:</strong>"
msgstr "<strong>Nombre del paciente:</strong>"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__res_partner__blood_group__ab+
msgid "AB+"
msgstr "AB+"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__res_partner__blood_group__ab-
msgid "AB-"
msgstr "AB-"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__api_key_ids
msgid "API Keys"
msgstr "Claves de API"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_account_payable_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_account_payable_id
msgid "Account Payable"
msgstr "Cuenta a pagar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_account_receivable_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_account_receivable_id
msgid "Account Receivable"
msgstr "Cuenta a cobrar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__acs_amount_due
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__acs_amount_due
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__acs_amount_due
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__acs_amount_due
msgid "Acs Amount Due"
msgstr "Importe de Acs adeudado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_needaction
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__active
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__active
msgid "Active"
msgstr "Activo"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.act_open_active_comp
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__name
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__active_component_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__active_component_ids
#: model:ir.ui.menu,name:acs_hms_base.menu_medicine_product_active_component
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_active_comp_form_search
msgid "Active Component"
msgstr "Componente activo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__active_lang_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__active_lang_count
msgid "Active Lang Count"
msgstr "Lenguaje activo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Excepción de actividad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_state
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_state
msgid "Activity State"
msgstr "Estado de actividad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_type_icon
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__additional_note
msgid "Additional Note"
msgstr "Nota adicional"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__additional_info
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__additional_info
msgid "Additional info"
msgstr "Información adicional"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__address_home_id
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "Address"
msgstr "Dirección"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__type
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__type
msgid "Address Type"
msgstr "Tipo de dirección"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__adverse_reaction
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__adverse_reaction
msgid "Adverse Reactions"
msgstr "Reacciones Adversas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__age
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__age
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__age
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__age
msgid "Age"
msgstr "Edad"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__lang
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__lang
msgid ""
"All the emails and documents sent to this contact will be translated in this "
"language."
msgstr ""
"Todos los correos electrónicos y documentos enviados a este contacto se "
"traducirán en este idioma."

#. module: acs_hms_base
#: model:res.groups,name:acs_hms_base.group_acs_invoice_exemption
msgid "Allow Invoice Exemption"
msgstr "Permitir facturas excentas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__amount
msgid "Amount of component"
msgstr "Cantidad de componente"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_active_comp__amount
msgid "Amount of component used in the drug (eg, 250 mg) per dose"
msgstr ""
"Cantidad de componente utilizado en el medicamento (p. Ej., 250 mg) por dosis"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_consumable_line__product_uom
msgid "Amount of medication (eg, 250 mg) per dose"
msgstr "Cantidad de medicamento (por ejemplo, 250 mg) por dosis"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_attachment_count
msgid "Attachment Count"
msgstr "Recuento de adjuntos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_documnt_mixin__attachment_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__attachment_ids
msgid "Attachments"
msgstr "Adjuntos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__barcode
msgid "Badge ID"
msgstr "Credencial ID"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__bank_account_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__bank_account_count
msgid "Bank"
msgstr "Banco"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__bank_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__bank_ids
msgid "Banks"
msgstr "Bancos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__barcode
msgid "Barcode"
msgstr "Código de barras"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_filter
msgid "Birthday"
msgstr "Cumpleaños"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_res_company__birthday_mail_template_id
#: model:ir.model.fields,field_description:acs_hms_base.field_res_config_settings__birthday_mail_template_id
msgid "Birthday Wishes Template"
msgstr "Plantilla de deseos de cumpleaños"

#. module: acs_hms_base
#: model:mail.template,subject:acs_hms_base.email_template_birthday_wish
msgid "Birthday Wishes!!!"
msgstr "Deseos de cumpleaños!!!"

#. module: acs_hms_base
#: model:ir.actions.server,name:acs_hms_base.ir_cron_birth_ir_actions_server
#: model:ir.cron,cron_name:acs_hms_base.ir_cron_birth
#: model:ir.cron,name:acs_hms_base.ir_cron_birth
msgid "Birthday scheduler"
msgstr "Programador de cumpleaños"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "Birthday wish template."
msgstr "Plantilla de deseo de cumpleaños."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__is_blacklisted
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__is_blacklisted
msgid "Blacklist"
msgstr "Lista negra"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__mobile_blacklisted
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "El teléfono en la lista negra es móvil"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__phone_blacklisted
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Teléfono en la lista negra es teléfono"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__blood_group
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__blood_group
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__blood_group
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__blood_group
msgid "Blood Group"
msgstr "Grupo sanguíneo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_bounce
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_bounce
msgid "Bounce"
msgstr "Rebote"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__can_edit
msgid "Can Edit"
msgstr "Puede editar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__can_publish
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__certificate
msgid "Certificate Level"
msgstr "Nivel de certificado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__channel_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__channel_ids
msgid "Channels"
msgstr "Canales"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__chatter_position
msgid "Chatter Position"
msgstr "Posición de Chatter"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__is_patient
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__is_patient
#: model:ir.model.fields,help:acs_hms_base.field_res_partner__is_patient
#: model:ir.model.fields,help:acs_hms_base.field_res_users__is_patient
msgid "Check if customer is linked with patient."
msgstr "Compruebe si el cliente está vinculado con el paciente."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__is_company
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr ""
"Marque esta casilla si el contacto es una compañía. En caso contrario, es "
"una persona."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__employee
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__employee
msgid "Check this box if this contact is an Employee."
msgstr "Marque si el contacto es un empleado."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__city
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__city
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "City"
msgstr "Ciudad"

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.act_open_active_comp
msgid "Click to add a Drug Active Component."
msgstr "Haga clic para agregar un componente activo de medicamento."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.act_open_drug_company_form_view
msgid "Click to add a Drug Company."
msgstr "Agregar una compañía farmacéutica."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.act_open_drug_form_view
msgid "Click to add a Drug Form."
msgstr "Haga clic para agregar una nueva presentación de medicamento."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_medicament_route
msgid "Click to add a Drug Route."
msgstr "Agregar una ruta de medicamentos."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_medicament_flavour
msgid "Click to add a Flavour."
msgstr "Haga clic para añadir un sabor."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_patient
msgid "Click to add a Patient. /&gt;"
msgstr "Haga clic para agregar un paciente. /&gt;"

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_physician_degree
msgid "Click to add a Physician Degree."
msgstr "Agregar el Título del Médico."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_physician
msgid "Click to add a Physician."
msgstr "Agregar un médico."

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_physician_specialty
msgid "Click to add a Specialty."
msgstr "Agregar una Especialidad."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__coach_id
msgid "Coach"
msgstr "Coach"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__code
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__code
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__code
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__code
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_search
msgid "Code"
msgstr "Código"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__color
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__commercial_partner_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidad comercial"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__company_ids
msgid "Companies"
msgstr "Compañías"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__ref_company_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__ref_company_ids
msgid "Companies that refers to partner"
msgstr "Compañías que se refieren a la empresa"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__company_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__company_id
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_filter
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_search
msgid "Company"
msgstr "Compañía"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__company_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__company_name
msgid "Company Name"
msgstr "Nombre de la compañía"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__commercial_company_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__commercial_company_name
msgid "Company Name Entity"
msgstr "Entidad del nombre de la compañía"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__company_type
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__company_type
msgid "Company Type"
msgstr "Tipo de compañía"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__partner_gid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__partner_gid
msgid "Company database ID"
msgstr "Base de datos empresas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_id
msgid "Company employee"
msgstr "Empleado de la empresa"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__drug_company_id
#: model:ir.model.fields,help:acs_hms_base.field_product_template__drug_company_id
msgid "Company producing this drug"
msgstr "Empresa productora de este medicamento"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__contact_address
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__contact_address
msgid "Complete Address"
msgstr "Dirección completa"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes"

#. module: acs_hms_base
#: model:ir.ui.menu,name:acs_hms_base.menu_medicine_cofig
#: model:ir.ui.menu,name:acs_hms_base.menu_physician_cofig
msgid "Configuration"
msgstr "Configuración"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__product_id
msgid "Consumable"
msgstr "Consumible"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_res_partner
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__child_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__child_ids
msgid "Contact"
msgstr "Contacto"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__corpo_company_id
msgid "Corporate Company"
msgstr "Empresa"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__is_corpo_tieup
msgid "Corporate Tie-Up"
msgstr "Patrono"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_bounce
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Contador del número de correos electrónicos rebotados de este contacto"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__country_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__country_id
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "Country"
msgstr "País"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__country_of_birth
msgid "Country of Birth"
msgstr "País de nacimiento"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__create_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__create_date
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__create_date
msgid "Created on"
msgstr "Creado el"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__credit_limit
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__credit_limit
msgid "Credit Limit"
msgstr "Crédito límite"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__currency_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_stock_customer
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_stock_customer
msgid "Customer Location"
msgstr "Ubicación de cliente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_payment_term_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Plazo de pago de cliente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__customer_rank
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__customer_rank
msgid "Customer Rank"
msgstr "Rango del cliente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__date
msgid "Date"
msgstr "Fecha"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__birthday
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__birthday
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__birthday
msgid "Date of Birth"
msgstr "Fecha de nacimiento"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__date_of_death
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__date_of_death
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__date_of_death
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__date_of_death
msgid "Date of Death"
msgstr "Fecha de muerte"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__resource_calendar_id
msgid "Default Working Hours"
msgstr "Horas de trabajo predeterminadas"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Define la planificación del recurso"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__degree_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__name
msgid "Degree"
msgstr "Título"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__trust
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__trust
msgid "Degree of trust you have in this debtor"
msgstr "Grado de confianza para este deudor"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__department_id
msgid "Department"
msgstr "Departamento"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_account_move__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_documnt_mixin__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_hms_mixin__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_qrcode_mixin__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_company__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__display_name
msgid "Display Name"
msgstr "Nombre"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_tree
msgid "Doctor ID"
msgstr "Registro médico"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "Doctor's Name"
msgstr "Nombre del doctor"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_acs_documnt_mixin
msgid "Document Mixin"
msgstr "Mezcla de documentos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_documnt_mixin__attach_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__attach_count
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
msgid "Documents"
msgstr "Documentos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__dosage
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__dosage
#: model:ir.model.fields,help:acs_hms_base.field_product_product__dosage
#: model:ir.model.fields,help:acs_hms_base.field_product_template__dosage
msgid "Dosage"
msgstr "Concentración"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_active_comp
msgid "Drug Active Component"
msgstr "Componente Activo"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.act_open_drug_company_form_view
#: model:ir.model,name:acs_hms_base.model_drug_company
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__drug_company_id
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__drug_company_id
#: model:ir.ui.menu,name:acs_hms_base.menu_medicine_drug_company
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_tree
msgid "Drug Company"
msgstr "Compañía farmacéutica"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.act_open_drug_form_view
#: model:ir.model,name:acs_hms_base.model_drug_form
#: model:ir.ui.menu,name:acs_hms_base.menu_medicine_druggg
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_form
msgid "Drug Form"
msgstr "Presentación"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_drug_route
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_form
msgid "Drug Route"
msgstr "Vía"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__form_id
#: model:ir.model.fields,help:acs_hms_base.field_product_template__form_id
msgid "Drug form, such as tablet or gel"
msgstr "Presentación farmacéutica, tales como tabletas o gel"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "Education"
msgstr "Educación"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__partner_share
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__partner_share
msgid ""
"Either customer (not a user), either shared user. Indicated the current "
"partner is a customer without access or with a limited access created for "
"sharing data."
msgstr ""
"Cualquiera de los clientes (no un usuario), o usuario compartido. Indicó que "
"el socio actual es un cliente sin acceso o con un acceso limitado creado "
"para compartir datos."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__email
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__email
msgid "Email"
msgstr "Correo electrónico"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__emergency_contact
msgid "Emergency Contact"
msgstr "Contacto de emergencia"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__emergency_phone
msgid "Emergency Phone"
msgstr "Teléfono de emergencia"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__employee
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee
msgid "Employee"
msgstr "Empleado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__emp_code
msgid "Employee Code"
msgstr "Código de empleado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_count
msgid "Employee Count"
msgstr "Recuento de empleados"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__category_ids
msgid "Employee Tags"
msgstr "Etiquetas de empleados"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__employee_bank_account_id
msgid "Employee bank salary account"
msgstr "Cuenta de salario del empleado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "Número de cuenta bancaria del empleado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_country_id
msgid "Employee's Country"
msgstr "País del empleado"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__address_home_id
msgid ""
"Enter here the private address of the employee, not the one linked to your "
"company."
msgstr ""
"Introduzca aquí la dirección privada del empleado, no la que está vinculada "
"a su empresa."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Usuario externo con acceso limitado, creado solo con el fin de compartir "
"datos."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__notes
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__notes
msgid "Extra Info"
msgstr "Información Extra"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__res_partner__gender__female
msgid "Female"
msgstr "Mujer"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__study_field
msgid "Field of Study"
msgstr "Campo de estudio"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__phone_sanitized
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Campo utilizado para almacenar el número de teléfono de instituciones de "
"salud. Ayuda a acelerar las búsquedas y comparaciones."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_account_position_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_account_position_id
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_medicament_flavour
msgid "Flavour"
msgstr "Sabor"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_channel_ids
msgid "Followers (Channels)"
msgstr "Seguidores (Canales)"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__activity_type_icon
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__product_template__hospital_product_type__fdrinks
msgid "Food & Drinks"
msgstr "Alimentos y Bebidas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__name
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__form_id
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__form_id
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_search
msgid "Form"
msgstr "Presentación"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__email_formatted
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "Dirección de email con formato \"Nombre <email@domain>\""

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__email_formatted
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__email_formatted
msgid "Formatted Email"
msgstr "Email formateado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__gender
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__gender
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__gender
msgid "Gender"
msgstr "Género"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_move_form
msgid "General Details"
msgstr "Detalles generales"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "General Information"
msgstr "Información general"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__partner_latitude
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__partner_latitude
msgid "Geo Latitude"
msgstr "Latitud geográfica"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__partner_longitude
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__partner_longitude
msgid "Geo Longitude"
msgstr "Longitud geográfica"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__gov_code
msgid "Government Identity"
msgstr "Identidad del gobierno"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_active_comp_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_filter
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_search
msgid "Group By..."
msgstr "Agrupar por..."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__groups_id
msgid "Groups"
msgstr "Grupos"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "HMS"
msgstr "SGH"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_acs_hms_mixin
msgid "HMS Mixin"
msgstr "SGH Mixin"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "HMS Mobile App"
msgstr "SGH Móvil App"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__has_unreconciled_entries
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Tiene entradas no conciliadas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__action_id
msgid "Home Action"
msgstr "Para hacer en el hogar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__km_home_work
msgid "Home-Work Distance"
msgstr "Distancia de trabajo al hogar"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.hms_external_layout_header
msgid "Hosp.Reg:"
msgstr "Reg. Hosp:"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_res_company
#: model:ir.module.category,name:acs_hms_base.module_category_hms
msgid "Hospital"
msgstr "Hospital"

#. module: acs_hms_base
#: model:ir.module.category,name:acs_hms_base.module_category_hms_extra
msgid "Hospital Extra"
msgstr "Hospital Extra"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_move_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
msgid "Hospital Info"
msgstr "Información Clínica"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_account_bank_statement_line__hospital_invoice_type
#: model:ir.model.fields,field_description:acs_hms_base.field_account_move__hospital_invoice_type
#: model:ir.model.fields,field_description:acs_hms_base.field_account_payment__hospital_invoice_type
msgid "Hospital Invoice Type"
msgstr "Tipo de factura hospitalaria"

#. module: acs_hms_base
#: model:ir.module.category,description:acs_hms_base.module_category_hms
msgid "Hospital Management System"
msgstr "Sistema de Gestión Hospitalario"

#. module: acs_hms_base
#: model:ir.module.category,description:acs_hms_base.module_category_hms_extra
msgid "Hospital Management System Extra"
msgstr "Sistema de Gestión Hospitalaria Extra"

#. module: acs_hms_base
#: model:ir.module.category,description:acs_hms_base.module_category_medical
msgid "Hospital Management System Realted Modules."
msgstr "Módulos relacionados al Sistema de Gestión Hospitalaria."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__hospital_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__hospital_name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__hospital_name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__hospital_name
msgid "Hospital Name"
msgstr "Nombre del Hospital"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__hospital_product_type
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__hospital_product_type
msgid "Hospital Product Type"
msgstr "Tipo de producto hospitalario"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__hr_presence_state
msgid "Hr Presence State"
msgstr "Estado de Presencia Hr"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_account_move__id
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_documnt_mixin__id
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_hms_mixin__id
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_qrcode_mixin__id
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__id
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__id
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__id
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__id
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__id
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__id
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__id
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__id
#: model:ir.model.fields,field_description:acs_hms_base.field_res_company__id
#: model:ir.model.fields,field_description:acs_hms_base.field_res_config_settings__id
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__id
msgid "ID"
msgstr "ID (identificación)"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__barcode
msgid "ID used for employee identification."
msgstr "ID utilizado para la identificación de los empleados."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__im_status
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__im_status
msgid "IM Status"
msgstr "Estado del chat"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__activity_exception_icon
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__code
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__code
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__code
msgid "Identification Code"
msgstr "Código de Identificación"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__identification_id
msgid "Identification No"
msgstr "# Identificación"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__code
#: model:ir.model.fields,help:acs_hms_base.field_res_partner__code
#: model:ir.model.fields,help:acs_hms_base.field_res_users__code
msgid "Identifier provided by the Health Center."
msgstr "Identificador del Paciente Proporcionado por el Centro Médico"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_needaction
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_unread
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_needaction
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado hay nuevos mensajes que requieren su atención."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_has_error
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_has_sms_error
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_has_error
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra marcado, algunos mensajes tienen error de envío."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__is_corpo_tieup
msgid ""
"If not checked, these Corporate Tie-Up Group will not be visible at all."
msgstr ""
"Si no se marca, estos grupos corporativos de enlace no serán visibles en "
"absoluto."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__team_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignments related to "
"this partner"
msgstr ""
"Si se establece, este equipo de ventas se utilizará para ventas y "
"asignaciones relacionadas con este socio"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__action_id
msgid ""
"If specified, this action will be opened at log on for this user, in "
"addition to the standard menu."
msgstr ""
"Si se especifica, esta acción se abrirá al iniciar sesión para este usuario, "
"además del menú estándar."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__is_blacklisted
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Si la dirección de email esta en la lista negra, el contacto ya no recibirá "
"correo masivo de cualquier lista."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__phone_sanitized_blacklisted
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive "
"mass mailing sms anymore, from any list"
msgstr ""
"Si el número de teléfono está en la lista negra, el contacto ya no recibirá "
"sms de correo masivo, de cualquier lista"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__image_1920
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__image_1920
msgid "Image"
msgstr "Imagen"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__image_1024
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__image_128
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__image_256
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__image_512
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__mobile_blacklisted
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Indica si un número de teléfono en la lista negra es un número de teléfono "
"móvil. Ayuda a distinguir qué número aparece en la lista negra cuando hay un "
"campo móvil y telefónico en un modelo."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__phone_blacklisted
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Indica si un número de teléfono en la lista negra es un número de teléfono. "
"Ayuda a distinguir qué número aparece en la lista negra cuando hay un campo "
"móvil y telefónico en un modelo."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__indications
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__indications
msgid "Indication"
msgstr "Indicación"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__indications
#: model:ir.model.fields,help:acs_hms_base.field_product_template__indications
#: model_terms:ir.ui.view,arch_db:acs_hms_base.product_template_form_view_inherit
msgid "Indications"
msgstr "Indicaciones"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__industry_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__industry_id
msgid "Industry"
msgstr "Industria"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__invoice_warn
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__invoice_warn
msgid "Invoice"
msgstr "Factura"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__type
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__type
msgid ""
"Invoice & Delivery addresses are used in sales orders. Private addresses are "
"only visible by authorized users."
msgstr ""
"Las direcciones de factura y entrega se utilizan en pedidos de ventas. Las "
"direcciones privadas solo son visibles para los usuarios autorizados."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__invoice_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__invoice_ids
msgid "Invoices"
msgstr "Facturas"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_filter
msgid "Is Famale"
msgstr "Es Femenino"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_is_follower
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_filter
msgid "Is Male"
msgstr "Es Masculino"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__is_patient
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__is_patient
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__is_patient
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__is_patient
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_res_partner_filter
msgid "Is Patient"
msgstr "Paciente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__is_published
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__is_published
msgid "Is Published"
msgstr "Esta Publicado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__is_company
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__is_company
msgid "Is a Company"
msgstr "Es una compañia"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__is_moderator
msgid "Is moderator"
msgstr "Es moderador"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__function
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__function
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__job_title
msgid "Job Title"
msgstr "Título del trabajo"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_account_move
msgid "Journal Entry"
msgstr "Entrada al diario"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__journal_item_count
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__journal_item_count
msgid "Journal Items"
msgstr "Apuntes contables"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__password
msgid ""
"Keep empty if you don't want the user to be able to connect on the system."
msgstr ""
"Manténgase vacío si no desea que el usuario pueda conectarse al sistema."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__lactation_warning
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__lactation_warning
msgid "Lactation Warning"
msgstr "Advertencia en lactancia"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__lang
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__lang
msgid "Language"
msgstr "Idioma"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__last_activity
msgid "Last Activity"
msgstr "Última actividad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__last_activity_time
msgid "Last Activity Time"
msgstr "Fecha de última actividad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_account_move____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_documnt_mixin____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_hms_mixin____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_qrcode_mixin____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_res_company____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__write_uid
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_active_comp__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_form__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_degree__write_date
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__calendar_last_notif_ack
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "Ultima notificación marcada como leída desde el calendario base"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__last_time_entries_checked
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"Última vez que se conciliaros facturas y pagos de este asociado. Se "
"configura incluso si no hay ningún débito o crédito por conciliar, o si "
"pulsa el botón \"Hecho\"."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__last_time_entries_checked
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Fecha de la última conciliación de facturas y pagos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__login_date
msgid "Latest authentication"
msgstr "Autenticación más reciente"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_hms_consumable_line
msgid "List of Consumables"
msgstr "Lista de consumibles"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__login
msgid "Login"
msgstr "Ingresar"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_physician_degree_form
msgid "MBBS"
msgstr "MBBS"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__res_partner__gender__male
msgid "Male"
msgstr "Hombre"

#. module: acs_hms_base
#: model:res.groups,name:acs_hms_base.group_manage_medicines
msgid "Manage Medicines"
msgstr "Administrar medicamentos"

#. module: acs_hms_base
#: model:res.groups,name:acs_hms_base.group_manage_ethnic_religion_tribe
msgid "Manage Religion/Tribe/Ethnic group"
msgstr "Administrar religión/tribu/grupo étnico"

#. module: acs_hms_base
#: model:res.groups,name:acs_hms_base.group_manage_services
msgid "Manage Services"
msgstr "Administrar servicios"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_parent_id
#: model:res.groups,name:acs_hms_base.group_hms_manager
msgid "Manager"
msgstr "Gerente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__marital_status
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__marital
msgid "Marital Status"
msgstr "Estado Civil"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__hms_patient__marital_status__married
msgid "Married"
msgstr "Casado"

#. module: acs_hms_base
#: model:ir.module.category,name:acs_hms_base.module_category_medical
msgid "Medical"
msgstr "Médica"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__medical_license
msgid "Medical License"
msgstr "Licencia médica"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__product_template__hospital_product_type__medicament
msgid "Medicament"
msgstr "Medicamento"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.product_template_form_view_inherit
msgid "Medicament Details"
msgstr "Detalles del medicamento"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_medicament_flavour
#: model:ir.ui.menu,name:acs_hms_base.menu_medicine_medicament_flavour
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_medicament_flavour_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_medicament_flavour_tree
msgid "Medicament Flavour"
msgstr "Sabor del medicamento"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_medicament_flavour_form
msgid "Medicament Flavours"
msgstr "Sabores de medicamentos"

#. module: acs_hms_base
#: model:ir.ui.menu,name:acs_hms_base.menu_medicine_medicament_route
msgid "Medicament Route"
msgstr "Vía del medicamento"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_physician_specialty_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_specialty_tree
msgid "Medicament Specialty"
msgstr "Especialidad del medicamento"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_physician_specialty_form
msgid "Medicament Specialtys"
msgstr "Especialidades de Medicamentos"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.product_template_action_medicines
#: model:ir.ui.menu,name:acs_hms_base.acs_medicine_root
#: model:ir.ui.menu,name:acs_hms_base.menu_acs_medicine
msgid "Medicines"
msgstr "Medicinas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_has_error
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__invoice_warn_msg
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__invoice_warn_msg
msgid "Message for Invoice"
msgstr "Mensaje para factura"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__picking_warn_msg
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Mensaje para recolección de stock"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__mobile
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__mobile
msgid "Mobile"
msgstr "Móvil"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__moderation_channel_ids
msgid "Moderated channels"
msgstr "Canales moderados"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__moderation_counter
msgid "Moderation count"
msgstr "Recuento de moderación"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__name
#: model:ir.model.fields,field_description:acs_hms_base.field_medicament_flavour__name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__name
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__name
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_medicament_flavour_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_physician_degree_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_physician_specialty_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_active_comp_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "Name"
msgstr "Nombre"

#. module: acs_hms_base
#: model:ir.model.constraint,message:acs_hms_base.constraint_drug_form_name_uniq
#: model:ir.model.constraint,message:acs_hms_base.constraint_drug_route_name_uniq
#: model:ir.model.constraint,message:acs_hms_base.constraint_physician_degree_name_uniq
#: model:ir.model.constraint,message:acs_hms_base.constraint_physician_specialty_name_uniq
msgid "Name must be unique!"
msgstr "El nombre debe ser único!"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_summary
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_type_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: acs_hms_base
#: model_terms:ir.actions.act_window,help:acs_hms_base.act_open_active_comp
#: model_terms:ir.actions.act_window,help:acs_hms_base.act_open_drug_form_view
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_medicament_flavour
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_medicament_route
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_patient
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_physician
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_physician_degree
#: model_terms:ir.actions.act_window,help:acs_hms_base.action_physician_specialty
msgid "No Record Found"
msgstr "Registro no encontrado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__email_normalized
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__email_normalized
msgid "Normalized Email"
msgstr "Email normalizado"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__product_template__hospital_product_type__not_medical
msgid "Not Medical"
msgstr "No médico"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__comment
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__comment
#: model_terms:ir.ui.view,arch_db:acs_hms_base.product_template_form_view_inherit
msgid "Notes"
msgstr "Notas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__notification_type
msgid "Notification"
msgstr "Notificación"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__children
msgid "Number of Children"
msgstr "Número de Hijos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__companies_count
msgid "Number of Companies"
msgstr "Número de empresas"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__accesses_count
msgid "Number of access rights that apply to the current user"
msgstr "Número de derechos de acceso que se aplican al usuario actual"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__groups_count
msgid "Number of groups that apply to the current user"
msgstr "Número de grupos que se aplican al usuario actual"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_needaction_counter
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_has_error_counter
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__rules_count
msgid "Number of record rules that apply to the current user"
msgstr "Número de reglas de registro que se aplican al usuario actual"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__message_unread_counter
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes no leidos"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__barcode
msgid "Number used for Patient identification."
msgstr "Número utilizado para la identificación del paciente."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__occupation
msgid "Occupation"
msgstr "Ocupación"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__odoobot_state
msgid "OdooBot Status"
msgstr "Estado de MedicBot"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__odoobot_failed
msgid "Odoobot Failed"
msgstr "Medicbot falló"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "On Apple Store"
msgstr "En Apple Store"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "On Google Play"
msgstr "En Google Play"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__res_partner__gender__other
msgid "Other"
msgstr "Otro"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.res_config_settings_view_form
msgid "Other Configurations"
msgstr "Otras configuraciones"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__product_template__hospital_product_type__os
msgid "Other Service"
msgstr "Otro servicio"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__pin
msgid "PIN"
msgstr "PIN"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__pin
msgid "PIN used to Check In/Out in Kiosk Mode (if enabled in Configuration)."
msgstr ""
"PIN utilizado para Check In/Out en modo quiosco (si está habilitado en "
"Configuración)."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__parent_name
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__parent_name
msgid "Parent name"
msgstr "Nombre del padre"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_company__partner_id
msgid "Partner"
msgstr "Cliente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__contract_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__contract_ids
msgid "Partner Contracts"
msgstr "Contratos de socios"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__active_partner
msgid "Partner is Active"
msgstr "Partner esta activo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__same_vat_partner_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__same_vat_partner_id
msgid "Partner with same Tax ID"
msgstr "Partner con el mismo ID de impuestos"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__partner_id
msgid "Partner-related data of the Patient"
msgstr "Datos del cliente relacionados a la mascota"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__partner_id
msgid "Partner-related data of the user"
msgstr "Datos del partner relativos al usuario"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__passport_id
msgid "Passport No"
msgstr "Pasaporte #"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__password
msgid "Password"
msgstr "Contraseña"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_patient
#: model:ir.model,name:acs_hms_base.model_hms_patient
#: model:ir.model.fields,field_description:acs_hms_base.field_account_bank_statement_line__patient_id
#: model:ir.model.fields,field_description:acs_hms_base.field_account_move__patient_id
#: model:ir.model.fields,field_description:acs_hms_base.field_account_payment__patient_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__acs_patient_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__acs_patient_id
#: model:ir.model.fields,field_description:acs_hms_base.field_res_partner__acs_patient_id
#: model:ir.model.fields,field_description:acs_hms_base.field_res_users__acs_patient_id
#: model:ir.model.fields.selection,name:acs_hms_base.selection__account_move__hospital_invoice_type__patient
#: model:ir.ui.menu,name:acs_hms_base.action_main_menu_patient
#: model:ir.ui.menu,name:acs_hms_base.main_menu_patient
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_filter
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_tree
msgid "Patient"
msgstr "Paciente"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__debit_limit
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__debit_limit
msgid "Payable Limit"
msgstr "Límite a pagar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_payment_method_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_payment_method_id
msgid "Payment Method"
msgstr "Método de Pago"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__phone
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__phone
msgid "Phone"
msgstr "Teléfono"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__phone_sanitized_blacklisted
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Teléfono en lista negra"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.hms_external_layout_header
msgid "Phone:"
msgstr "Teléfono:"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_physician
#: model:ir.model,name:acs_hms_base.model_hms_physician
#: model:ir.model.fields,field_description:acs_hms_base.field_account_bank_statement_line__physician_id
#: model:ir.model.fields,field_description:acs_hms_base.field_account_move__physician_id
#: model:ir.model.fields,field_description:acs_hms_base.field_account_payment__physician_id
#: model:ir.ui.menu,name:acs_hms_base.action_menu_physician
#: model:ir.ui.menu,name:acs_hms_base.main_menu_physician
#: model_terms:ir.ui.view,arch_db:acs_hms_base.physician_kanban_view
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_tree
msgid "Physician"
msgstr "Profesional"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__code
msgid "Physician Code"
msgstr "Registro médico"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_physician_degree
#: model:ir.model,name:acs_hms_base.model_physician_degree
#: model:ir.ui.menu,name:acs_hms_base.menu_physician_degree
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_acs_physician_degree_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_degree_tree
msgid "Physician Degree"
msgstr "Título médico"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_physician_specialty
msgid "Physician Specialty"
msgstr "Especialidad del Médico"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__place_of_birth
msgid "Place of Birth"
msgstr "Lugar de nacimiento"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Política sobre cómo manejar las notificaciones de chat:\n"
"- Manejar por correos electrónicos: las notificaciones se envían a su "
"dirección de correo electrónico\n"
"- Manejar en Odoo: las notificaciones aparecen en su bandeja de entrada de "
"Odoo"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_payment_method_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr ""
"Método de pago preferido al pagar a este proveedor. Esto se utiliza para "
"filtrar las facturas de proveedor por método de pago preferido para "
"registrar pagos en masa. Casos de uso: cree archivos bancarios para cables "
"por lotes, compruebe las ejecuciones."

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.product_template_form_view_inherit
msgid "Pregnancy"
msgstr "Embarazo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__pregnancy_warning
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__pregnancy_warning
msgid "Pregnancy Warning"
msgstr "Advertencia de embarazo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__pregnancy
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__pregnancy
msgid "Pregnancy and Lactancy"
msgstr "Embarazo y lactancia"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.product_template_form_view_inherit
msgid "Pregnancy/Lactation Warning"
msgstr "Advertencia de embarazo/lactancia"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
msgid "Preview Documents"
msgstr "Vista previa de documentos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_product_pricelist
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_product_pricelist
msgid "Pricelist"
msgstr "Tarifa"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__primary_doctor
msgid "Primary Care Doctor"
msgstr "Médico de atención primaria"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__private_email
msgid "Private Email"
msgstr "Email privado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_phone
msgid "Private Phone"
msgstr "Teléfono privado"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_qrcode_mixin__qr_image
msgid "QR Code"
msgstr "Código QR"

#. module: acs_hms_base
#: model:ir.model,name:acs_hms_base.model_acs_qrcode_mixin
msgid "QrCode Mixin"
msgstr "CódigoQr Mixin"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__qty
msgid "Quantity"
msgstr "Cantidad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__ref
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__ref
msgid "Reference"
msgstr "Referencia"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__parent_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__parent_id
msgid "Related Company"
msgstr "Empresa relacionada"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__partner_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__partner_id
msgid "Related Partner"
msgstr "Contacto Relacionado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__user_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__user_id
msgid "Related User"
msgstr "Usuario relacionado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__employee_ids
msgid "Related employee"
msgstr "Empleado relacionado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__religion
msgid "Religion"
msgstr "Religión"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__resource_ids
msgid "Resources"
msgstr "Recursos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__activity_user_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__website_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir la publicación a este sitio web."

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_medicament_route
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__route_id
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__route_id
msgid "Route"
msgstr "Vía"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__team_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__team_id
msgid "Sales Team"
msgstr "Equipo de ventas"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__sale_team_id
msgid ""
"Sales Team the user is member of. Used to compute the members of a Sales "
"Team through the inverse one2many"
msgstr ""
"Equipo de ventas del que es miembro el usuario. Se utiliza para calcular los "
"miembros de un equipo de ventas a través de la inversa una a muchos"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__phone_sanitized
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__phone_sanitized
msgid "Sanitized Number"
msgstr "Número de sanidad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__study_school
msgid "School"
msgstr "Colegio"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""
"Seleccione el \"Empleado\" que es el entrenador de este empleado.\n"
"El \"Entrenador\" no tiene derechos o responsabilidades específicas por "
"defecto."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__invoice_warn
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__picking_warn
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__invoice_warn
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Si selecciona la opción \"Aviso\" se notificará a los usuarios con el "
"mensaje, si selecciona \"Mensaje de bloqueo\" se lanzará una excepción con "
"el mensaje y se bloqueará el flujo. El mensaje debe escribirse en el "
"siguiente campo."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__self
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__self
msgid "Self"
msgstr "Sí mismo"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.product_template_action_services
#: model:ir.ui.menu,name:acs_hms_base.acs_services_root
#: model:ir.ui.menu,name:acs_hms_base.menu_acs_services
msgid "Services"
msgstr "Servicios"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__new_password
msgid "Set Password"
msgstr "Establecer Contraseña"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_hms_config_settings
msgid "Settings"
msgstr "Ajustes"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__partner_share
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__partner_share
msgid "Share Partner"
msgstr "Compartir partner"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__share
msgid "Share User"
msgstr "Usuario Compartido"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__sidebar_visible
msgid "Show App Sidebar"
msgstr "Mostrar barra lateral"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__signature
msgid "Signature"
msgstr "Firma"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__signup_expiration
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__signup_expiration
msgid "Signup Expiration"
msgstr "Expiración del ingreso"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__signup_token
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__signup_token
msgid "Signup Token"
msgstr "Palabra de ingreso"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__signup_type
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__signup_type
msgid "Signup Token Type"
msgstr "Tipo de la palabra de ingreso"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__signup_valid
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__signup_valid
msgid "Signup Token is Valid"
msgstr "La palabra de ingreso es válida"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__signup_url
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__signup_url
msgid "Signup URL"
msgstr "URL de ingreso"

#. module: acs_hms_base
#: model:ir.model.fields.selection,name:acs_hms_base.selection__hms_patient__marital_status__single
msgid "Single"
msgstr "Soltero"

#. module: acs_hms_base
#: model:ir.actions.act_window,name:acs_hms_base.action_physician_specialty
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__specialty_id
#: model:ir.model.fields,field_description:acs_hms_base.field_physician_specialty__name
#: model:ir.ui.menu,name:acs_hms_base.menu_physician_specialty
msgid "Specialty"
msgstr "Especialidad"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__specialty_id
msgid "Specialty Code"
msgstr "Código de Especialidad"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__new_password
msgid ""
"Specify a value only when creating a user or if you're changing the user's "
"password, otherwise leave empty. After a change of password, the user has to "
"login again."
msgstr ""
"Especifique un valor solo al crear un usuario o si va a cambiar la "
"contraseña del usuario, de lo contrario deje vacío. Después de un cambio de "
"contraseña, el usuario tiene que iniciar sesión de nuevo."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Fecha de nacimiento del cónyuge"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "Nombre completo del cónyuge"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__state_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__state_id
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "State"
msgstr "Estado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__state
msgid "Status"
msgstr "Estado"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__activity_state
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__move_id
msgid "Stock Move"
msgstr "Movimiento de Stock"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__picking_warn
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__picking_warn
msgid "Stock Picking"
msgstr "Recolección de Stock"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__storage
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__storage
msgid "Storage"
msgstr "Almacenes"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__street
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__street
msgid "Street"
msgstr "Dirección"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
msgid "Street 2..."
msgstr "Calle 2..."

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "Street..."
msgstr "Dónde vive…"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__street2
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__street2
msgid "Street2"
msgstr "Calle2"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__supplier_rank
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__supplier_rank
msgid "Supplier Rank"
msgstr "Rango del proveedor"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__category_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__category_id
msgid "Tags"
msgstr "Categorías"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__vat
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__vat
msgid "Tax ID"
msgstr "Céd/Pas/RUC"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__vat
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"El número de identificación fiscal. Complételo si el contacto está sujeto a "
"los impuestos del gobierno. Utilizado en algunas declaraciones legales."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__company_id
msgid "The default company for this user."
msgstr "La empresa predeterminada para este usuario."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__lactation_warning
#: model:ir.model.fields,help:acs_hms_base.field_product_template__lactation_warning
msgid "The drug represents risk in lactation period"
msgstr "El medicamento representa riesgo en el período de lactancia"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__pregnancy_warning
#: model:ir.model.fields,help:acs_hms_base.field_product_template__pregnancy_warning
msgid "The drug represents risk to pregnancy"
msgstr "El medicamento representa un riesgo para el embarazo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__is_address_home_a_company
msgid "The employee address has a company linked"
msgstr "La dirección del empleado tiene una empresa vinculada"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_account_position_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""
"La posición fiscal determina los impuestos / cuentas utilizados para este "
"contacto."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__website_url
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: acs_hms_base
#: model:ir.model.constraint,message:acs_hms_base.constraint_medicament_flavour_name_acs_medi_flavour_uniq
msgid "The name of the Content must be unique !"
msgstr "El nombre del Contenido debe ser único !"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__has_unreconciled_entries
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"El asociado tiene al menos un débito o crédito no conciliado desde la última "
"vez que se realizó la conciliación de facturas y pagos."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_stock_customer
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"La ubicación  utilizada como destino al enviar mercancías a este contacto."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_stock_supplier
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"La ubicación  utilizada como fuente al recibir mercancías de este contacto."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_product_product__therapeutic_action
#: model:ir.model.fields,field_description:acs_hms_base.field_product_template__therapeutic_action
msgid "Therapeutic Effect"
msgstr "Efecto Terapéutico"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__therapeutic_action
#: model:ir.model.fields,help:acs_hms_base.field_product_template__therapeutic_action
msgid "Therapeutic action"
msgstr "Acción terapéutica"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_account_payable_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Esta cuenta se utilizará en lugar de la cuenta por defecto como la cuenta "
"pendiente de pago del asociado actual."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_account_receivable_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Esta cuenta se utilizará en lugar de la cuenta por defecto como la cuenta "
"pendiente de cobro del asociado actual."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__email_normalized
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can "
"contain more than strictly an email address."
msgstr ""
"Este campo se utiliza para buscar en la dirección de correo electrónico, ya "
"que el campo de correo electrónico principal puede contener más que "
"estrictamente una dirección de correo electrónico."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_supplier_payment_term_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"Se utilizará esta condición de pago, en lugar de la predeterminada, para los "
"pedidos de compra y las facturas de proveedor."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_payment_term_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"Este término de pago se utilizará en lugar del predeterminado para pedidos "
"de cliente y facturas de clientes"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__property_product_pricelist
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Esta tarifa se utilizará, en lugar de la por defecto, para las ventas de la "
"empresa actual."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_res_company__birthday_mail_template_id
#: model:ir.model.fields,help:acs_hms_base.field_res_config_settings__birthday_mail_template_id
msgid "This will set the default mail template for birthday wishes."
msgstr ""
"Esto establecerá la plantilla de correo predeterminada para los deseos de "
"cumpleaños."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__tz
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__tz_offset
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__tz_offset
msgid "Timezone offset"
msgstr "Compensación de zona horaria"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__title
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__title
msgid "Title"
msgstr "Título"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_account_invoice_filter
msgid "Today's Invoices"
msgstr "Facturas de hoy"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_company_form
msgid "Torrent Pharma"
msgstr "Torrent Pharma"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__total_invoiced
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__total_invoiced
msgid "Total Invoiced"
msgstr "Total facturado"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__debit
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__debit
msgid "Total Payable"
msgstr "Total a pagar"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__credit
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__credit
msgid "Total Receivable"
msgstr "Total a cobrar"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__credit
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__credit
msgid "Total amount this customer owes you."
msgstr "Cantidad total que le debe este cliente."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__debit
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__debit
msgid "Total amount you have to pay to this vendor."
msgstr "Cantidad total a pagar a este proveedor."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__totp_secret
msgid "Totp Secret"
msgstr "Súper Secreto"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__caste
msgid "Tribe"
msgstr "Cultura"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__totp_enabled
msgid "Two-factor authentication"
msgstr "Autenticación de dos factores"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_acs_qrcode_mixin__unique_code
msgid "Unique UID"
msgstr "UID único"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_drug_route__name
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_search
msgid "Unit"
msgstr "Unidad"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_consumable_line__product_uom
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_unread
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de mensajes sin leer"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__login
msgid "Used to log into the system"
msgstr "Utilizado para conectarse al sistema"

#. module: acs_hms_base
#: model:res.groups,name:acs_hms_base.group_hms_user
msgid "User"
msgstr "Usuario"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__log_ids
msgid "User log entries"
msgstr "Entradas de registro de usuario"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__sale_team_id
msgid "User's Sales Team"
msgstr "Equipo de ventas del usuario"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__user_id
msgid "User-related data of the patient"
msgstr "Datos del paciente relacionados con el usuario."

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__user_id
msgid "User-related data of the physician"
msgstr "Datos relacionados con el usuario del médico"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__user_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__user_ids
msgid "Users"
msgstr "Usuarios"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__currency_id
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__currency_id
msgid "Utility field to express amount currency"
msgstr "Campo útil para expresar importe de la moneda."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_stock_supplier
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_stock_supplier
msgid "Vendor Location"
msgstr "Ubicación de proveedor"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__property_supplier_payment_term_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Términos de Pago"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__visa_expire
msgid "Visa Expire Date"
msgstr "Fecha de vencimiento de la VISA"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__visa_no
msgid "Visa No"
msgstr "Visa #"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__website_published
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__visitor_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__visitor_ids
msgid "Visitors"
msgstr "Visitantes"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_product_product__pregnancy
#: model:ir.model.fields,help:acs_hms_base.field_product_template__pregnancy
msgid "Warnings for Pregnant Women"
msgstr "Advertencias para mujeres embarazadas"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__acs_webcam_url
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__acs_webcam_url
msgid "Webcam Field"
msgstr "Campo de webcam"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__website_id
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__website_id
msgid "Website"
msgstr "Sitio web"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__website
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__website
msgid "Website Link"
msgstr "Enlace al sitio web"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__website_message_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__website_url
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__website_url
msgid "Website URL"
msgstr "URL del Sitio Web"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__website_message_ids
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: acs_hms_base
#: model:ir.model.fields,help:acs_hms_base.field_hms_patient__tz
#: model:ir.model.fields,help:acs_hms_base.field_hms_physician__tz
msgid ""
"When printing documents and exporting/importing data, time values are "
"computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your "
"web client."
msgstr ""
"Al imprimir documentos y exportar/importar datos, los valores de tiempo se "
"calculan según esta zona horaria.\n"
"Si no se establece la zona horaria, se utiliza UTC (hora universal "
"coordinada).\n"
"En cualquier otro lugar, los valores de tiempo se calculan de acuerdo con el "
"desplazamiento de tiempo de su cliente web."

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__address_id
msgid "Work Address"
msgstr "Dirección de trabajo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__work_email
msgid "Work Email"
msgstr "Email profesional"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__work_location
msgid "Work Location"
msgstr "Ubicación del trabajo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__mobile_phone
msgid "Work Mobile"
msgstr "Móvil de trabajo"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__permit_no
msgid "Work Permit No"
msgstr "Permiso de trabajo #"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__work_phone
msgid "Work Phone"
msgstr "Número de Oficina"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_patient_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "ZIP"
msgstr "Código Postal"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__zip
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__zip
msgid "Zip"
msgstr "C.P."

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_form_tree
msgid "acs Drug Form"
msgstr "Presentación de medicamento"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_form
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_search
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_drug_route_tree
msgid "acs Drug Route"
msgstr "Vía de medicamentos"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "e.g. +506 5555 5555"
msgstr "Este formato: +50755555555"

#. module: acs_hms_base
#: model_terms:ir.ui.view,arch_db:acs_hms_base.view_physician_form
msgid "e.g. MBBS"
msgstr "por ejemplo, MBBS"

#. module: acs_hms_base
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_patient__whatsapp_ids
#: model:ir.model.fields,field_description:acs_hms_base.field_hms_physician__whatsapp_ids
msgid "whatsapp"
msgstr "Whatsapp"

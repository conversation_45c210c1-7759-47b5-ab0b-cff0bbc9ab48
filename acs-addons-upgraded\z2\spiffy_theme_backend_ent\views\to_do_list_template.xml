<?xml version="1.0" encoding="UTF-8"?>
<!-- # Part of Odoo Module Developed by Bizople Solutions Pvt. Ltd.
# See LICENSE file for full copyright and licensing details. -->
<odoo>
	<template id="to_do_list_content_template" name="To Do List Content Template">
		<t t-set="note_color_pallet" t-value="note.note_color_pallet"/>
		<t t-set="note_create_date" t-value="note.write_date + user_tz_offset_time"/>
		<section t-attf-class="note_content #{note_color_pallet}" t-att-data-note-id="note.id">
			<div class="note_outer_content p-3 position-relative">
				<div class="date-and-edit d-flex justify-content-end">
					<div class="note-date">
						<t t-if="today_date.strftime('%d-%b-%Y') == note_create_date.strftime('%d-%b-%Y')">
							<span t-out="note_create_date.strftime('%I:%M %p')"></span>
						</t>
						<t t-else=" ">
							<span t-out="note_create_date.strftime('%d-%b-%Y')"></span>
						</t>
					</div>
					<div class="note-options d-md-none ms-2">
						<div class="btn-group">
							<button class="btn dropdown-toggle p-0 bg-transparent" type="button" data-bs-toggle="dropdown" aria-expanded="false">
								<span class="ri ri-more-fill align-middle"/>
							</button>
							<ul class="dropdown-menu">
								<li class="edit-note-item note-edit">
									<a class="nav-item d-flex p-2" href="#" t-att-data-note-id="note.id" t-att-data-note-color="note.note_color_pallet">
										<span class="ri ri-pencil-line align-middle"/>
										<span class="ps-2 edit-note-text">Edit Note</span>
									</a>
								</li>
								<li class="edit-note-item note-delete">
									<a class="nav-item d-flex p-2" href="#" t-att-data-note-id="note.id">
										<span class="ri ri-delete-bin-line align-middle"/>
										<span class="ps-2 edit-note-text">Delete Note</span>
									</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
				<div class="note-details">
					<!-- <div t-out="note.sequence"></div> -->
					<div class="note-title">
						<h2 t-field="note.name"></h2>
					</div>
					<div class="note-description">
						<span class="description-main" t-field="note.description"></span>
					</div>
				</div>

			</div>
		</section>
	</template>



	<template id="to_do_list_template" name="To Do List template">
		<div class="row g-0 align-items-start to-do-list-sidebar h-100">
			<div class="to-do-sidebar-heading p-3 border-bottom d-flex">
				<h4 class="to-do-title d-flex m-0 align-items-center flex-fill">
					<span>Notes</span>

                    <button type="button" id="close_to_do_sidebar" class="bg-transparent ms-auto border-0">
                        <img src="/spiffy_theme_backend/static/src/image/close.png" class="img img-fluid" />
                    </button>
				</h4>
			</div>
			<div class="to-do-sidebar-body position-relative">
				<button type="button" class="add-new-list-btn btn btn-primary rounded-circle">
					<span class="ri ri-add-line"></span>
					<span class="text d-none">CLOSE</span>
				</button>
				<div class="add-list d-none p-3 shadow-sm bg-white">
					<input t-if="user" type="hidden" name="user_id" t-att-value="user.id"></input>
					<input type="hidden" name="note_id" value=""></input>

					<div class="mb-3 p-3 shadow-sm add-list-outer">
						<div class="note-colors-option d-flex">
							<t t-set="pallets" t-value="8"/>
							<t t-foreach="range(1, pallets)" t-as="p">
								<label t-attf-for="note_pallet_#{p}" t-attf-class="flex-fill pallet_#{p}" t-attf-color-pallet="pallet_#{p}">
									<input class="d-none" type="radio" t-attf-id="note_pallet_#{p}" name="noteColorPallet" t-attf-value="pallet_#{p}" t-att-checked="p == 1 and 'checked'"/>
									<span class="pallet_detail"></span>
								</label>
							</t>
						</div>
						<div class="note-title border-bottom border-primary">
							<input class="form-control note-title-input border-0" placeholder="Note Title..." aria-label="Take a Note..." aria-describedby="to-do-button"/>
						</div>
						<div class="note-description mt-3">
							<!-- <span class="description-title">Description</span> -->
							<div contenteditable="true" data-text="Note description..." class="form-control note-description-input border-0 border-bottom border-primary rounded-0"></div>
						</div>
						<div class="note-save-update mt-3 d-flex justify-content-end">
							<button class="btn btn-link note-add" type="button" data-update="0" id="note-create">Save</button>
							<button class="btn btn-link note-add note-update d-none" type="button" data-update="1" id="note-update">Update</button>
						</div>
					</div>
				</div>
				<div class="users-to-do-list">
					<t t-if="user.todo_list_ids">
						<t t-foreach="user.todo_list_ids" t-as="note">
							<t t-call="spiffy_theme_backend.to_do_list_content_template"/>
						</t>
					</t>
				</div>
			</div>
        </div>
    </template>
</odoo>
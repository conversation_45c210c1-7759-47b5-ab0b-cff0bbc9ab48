# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_commission
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0-20211110\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-18 00:00+0000\n"
"PO-Revision-Date: 2021-11-18 06:58-0500\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0\n"
"Last-Translator: \n"
"Language: es_PA\n"

#. module: acs_commission
#: model:ir.actions.report,print_report_name:acs_commission.action_acs_commission_sheet_report
msgid "(object.name or 'CommissionSheet').replace('/','_')"
msgstr "(object.name or 'CommissionSheet').replace('/','_')"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Commission Product</span>"
msgstr "<span class=\"o_form_label\">Comisión del Producto</span>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<span>Commission Sheet: </span>"
msgstr "<span>Hoja de Comisión: </span>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>#</strong>"
msgstr "<strong>#</strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Commission Amount</strong>"
msgstr "<strong>Monto de la Comisión</strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Commission On</strong>"
msgstr "<strong>Comisión de</strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Contact: </strong>"
msgstr "<strong>Contacto: </strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Date: </strong>"
msgstr "<strong>Fecha: </strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Date</strong>"
msgstr "<strong>Fecha</strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Invoice</strong>"
msgstr "<strong>Factura</strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Name</strong>"
msgstr "<strong>Nombre</strong>"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_needaction
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_needaction
msgid "Action Needed"
msgstr "Acción Requerida"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_ids
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Excepción de Actividad"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_state
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_state
msgid "Activity State"
msgstr "Estado de la Actividad"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_type_icon
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__print_commission
msgid "Add Commission no in Description"
msgstr "Agregar No de comisión en la descripción"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_attachment_count
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Recuento de Adjuntos"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__account_move__commission_type__automatic
msgid "Automatic"
msgstr "Automático"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_commission_bill
msgid ""
"Bill will be created in draft so that you can review\n"
"                    them before validation."
msgstr ""
"La factura se creará en borrador para que pueda revisar\n"
"                    antes de la validación."

#. module: acs_commission
#: model:ir.actions.act_window,name:acs_commission.acs_commission_action
#: model:ir.model.fields,field_description:acs_commission.field_hms_patient__commission_ids
#: model:ir.model.fields,field_description:acs_commission.field_hms_physician__commission_ids
#: model:ir.model.fields,field_description:acs_commission.field_res_partner__commission_ids
#: model:ir.model.fields,field_description:acs_commission.field_res_users__commission_ids
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_search_view
msgid "Business Commission"
msgstr "Comisión de la empresa"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_list_view
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_search_view
#: model_terms:ir.ui.view,arch_db:acs_commission.view_move_form
#: model_terms:ir.ui.view,arch_db:acs_commission.view_partner_form
msgid "Business Commissions"
msgstr "Comisiones de la empresa"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_form_view
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
#: model_terms:ir.ui.view,arch_db:acs_commission.view_commission_bill
msgid "Cancel"
msgstr "Cancelar"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__payment_status__cancel
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__state__cancel
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__payment_status__cancel
msgid "Canceled"
msgstr "Cancelado"

#. module: acs_commission
#: model_terms:ir.actions.act_window,help:acs_commission.action_acs_commission_role
msgid "Click to add a new Commission Role."
msgstr "Haga clic para agregar un nuevo rol de comisión."

#. module: acs_commission
#: model_terms:ir.actions.act_window,help:acs_commission.acs_commission_action
#: model_terms:ir.actions.act_window,help:acs_commission.action_acs_commission_sheet
msgid "Click to add new Business Commission."
msgstr "Haga clic para agregar una nueva comisión de la empresa"

#. module: acs_commission
#: model:ir.ui.menu,name:acs_commission.menu_acs_invoice_commission
#: model:product.product,name:acs_commission.acs_commission_product
#: model:product.template,name:acs_commission.acs_commission_product_product_template
#: model_terms:ir.ui.view,arch_db:acs_commission.res_config_settings_view_form
msgid "Commission"
msgstr "Comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__commission_amount
msgid "Commission Amount"
msgstr "Monto de la comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_account_bank_statement_line__commission_created
#: model:ir.model.fields,field_description:acs_commission.field_account_move__commission_created
#: model:ir.model.fields,field_description:acs_commission.field_account_payment__commission_created
msgid "Commission Finalized"
msgstr "Comisión finalizada"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_account_bank_statement_line__commission_partner_ids
#: model:ir.model.fields,field_description:acs_commission.field_account_move__commission_partner_ids
#: model:ir.model.fields,field_description:acs_commission.field_account_payment__commission_partner_ids
msgid "Commission For"
msgstr "Comisión para"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_account_bank_statement_line__commission_on
#: model:ir.model.fields,field_description:acs_commission.field_account_move__commission_on
#: model:ir.model.fields,field_description:acs_commission.field_account_payment__commission_on
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__commission_on
msgid "Commission On"
msgstr "Comisión en"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__percentage
#: model:ir.model.fields,field_description:acs_commission.field_hms_patient__commission_percentage
#: model:ir.model.fields,field_description:acs_commission.field_hms_physician__commission_percentage
#: model:ir.model.fields,field_description:acs_commission.field_res_partner__commission_percentage
#: model:ir.model.fields,field_description:acs_commission.field_res_users__commission_percentage
msgid "Commission Percentage"
msgstr "Porcentaje de comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_res_company__commission_product_id
#: model:ir.model.fields,field_description:acs_commission.field_res_config_settings__commission_product_id
msgid "Commission Product"
msgstr "Comisión del producto"

#. module: acs_commission
#: model:ir.actions.act_window,name:acs_commission.action_acs_commission_role
#: model:ir.model,name:acs_commission.model_acs_commission_role
#: model:ir.ui.menu,name:acs_commission.menu_acs_invoice_commission_role
msgid "Commission Role"
msgstr "Función de la Comisión"

#. module: acs_commission
#: model:ir.model,name:acs_commission.model_acs_commission_rule
msgid "Commission Rule"
msgstr "Regla de la Comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__commission_rule_ids
#: model:ir.model.fields,field_description:acs_commission.field_hms_patient__commission_rule_ids
#: model:ir.model.fields,field_description:acs_commission.field_hms_physician__commission_rule_ids
#: model:ir.model.fields,field_description:acs_commission.field_res_partner__commission_rule_ids
#: model:ir.model.fields,field_description:acs_commission.field_res_users__commission_rule_ids
msgid "Commission Rules"
msgstr "Reglamento de la Comisión"

#. module: acs_commission
#: model:ir.actions.act_window,name:acs_commission.action_acs_commission_sheet
#: model:ir.model,name:acs_commission.model_acs_commission_sheet
#: model:ir.ui.menu,name:acs_commission.menu_acs_commission_sheet
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_list_view
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_search_view
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
msgid "Commission Sheet"
msgstr "Hoja de comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_account_bank_statement_line__commission_type
#: model:ir.model.fields,field_description:acs_commission.field_account_move__commission_type
#: model:ir.model.fields,field_description:acs_commission.field_account_payment__commission_type
msgid "Commission Type"
msgstr "Tipo de comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_res_company__commission_on_invoice_amount
#: model:ir.model.fields,field_description:acs_commission.field_res_config_settings__commission_on_invoice_amount
msgid "Commission on Invoice Amount"
msgstr "Comisión sobre el importe de la factura"

#. module: acs_commission
#: model:ir.ui.menu,name:acs_commission.menu_invoice_acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_partner_form
msgid "Commissions"
msgstr "Comisiones"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__company_id
msgid "Company"
msgstr "Compañía"

#. module: acs_commission
#: model:ir.model,name:acs_commission.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_move_form
msgid "Confirm Commissions"
msgstr "Confirmar comisiones"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__state__confirmed
msgid "Confirmed"
msgstr "Confirmado"

#. module: acs_commission
#: model:ir.model,name:acs_commission.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: acs_commission
#: model:ir.actions.act_window,name:acs_commission.action_view_commission_bill
#: model:ir.model,name:acs_commission.model_commission_bill
#: model_terms:ir.ui.view,arch_db:acs_commission.view_commission_bill
msgid "Create Commission Bill"
msgstr "Crear factura de comisión "

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_commission_bill
msgid "Create Invoices"
msgstr "Crear facturas"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
msgid "Create Payment Bill"
msgstr "Crear Pago de Factura"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_commission_bill
msgid "Create and View Invoices"
msgstr "Crear y ver facturas"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.res_config_settings_view_form
msgid "Create commission based on invoice amount by default."
msgstr ""
"Cree una comisión basada en el importe de la factura de forma "
"predeterminada."

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__create_uid
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__create_uid
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__create_uid
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__create_uid
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__create_date
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__create_date
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__create_date
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__create_date
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__create_date
msgid "Created on"
msgstr "Creado el"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_search_view
msgid "Customer"
msgstr "Cliente"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__date_from
msgid "Date From"
msgstr "Fecha de Inicio"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__date_to
msgid "Date To"
msgstr "Fecha final"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.res_config_settings_view_form
msgid "Default product to manage commission"
msgstr "Producto predeterminado para gestionar la comisión"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__description
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__description
msgid "Description"
msgstr "Descripción"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__display_name
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__display_name
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__display_name
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__display_name
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__display_name
msgid "Display Name"
msgstr "Nombre"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__state__done
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_form_view
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
msgid "Done"
msgstr "Listo"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__state__draft
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__state__draft
msgid "Draft"
msgstr "Borrador"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__payment_status__draft
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__payment_status__draft
msgid "Draft Payment"
msgstr "Pago en Borrador"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_follower_ids
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_partner_ids
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Socios)"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__activity_type_icon
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome e.g. fa-tasks"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_hms_patient__provide_commission
#: model:ir.model.fields,field_description:acs_commission.field_hms_physician__provide_commission
#: model:ir.model.fields,field_description:acs_commission.field_res_partner__provide_commission
#: model:ir.model.fields,field_description:acs_commission.field_res_users__provide_commission
msgid "Give Commission"
msgstr "Dar Comisión"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_search_view
msgid "Group By"
msgstr "Agrupar por"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__groupby_partner
msgid "Group by Partner"
msgstr "Agrupar por socio"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__has_message
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__has_message
msgid "Has Message"
msgstr "Tiene mensaje"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__hide_groupby_partner
msgid "Hide Group by Partner"
msgstr "Ocultar grupo por socio"

#. module: acs_commission
#: model:ir.model,name:acs_commission.model_res_company
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__company_id
msgid "Hospital"
msgstr "Hospital"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__id
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__id
msgid "ID"
msgstr "Cédula o Pasaporte"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_exception_icon
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__activity_exception_icon
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_needaction
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_unread
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_needaction
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado hay nuevos mensajes que requieren su atención."

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_has_error
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_has_sms_error
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_has_error
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra marcado, algunos mensajes tienen error de envío."

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__invoice_id
msgid "Invoice"
msgstr "Factura"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
msgid "Invoice Lines"
msgstr "Líneas de factura"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_is_follower
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__journal_id
msgid "Journal"
msgstr "Diario"

#. module: acs_commission
#: model:ir.model,name:acs_commission.model_account_move
msgid "Journal Entry"
msgstr "Entrada al diario"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_sheet_report_document
msgid ""
"Keep this print as reference further communication and procedure.\n"
"                    Should you have any questions please contact us at your "
"convenience.<br/><br/>\n"
"                    Best Regards<br/>"
msgstr ""
"Conserve esta impresión como referencia para futuras comunicaciones y "
"procedimientos.\n"
"                    Si tiene alguna pregunta, comuníquese con nosotros.<br/"
"><br/>\n"
"                    Saludos cordiales<br/>"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission____last_update
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role____last_update
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule____last_update
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet____last_update
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__write_uid
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__write_uid
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__write_uid
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__write_uid
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__write_date
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__write_date
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__write_date
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__write_date
#: model:ir.model.fields,field_description:acs_commission.field_commission_bill__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__commission_line_ids
msgid "Lines"
msgstr "Líneas"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: acs_commission
#: model:res.groups,name:acs_commission.group_acs_commission_user
msgid "Manage Commissions"
msgstr "Gestionar comisiones"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_has_error
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_ids
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__my_activity_date_deadline
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__name
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_role__name
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__name
msgid "Name"
msgstr "Nombre"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_date_deadline
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_summary
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_type_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: acs_commission
#: model_terms:ir.actions.act_window,help:acs_commission.acs_commission_action
#: model_terms:ir.actions.act_window,help:acs_commission.action_acs_commission_sheet
msgid "No Record Found"
msgstr "Registro no encontrado"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_search_view
msgid "Not Invoiced"
msgstr "No facturado"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__payment_status__not_inv
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__payment_status__not_inv
msgid "Not Paid"
msgstr "No pagado"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__note
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__note
msgid "Note"
msgstr "Nota"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_needaction_counter
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Acciones"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_has_error_counter
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de Errores"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_needaction_counter
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_has_error_counter
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__message_unread_counter
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes no leidos"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__account_move__commission_type__fix_amount
msgid "On Fix Amount"
msgstr "Sobre la cantidad fija"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__payment_status__open
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__payment_status__open
msgid "Open"
msgstr "Abrir"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission__payment_status__paid
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__payment_status__paid
msgid "Paid"
msgstr "Pagado"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__partner_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__partner_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__partner_id
msgid "Partner"
msgstr "Cliente"

#. module: acs_commission
#: model:ir.model,name:acs_commission.model_acs_commission
msgid "Partner Commission"
msgstr "Comisión del cliente"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__payment_invoice_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__payment_invoice_id
msgid "Payment Invoice"
msgstr "Factura de pago"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__invoice_line_id
msgid "Payment Invoice Line"
msgstr "Línea de factura de pago"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__payment_status
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__payment_status
msgid "Payment Status"
msgstr "Estado del pago"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__physician_id
msgid "Physician"
msgstr "Profesional"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__product_id
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_rule__rule_on__product
msgid "Product"
msgstr "Producto"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__product_category_id
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_rule__rule_on__product_category
msgid "Product Category"
msgstr "Categoría de producto"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
msgid "Refresh Data"
msgstr "Actualizar datos"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_acs_commission_sheet
msgid "Reset to Draft"
msgstr "Restablecer a Borrador"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__activity_user_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__activity_user_id
msgid "Responsible User"
msgstr "Usuario Responsable"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__role_id
#: model:ir.model.fields,field_description:acs_commission.field_hms_patient__commission_role_id
#: model:ir.model.fields,field_description:acs_commission.field_hms_physician__commission_role_id
#: model:ir.model.fields,field_description:acs_commission.field_res_partner__commission_role_id
#: model:ir.model.fields,field_description:acs_commission.field_res_users__commission_role_id
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_rule__rule_type__role
msgid "Role"
msgstr "Rol"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__rule_on
msgid "Rule On"
msgstr "Regla sobre"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__rule_type
msgid "Rule Type"
msgstr "Tipo de regla"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_has_sms_error
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_account_bank_statement_line__commission_ids
#: model:ir.model.fields,field_description:acs_commission.field_account_move__commission_ids
#: model:ir.model.fields,field_description:acs_commission.field_account_payment__commission_ids
msgid "Sales Commission"
msgstr "Comisión de Ventas"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.res_config_settings_view_form
msgid "Set Invoice amount as default Commision Amount"
msgstr ""
"Establecer el importe de la factura como importe predeterminado de la "
"comisión"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_form_view
msgid "Set to Draft"
msgstr "Establecer en borrador"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_commission_bill__print_commission
msgid "Set true if want to append SO in bill line Description"
msgstr ""
"Establezca verdadero si desea agregar SO en la descripción de factura."

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_commission_bill__groupby_partner
msgid "Set true if want to create single bill for Partner"
msgstr ""
"Establecer como verdadero si desea crear una sola factura para el Cliente."

#. module: acs_commission
#: model:ir.actions.report,name:acs_commission.action_acs_commission_sheet_report
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__commission_sheet_id
msgid "Sheet"
msgstr "Hoja"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_search_view
msgid "State"
msgstr "Estado"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__state
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__state
msgid "Status"
msgstr "Estado"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__activity_state
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__payment_invoice_id
msgid "The move of this entry line."
msgstr "El movimiento de esta línea de entrada."

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__amount_total
msgid "Total"
msgstr "Total"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__activity_exception_decoration
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: acs_commission
#: model:product.product,uom_name:acs_commission.acs_commission_product
#: model:product.template,uom_name:acs_commission.acs_commission_product_product_template
msgid "Units"
msgstr "Unidades"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_unread
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_unread
msgid "Unread Messages"
msgstr "Mensajes no leídos"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__message_unread_counter
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de Mensajes no leídos"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.view_move_form
msgid "Update Commission"
msgstr "Comisión Actualizada"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_rule__user_id
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__user_id
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_rule__rule_type__user
msgid "User"
msgstr "Usuario"

#. module: acs_commission
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission__website_message_ids
#: model:ir.model.fields,field_description:acs_commission.field_acs_commission_sheet__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del Sitio Web"

#. module: acs_commission
#: model:ir.model.fields,help:acs_commission.field_acs_commission__website_message_ids
#: model:ir.model.fields,help:acs_commission.field_acs_commission_sheet__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: acs_commission
#: model:ir.model.fields.selection,name:acs_commission.selection__acs_commission_sheet__state__done
msgid "done"
msgstr "Listo"

#. module: acs_commission
#: model_terms:ir.ui.view,arch_db:acs_commission.acs_commission_form_view
msgid "e.g. Business Commission"
msgstr "e.g. Comisión de la Empresa"

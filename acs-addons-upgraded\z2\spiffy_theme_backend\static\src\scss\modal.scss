// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
body.o_web_client {
    &.modal-open{
        .modal{
            overflow-y: hidden;
        }
    }
    .modal-dialog {
        max-width: unset !important;

        &.modal-lg, &.modal-xl{
            .modal-content {
                @media (min-width: 576px) {
                    width: 650px;
                }
                @media (min-width: 992px) {
                    width: 980px;
                }
            }
        }
        &.modal-sm{
            .modal-content {
                @media (min-width: 576px) {
                    width: 300px;
                }
            }
        }
        @media (min-width: 576px) {
            .modal-content {
                width: 650px;
                margin: auto;
            }
        }
        .close {
            color: var(--biz-theme-body-text-color) !important;
        }
        .modal-content {
            padding: 15px;
            padding-top: 0;
            border-radius: var(--border-radius-lg);
            background-color: var(--biz-theme-body-color) !important;
            color: var(--biz-theme-body-text-color) !important;

            .modal-header {
                padding: 15px 0 !important;
            }

            .modal-body.o_act_window{
                .o_form_view{
                    background-color: var(--biz-theme-body-color) !important;
                    .o_form_sheet_bg{
                        background-color: var(--biz-theme-body-color)!important;
                        .o_form_sheet{
                            background-color: var(--biz-theme-body-color)!important;
                        }
                    }
                    .o_form_uri {
                        > span:first-child {
                            color: var(--biz-theme-primary-color);
                        }
                        color: var(--biz-theme-primary-color);
                    }
                }
            }
        }
        @include media-breakpoint-down(sm) {
            .modal-content {
                .modal-header {
                    .modal-title {
                        color: inherit !important;
                    }
                    background: transparent !important;
                }
            }
        }
    }
    .input_border {
        border-color: var(--biz-theme-primary-color);
    }
    .dynamic_data {
        .border-style {
            border-bottom: 1px solid #e2e2e2;
        }
        position: fixed;
        z-index: 1041;
        background-color: var(--biz-theme-body-color) !important;
        color: var(--biz-theme-body-text-color) !important;
        box-shadow: var(--box-shadow-common);
        right: -100%;
        top: 0;
        height: 100%;
        transition: 0.6s;
        @include media-breakpoint-up(lg){
            width: 600px;
            border-top-left-radius: var(--border-radius-lg);
            border-bottom-left-radius: var(--border-radius-lg);
        }
        @include media-breakpoint-down(md){
            width: 100%;
        }
    
        .theme-config-sidebar{
            > .backend_configurator_close {
                position: absolute;
                top: 10px;
                right: 8px;
                z-index: 1042;
    
                &:focus {
                    outline: 0 !important;
                }
                .img {
                    max-width: 16px;
                }
            }
            .config-sidebar-tab-panel{
                max-height: 100vh;
                overflow-y: auto;
                li{
                    width: 100%;
                }
                h3 {
                    border-bottom: 1px solid #e2e2e2;
                    font-weight: 600;
                }
                .nav-item {
                    .nav-link {
                        font-weight: 600;
                        color: inherit;
                        margin: 0 0.5rem !important;
                        display: block !important;
                        border-radius: var(--border-radius-lg);
    
                        &.active{
                            background-color: var(--biz-theme-primary-color);
                            color: var(--biz-theme-primary-text-color);
                        }
                    }
                }
            }
            .config-sidebar-tab-content{
                max-height: 100vh;
                // overflow-y: auto;
                border-left: 1px solid #e2e2e2;
    
                h3 {
                    border-bottom: 1px solid #e2e2e2;
                }
    
                .menu-position-row{
                    .menu-position{
                        img{
                            max-height: 50px;
                            min-height: 50px;
                            width: 115px;
                        }
                    }
                    .form-check .top_menu_style{
                        img{
                            max-height: 50px;
                            min-height: 50px;
                            width: 115px;
                        }
                    }
                    .vertical-mini-menu-bg-image-content{
                        max-height: 50px;
                        min-height: 50px;
                    }
                }
                .theme-style-row, .separator-styles-row{
                    img{
                        max-height: 40px;
                        min-height: 40px;
                    }
                }
    
                .custom-control{
                    .custom-control-label{
                        flex: 1 1 auto;
                    }
                }
                .nav-item {
                    .nav-link {
                        color: inherit;
                    }
                    .nav-link.active {
                        background-color: var(--biz-theme-primary-color);
                        color: var(--biz-theme-primary-text-color);
                    }
                }
    
                .tab-pane{
                    height: 100%;
                    max-height: calc(100% - 60px);
                    .custom_color_config{
                        .background-color{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border: 1px solid;
                            border-color: #e2e2e2;
                            border-radius: var(--border-radius-lg);
                            height: 50px;
                            width: 150px;
                            .img{
                                width: 30px;
                                
                            }
                            #menu_shape_bg, #primary_bg, #primary_text, #custom_drawer_bg, #custom_drawer_text{
                                margin-left: 12px;
                            }
                        }
                    }
                    h3{
                        font-weight: 600;
                    }
                    h5{
                        font-weight: 600;
                        font-size: 15px;
                    }
                    .container{
                        height: 100%;
                        max-height: calc(100% - 50px);
                        overflow: auto;
    
                        .custom-radio,.form_check_content{
                            border: 1px solid;
                            border-color: #e2e2e2;
                            border-radius: var(--border-radius-lg);
                        }
                        .form_check_content{
                            padding: 10px;
                        }
                    }
                }
                .discard_button_style{
                    border-top: 1px solid #e2e2e2;
                    background-color: var(--biz-theme-body-color);
                }
    
                #general_settings, #font_family, #font_size {
                    input[type="radio"]{
                        .custom-control-label::before {
                            top: 3px !important;
                        }
    
                        .custom-control-label::after {
                            position: absolute;
                            top: 3px !important;
                        }
                    }
                    input[type="checkbox"]{
                        .custom-control-label::after {
                            position: absolute;
                            top: 6px !important;
                        }
                    }
                }
                #general_settings{
                    #menu_shape_bg_color_opacity{
                        width: 50%;
                        height: 10px;
                        border-radius: 5px;
                        background: #d3d3d3;
                        outline: none;
                        transition: opacity .2s;
                    }
                }
            }
        }
    
        .text-style-design {
            margin-left: 10px;
        }
    
        &.visible{
            right: 0 !important;
        }
    }


    &.dark_mode{
        .btn-close{
            filter: invert(1);
        }
    }
}

.tab-pane {
    .custom-control-inline {
        .custom-control-input {
            position: unset !important;
        }
    }

    .color_picker {
        padding: 0 !important;
        background-color: transparent !important;
        border: none !important;
        cursor: pointer;
    }
}

.box-shadow {
    padding: 10px;
}

body.o_web_client {
    &::after {
        backdrop-filter: blur(0px);
        content: "";
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        transition: 0.5s;
        z-index: -10;
    }
}

body.o_web_client.backdrop {
    &::after {
        backdrop-filter: blur(3px);
        background-color: rgba(0, 0, 0, .10);
        z-index: 100;
        @include media-breakpoint-down(md){
            z-index: 97;
        }
    }
}

.color_picker {
    span {
        font-size: 21px;
    }
    .fa-circle:before {
        border: 1px solid #1b1b1b;
        border-radius: 100%;
        line-height: 24px;
        padding-left: 1.6px;
        padding-right: 1.6px;
    }
}

// Scorll Bar Design
body.o_web_client {
    ::-webkit-scrollbar-track {
        border-radius: 5px;
    }
    ::-webkit-scrollbar {
        height: 5px;
        width: 5px;
        border-radius: 5px;
    }
    ::-webkit-scrollbar-thumb {
        background-color: var(--biz-theme-primary-color);
        border-radius: 5px;
    }
}

body.o_web_client {
    .o_content .o_list_view .table-responsive .o_list_table {
        border: 0 !important;
    }
}
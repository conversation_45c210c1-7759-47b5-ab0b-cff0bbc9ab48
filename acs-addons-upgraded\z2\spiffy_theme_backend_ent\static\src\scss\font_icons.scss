
// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
@mixin fa-font-icons-content($name, $content) {
    .fa.fa-#{$name}{
        font-family: 'remixicon' !important;
        &::before{
            content: $content !important;
        }
    }
}

@include fa-font-icons-content(search, "\f0d1");
@include fa-font-icons-content(folder, "\ed27");
@include fa-font-icons-content(filter, "\ed23");
@include fa-font-icons-content(star, "\f18b");
@include fa-font-icons-content(bars, "\ef3e");
@include fa-font-icons-content(chevron-left, "\ea60");
@include fa-font-icons-content(chevron-right, "\ea6c");
@include fa-font-icons-content(download, "\ec54");
@include fa-font-icons-content(plus, "\ea13");
@include fa-font-icons-content(pencil, "\efe0");
@include fa-font-icons-content(check, "\eb7b");
@include fa-font-icons-content(times, "\eb99");
@include fa-font-icons-content(calendar-check-o, "\eb23");
@include fa-font-icons-content(cogs, "\f0e6");
@include fa-font-icons-content(cog, "\f0e6");
@include fa-font-icons-content(clock-o, "\f215");
@include fa-font-icons-content(caret-right, "\ea6c");
@include fa-font-icons-content(caret-down, "\ea4e");
@include fa-font-icons-content(envelope, "\eef6");
@include fa-font-icons-content(tasks, "\eb27");
@include fa-font-icons-content(print, "\f029");
@include fa-font-icons-content(paperclip, "\ea86");
@include fa-font-icons-content(user, "\f264");
@include fa-font-icons-content(star-o, "\f18b");
@include fa-font-icons-content(bug, "\eb07");
@include fa-font-icons-content(inbox, "\ee4f");

body.o_web_client{
    .dropdown-item.selected:before {
        font-family: 'remixicon' !important;
        content: "\eb7b" !important;
    }
    .ri{
        font-size: 18px;
        vertical-align: middle;
        line-height: 20px
    }
    .fa{
        vertical-align: middle;
    }
    .fa-folder{
        color: var(--biz-theme-primary-color) !important;
    }
    
    .o_menu_systray {
        .fa{
            font-size:18px;
            vertical-align: middle;
            &.fa-circle{
                font-size: 14px
            }
        }
        @include fa-font-icons-content(globe, "\ed8a");
        @include fa-font-icons-content(institution, "\eb03");
        // @include fa-font-icons-content(pencil, "\ef3f");
        @include fa-font-icons-content(comments, "\eb51");
    }
    
    .header_menu_right_content {
        .fullscreen-exit {
            .ri-fullscreen-line:before {
                content: "\ed9a" !important;
            }
        }
    }
}

.o_rtl {
    .fa.fa-chevron-right, .fa.fa-chevron-left {
        transform: unset !important;
    }
}
DELL@DESKTOP-C9C0T1T MINGW64 ~/vetlane/vetlane/acs-addons-upgraded (main)
$ ls -R
.:
z1/  z2/  z3/  z4/

./z1:

./z2:
spiffy_theme_backend/  spiffy_theme_backend_ent/

./z2/spiffy_theme_backend:
COPYRIGHT  __init__.py      data/             security/
LICENSE    __manifest__.py  models/           static/
README.md  controllers/     requirements.txt  views/

./z2/spiffy_theme_backend/controllers:
__init__.py  global_search_main.py  main.py  pwa.py

./z2/spiffy_theme_backend/data:
backend_config_data.xml  spiffy_default_images.xml
global_level_config.xml

./z2/spiffy_theme_backend/models:
__init__.py              mail_firebase.py
backend_configurator.py  multi_tab.py
bookmark.py              push_notification_menu.py
favorite_apps.py         pwa_shortcuts.py
global_search.py         res_company.py
ir_http.py               res_config_setting.py
ir_menu.py               res_users.py
ir_module.py             spiffy_app_group.py
mail_channel.py          to_do_list.py

./z2/spiffy_theme_backend/security:
ir.model.access.csv

./z2/spiffy_theme_backend/static:
description/  lib/  src/

./z2/spiffy_theme_backend/static/description:
Bizople-logo-black.png        spiffy-enterprise.png
Spiffy-full-white.png         spiffy-mobile-app-icon.png
app-drawer-bg-image.png       spiffy_cover.png
bizople-icon.png              spiffy_screenshot.gif
bizople-white-logo-icon.png   spiffy_screenshot_sale.gif
bizople-white-logo.png        support_banner.jpg
firebase-key/                 top-menu-horizontal.gif
global-search-banner.gif      top-menu-v2-bg1.jpg
header_vertical_mini.svg      top-menu-v2-bg2.jpg
icon.png                      top-menu-v2-bg3.jpg
index.html                    top-menu-v2-bg4.jpg
new-index/                    top-menu-vertical-mini.gif
odoo_ready_partner_logo.png   top-menu-vertical.gif
service-banner.jpg            vertical-pinned-menu-logo.png
spiffy-dashboard-buy-now.jpg  vertical-unpinned-menu-logo.png

./z2/spiffy_theme_backend/static/description/firebase-key:
spiffy-99038-firebase-adminsdk-tmssr-26241eaaa2.json

./z2/spiffy_theme_backend/static/description/new-index:
3d-icon-pack.jpg                input-styles.gif
Font-Size-options.jpg           lang-company-debug.jpg
Font-Styles.jpg                 list-view-density.gif
Loadin-Icons.jpg                menu-style.gif
Login-page-Style.gif            modal-draggable.gif
Popup-Animations.jpg            multi-tab.gif
Radio-Styles.jpg                prevent-auto-save.jpg
Separator-Styles.jpg            progressive-web-app.jpg
Tab-Styles.jpg                  quick-action-buttons.jpg
app-banner.jpg                  refresh-btn.gif
app-drawer-bg.gif               rtl-support.jpg
app-drawer.jpg                  sidebar-pin-unpin.gif
app-icon-bg-shape.gif           spiffy-info-banner.jpg
app-icon-config.gif             split-view.gif
bookmark.gif                    sticky-header.gif
branding.gif                    theme-color.gif
chatter-attachment-in-list.gif  theme-style.jpg
checkbox-styles.jpg             todo-notes.gif
custom-app.jpg                  twitter-banner.jpg
dark-light-mode.gif             user-access.jpg
dark-light-mode.jpg             vertical-menu-with-bg-image.gif
development_note.jpg            vertical-mini-header.gif
fav-apps.gif                    websie-login-addon.jpg
fully-responsive.jpg            zoom-fullscreen.gif
index-main-banner-ce.jpg

./z2/spiffy_theme_backend/static/lib:
Almarai/                             Manrope/     Raleway/
AlumniSansCollegiateOne-Regular.ttf  Montserrat/  RemixIcon/
Cairo/                               NunitoSans/  Roboto/
Inter/                               OpenSans/    Rubik/
JosefinSans/                         Oswald/      Tajawal/
Lato/                                Poppins/     VarelaRound/

./z2/spiffy_theme_backend/static/lib/Almarai:
Almarai-Bold.ttf       Almarai-Light.ttf    OFL.txt
Almarai-ExtraBold.ttf  Almarai-Regular.ttf

./z2/spiffy_theme_backend/static/lib/Cairo:
Cairo-Black.ttf      Cairo-ExtraLight.ttf  Cairo-Regular.ttf
Cairo-Bold.ttf       Cairo-Light.ttf       Cairo-SemiBold.ttf
Cairo-ExtraBold.ttf  Cairo-Medium.ttf      README.txt

./z2/spiffy_theme_backend/static/lib/Inter:
Inter-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend/static/lib/JosefinSans:
JosefinSans-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend/static/lib/Lato:
Lato-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend/static/lib/Manrope:
Manrope-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend/static/lib/Montserrat:
Montserrat-Regular.ttf

./z2/spiffy_theme_backend/static/lib/NunitoSans:
NunitoSans-Black.ttf             NunitoSans-Italic.ttf
NunitoSans-BlackItalic.ttf       NunitoSans-Light.ttf
NunitoSans-Bold.ttf              NunitoSans-LightItalic.ttf
NunitoSans-BoldItalic.ttf        NunitoSans-Regular.ttf
NunitoSans-ExtraBold.ttf         NunitoSans-SemiBold.ttf
NunitoSans-ExtraBoldItalic.ttf   NunitoSans-SemiBoldItalic.ttf
NunitoSans-ExtraLight.ttf        OFL.txt
NunitoSans-ExtraLightItalic.ttf

./z2/spiffy_theme_backend/static/lib/OpenSans:
OpenSans-Regular.ttf

./z2/spiffy_theme_backend/static/lib/Oswald:
Oswald-Regular.ttf

./z2/spiffy_theme_backend/static/lib/Poppins:
OFL.txt  Poppins-Regular.ttf

./z2/spiffy_theme_backend/static/lib/Raleway:
Raleway-Black.ttf             Raleway-Light.ttf
Raleway-BlackItalic.ttf       Raleway-LightItalic.ttf
Raleway-Bold.ttf              Raleway-Medium.ttf
Raleway-BoldItalic.ttf        Raleway-MediumItalic.ttf
Raleway-ExtraBold.ttf         Raleway-Regular.ttf
Raleway-ExtraBoldItalic.ttf   Raleway-SemiBold.ttf
Raleway-ExtraLight.ttf        Raleway-SemiBoldItalic.ttf
Raleway-ExtraLightItalic.ttf  Raleway-Thin.ttf
Raleway-Italic.ttf            Raleway-ThinItalic.ttf

./z2/spiffy_theme_backend/static/lib/RemixIcon:
remixicon.css         remixicon.less        remixicon.ttf
remixicon.eot         remixicon.svg         remixicon.woff
remixicon.glyph.json  remixicon.symbol.svg  remixicon.woff2

./z2/spiffy_theme_backend/static/lib/Roboto:
LICENSE.txt             Roboto-LightItalic.ttf
Roboto-Black.ttf        Roboto-Medium.ttf
Roboto-BlackItalic.ttf  Roboto-MediumItalic.ttf
Roboto-Bold.ttf         Roboto-Regular.ttf
Roboto-BoldItalic.ttf   Roboto-Thin.ttf
Roboto-Italic.ttf       Roboto-ThinItalic.ttf
Roboto-Light.ttf

./z2/spiffy_theme_backend/static/lib/Rubik:
OFL.txt  Rubik-Regular.ttf

./z2/spiffy_theme_backend/static/lib/Tajawal:
OFL.txt            Tajawal-ExtraBold.ttf   Tajawal-Medium.ttf
Tajawal-Black.ttf  Tajawal-ExtraLight.ttf  Tajawal-Regular.ttf
Tajawal-Bold.ttf   Tajawal-Light.ttf

./z2/spiffy_theme_backend/static/lib/VarelaRound:
OFL.txt  VarelaRound-Regular.ttf

./z2/spiffy_theme_backend/static/src:
image/  js/  scss/  xml/

./z2/spiffy_theme_backend/static/src/image:
 backend_theme_icon.png          pallet_3.png
 background-color.svg            pallet_4.png
 checked.png                     pallet_5.png
 circle.png                      pallet_6.png
 close.png                       pallet_7.png
'download (1).png'               pallet_8.png
 download.svg                    pallet_9.png
 iconoff.png                     pallets.jpg
 iconon.png                      shape-style-rounded.png
 icons8-light-50.png             shape-style-square.png
 icons8-light-on-50.png          shape-style-standard.png
 input-style-bordered.png        theme-chatter-bottom.png
 input-style-borderless.png      theme-chatter-right.png
 input-style-bottom-border.png   theme-menu-vertical-center.png
 list_comfortable.png            theme-menu-vertical-left.png
 list_compact.png                theme-menu-vertical_mini.png
 loader_1.gif                    theme-menu_horizontal.png
 loader_1.svg                    theme-separator-style1.png
 loader_10.gif                   theme-separator-style2.png
 loader_2.gif                    theme-separator-style3.png
 loader_3.gif                    theme-separator-style4.png
 loader_4.gif                    theme-style-rounded.png
 loader_5.gif                    theme-style-square.png
 loader_6.gif                    theme-style-standard.png
 loader_7.gif                    top-menu-v2-bg-four.png
 loader_8.gif                    top-menu-v2-bg-one.png
 loader_9.gif                    top-menu-v2-bg-three.png
 pallet_1.png                    top-menu-v2-bg-two.png
 pallet_10.png                   top-menu-v2-bg1.jpg
 pallet_11.png                   top-menu-v2-bg2.jpg
 pallet_12.png                   top-menu-v2-bg3.jpg
 pallet_13.png                   top-menu-v2-bg4.jpg
 pallet_14.png                   top-menu-v2-bg5.jpg
 pallet_15.png                   ui-icons_444444_256x240.png
 pallet_16.png                   ui-icons_555555_256x240.png
 pallet_17.png                   ui-icons_777620_256x240.png
 pallet_18.png                   ui-icons_777777_256x240.png
 pallet_19.png                   ui-icons_cc0000_256x240.png
 pallet_2.png                    ui-icons_ffffff_256x240.png

./z2/spiffy_theme_backend/static/src/js:
SpiffyPageTitle.js    form_controller.js     pager.js
SwitchCompanyMenu.js  form_view_renderer.js  pwebapp.js
action_service.js     iconpack_load.js       service_worker.js
apps_menu.js          jquery-ui/             split_view/
color_pallet.js       list_view_renderer.js  user_menu.js
dialog.js             menu.js                widgets/
flip_min.js           menu_service.js

./z2/spiffy_theme_backend/static/src/js/jquery-ui:
jquery-ui.min.css  jquery-ui.min.js

./z2/spiffy_theme_backend/static/src/js/split_view:
split_view_components.js  split_view_form.js
split_view_container.js   split_view_form.xml
split_view_controller.js

./z2/spiffy_theme_backend/static/src/js/widgets:
spiffyDocumentViewer.js    spiffyDocumentViewer.xml
spiffyDocumentViewer.scss

./z2/spiffy_theme_backend/static/src/scss:
activity_view.scss     loginpage.scss
appdrawer.scss         menu_shape_styles.scss
bookmarks.scss         modal.scss
burger_menu.scss       multi_tab.scss
calendear_view.scss    notification.scss
chat_window.scss       pivot_view.scss
checkbox_styles.scss   popup_styles.scss
common_view.scss       radio_styles.scss
controlpannel.scss     responsive.scss
custom_varibles.scss   search_modal.scss
dashboards.scss        search_panel.scss
datetime_pickers.scss  separator_styles.scss
discuss_style.scss     setting_page.scss
font-family.scss       side_menu.scss
font_icons.scss        tab_styles.scss
font_sizes/            to_do_list.scss
form_chatter.scss      top_menu_horizontal.scss
form_view.scss         top_menu_vertical.scss
graph_view.scss        top_menu_vertical_mini.scss
kanban_view.scss       tree_form_split_view.scss
list_view.scss         website_menu.scss
loader.scss

./z2/spiffy_theme_backend/static/src/scss/font_sizes:
font_large.css  font_medium.css  font_small.css

./z2/spiffy_theme_backend/static/src/xml:
base.xml                        list_renderer.xml
bookmark.xml                    menu.xml
fileviewer.xml                  spiffy_app_menu_group.xml
form_statusbar.xml              view_button_icons.xml
inherit_mobile_apps_funnel.xml  web_inherit.xml

./z2/spiffy_theme_backend/views:
backend_configurator_template.xml  pwa_offline.xml
backend_configurator_view.xml      pwa_shortcuts_view.xml
global_search_view.xml             res_config_setting.xml
ir_module_view.xml                 res_users_view.xml
login_page_style.xml               spiffy_app_group_view.xml
manifest.xml                       templates_inherit.xml
menuitems.xml                      to_do_list_template.xml
push_notification_menu_view.xml

./z2/spiffy_theme_backend_ent:
CHANGELOG.md      README.md        data/             static/
COPYRIGHT         __init__.py      models/           views/
DOCUMENTATION.md  __manifest__.py  requirements.txt
LICENSE           controllers/     security/

./z2/spiffy_theme_backend_ent/controllers:
__init__.py  global_search_main.py  main.py  pwa.py

./z2/spiffy_theme_backend_ent/data:
backend_config_data.xml  spiffy_default_images.xml
global_level_config.xml

./z2/spiffy_theme_backend_ent/models:
__init__.py              multi_tab.py
backend_configurator.py  push_notification_menu.py
bookmark.py              pwa_shortcuts.py
favorite_apps.py         res_company.py
global_search.py         res_config_setting.py
ir_http.py               res_users.py
ir_menu.py               spiffy_app_group.py
ir_module.py             studio_config.py
mail_channel.py          to_do_list.py
mail_firebase.py

./z2/spiffy_theme_backend_ent/security:
ir.model.access.csv

./z2/spiffy_theme_backend_ent/static:
description/  lib/  src/

./z2/spiffy_theme_backend_ent/static/description:
Bizople-logo-black.png        spiffy-enterprise.png
Spiffy-full-white.png         spiffy-mobile-app-icon.png
app-drawer-bg-image.png       spiffy_cover.png
bizople-icon.png              spiffy_screenshot.gif
bizople-white-logo-icon.png   spiffy_screenshot_sale.gif
bizople-white-logo.png        support_banner.jpg
firebase-key/                 top-menu-horizontal.gif
global-search-banner.gif      top-menu-v2-bg1.jpg
header_vertical_mini.svg      top-menu-v2-bg2.jpg
icon.png                      top-menu-v2-bg3.jpg
index.html                    top-menu-v2-bg4.jpg
new-index/                    top-menu-vertical-mini.gif
odoo_ready_partner_logo.png   top-menu-vertical.gif
service-banner.jpg            vertical-pinned-menu-logo.png
spiffy-dashboard-buy-now.jpg  vertical-unpinned-menu-logo.png

./z2/spiffy_theme_backend_ent/static/description/firebase-key:
spiffy-99038-firebase-adminsdk-tmssr-26241eaaa2.json

./z2/spiffy_theme_backend_ent/static/description/new-index:
3d-icon-pack.jpg                input-styles.gif
Font-Size-options.jpg           lang-company-debug.jpg
Font-Styles.jpg                 list-view-density.gif
Loadin-Icons.jpg                menu-style.gif
Login-page-Style.gif            modal-draggable.gif
Popup-Animations.jpg            multi-tab.gif
Radio-Styles.jpg                prevent-auto-save.jpg
Separator-Styles.jpg            progressive-web-app.jpg
Tab-Styles.jpg                  quick-action-buttons.jpg
app-banner.jpg                  refresh-btn.gif
app-drawer-bg.gif               rtl-support.jpg
app-drawer.jpg                  sidebar-pin-unpin.gif
app-icon-bg-shape.gif           spiffy-info-banner.jpg
app-icon-config.gif             split-view.gif
bookmark.gif                    sticky-header.gif
branding.gif                    theme-color.gif
chatter-attachment-in-list.gif  theme-style.jpg
checkbox-styles.jpg             todo-notes.gif
custom-app.jpg                  twitter-banner.jpg
dark-light-mode.gif             user-access.jpg
dark-light-mode.jpg             vertical-menu-with-bg-image.gif
development_note.jpg            vertical-mini-header.gif
fav-apps.gif                    websie-login-addon.jpg
fully-responsive.jpg            zoom-fullscreen.gif
index-main-banner-ce.jpg

./z2/spiffy_theme_backend_ent/static/lib:
Almarai/                             Manrope/     Raleway/
AlumniSansCollegiateOne-Regular.ttf  Montserrat/  RemixIcon/
Cairo/                               NunitoSans/  Roboto/
Inter/                               OpenSans/    Rubik/
JosefinSans/                         Oswald/      Tajawal/
Lato/                                Poppins/     VarelaRound/

./z2/spiffy_theme_backend_ent/static/lib/Almarai:
Almarai-Bold.ttf       Almarai-Light.ttf    OFL.txt
Almarai-ExtraBold.ttf  Almarai-Regular.ttf

./z2/spiffy_theme_backend_ent/static/lib/Cairo:
Cairo-Black.ttf      Cairo-ExtraLight.ttf  Cairo-Regular.ttf
Cairo-Bold.ttf       Cairo-Light.ttf       Cairo-SemiBold.ttf
Cairo-ExtraBold.ttf  Cairo-Medium.ttf      README.txt

./z2/spiffy_theme_backend_ent/static/lib/Inter:
Inter-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend_ent/static/lib/JosefinSans:
JosefinSans-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend_ent/static/lib/Lato:
Lato-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend_ent/static/lib/Manrope:
Manrope-Regular.ttf  OFL.txt

./z2/spiffy_theme_backend_ent/static/lib/Montserrat:
Montserrat-Regular.ttf

./z2/spiffy_theme_backend_ent/static/lib/NunitoSans:
NunitoSans-Black.ttf             NunitoSans-Italic.ttf
NunitoSans-BlackItalic.ttf       NunitoSans-Light.ttf
NunitoSans-Bold.ttf              NunitoSans-LightItalic.ttf
NunitoSans-BoldItalic.ttf        NunitoSans-Regular.ttf
NunitoSans-ExtraBold.ttf         NunitoSans-SemiBold.ttf
NunitoSans-ExtraBoldItalic.ttf   NunitoSans-SemiBoldItalic.ttf
NunitoSans-ExtraLight.ttf        OFL.txt
NunitoSans-ExtraLightItalic.ttf

./z2/spiffy_theme_backend_ent/static/lib/OpenSans:
OpenSans-Regular.ttf

./z2/spiffy_theme_backend_ent/static/lib/Oswald:
Oswald-Regular.ttf

./z2/spiffy_theme_backend_ent/static/lib/Poppins:
OFL.txt  Poppins-Regular.ttf

./z2/spiffy_theme_backend_ent/static/lib/Raleway:
Raleway-Black.ttf             Raleway-Light.ttf
Raleway-BlackItalic.ttf       Raleway-LightItalic.ttf
Raleway-Bold.ttf              Raleway-Medium.ttf
Raleway-BoldItalic.ttf        Raleway-MediumItalic.ttf
Raleway-ExtraBold.ttf         Raleway-Regular.ttf
Raleway-ExtraBoldItalic.ttf   Raleway-SemiBold.ttf
Raleway-ExtraLight.ttf        Raleway-SemiBoldItalic.ttf
Raleway-ExtraLightItalic.ttf  Raleway-Thin.ttf
Raleway-Italic.ttf            Raleway-ThinItalic.ttf

./z2/spiffy_theme_backend_ent/static/lib/RemixIcon:
remixicon.css         remixicon.less        remixicon.ttf
remixicon.eot         remixicon.svg         remixicon.woff
remixicon.glyph.json  remixicon.symbol.svg  remixicon.woff2

./z2/spiffy_theme_backend_ent/static/lib/Roboto:
LICENSE.txt             Roboto-LightItalic.ttf
Roboto-Black.ttf        Roboto-Medium.ttf
Roboto-BlackItalic.ttf  Roboto-MediumItalic.ttf
Roboto-Bold.ttf         Roboto-Regular.ttf
Roboto-BoldItalic.ttf   Roboto-Thin.ttf
Roboto-Italic.ttf       Roboto-ThinItalic.ttf
Roboto-Light.ttf

./z2/spiffy_theme_backend_ent/static/lib/Rubik:
OFL.txt  Rubik-Regular.ttf

./z2/spiffy_theme_backend_ent/static/lib/Tajawal:
OFL.txt            Tajawal-ExtraBold.ttf   Tajawal-Medium.ttf
Tajawal-Black.ttf  Tajawal-ExtraLight.ttf  Tajawal-Regular.ttf
Tajawal-Bold.ttf   Tajawal-Light.ttf

./z2/spiffy_theme_backend_ent/static/lib/VarelaRound:
OFL.txt  VarelaRound-Regular.ttf

./z2/spiffy_theme_backend_ent/static/src:
image/  js/  scss/  xml/

./z2/spiffy_theme_backend_ent/static/src/image:
 backend_theme_icon.png          pallet_3.png
 background-color.svg            pallet_4.png
 checked.png                     pallet_5.png
 circle.png                      pallet_6.png
 close.png                       pallet_7.png
'download (1).png'               pallet_8.png
 download.svg                    pallet_9.png
 iconoff.png                     pallets.jpg
 iconon.png                      shape-style-rounded.png
 icons8-light-50.png             shape-style-square.png
 icons8-light-on-50.png          shape-style-standard.png
 input-style-bordered.png        theme-chatter-bottom.png
 input-style-borderless.png      theme-chatter-right.png
 input-style-bottom-border.png   theme-menu-vertical-center.png
 list_comfortable.png            theme-menu-vertical-left.png
 list_compact.png                theme-menu-vertical_mini.png
 loader_1.gif                    theme-menu_horizontal.png
 loader_1.svg                    theme-separator-style1.png
 loader_10.gif                   theme-separator-style2.png
 loader_2.gif                    theme-separator-style3.png
 loader_3.gif                    theme-separator-style4.png
 loader_4.gif                    theme-style-rounded.png
 loader_5.gif                    theme-style-square.png
 loader_6.gif                    theme-style-standard.png
 loader_7.gif                    top-menu-v2-bg-four.png
 loader_8.gif                    top-menu-v2-bg-one.png
 loader_9.gif                    top-menu-v2-bg-three.png
 pallet_1.png                    top-menu-v2-bg-two.png
 pallet_10.png                   top-menu-v2-bg1.jpg
 pallet_11.png                   top-menu-v2-bg2.jpg
 pallet_12.png                   top-menu-v2-bg3.jpg
 pallet_13.png                   top-menu-v2-bg4.jpg
 pallet_14.png                   top-menu-v2-bg5.jpg
 pallet_15.png                   ui-icons_444444_256x240.png
 pallet_16.png                   ui-icons_555555_256x240.png
 pallet_17.png                   ui-icons_777620_256x240.png
 pallet_18.png                   ui-icons_777777_256x240.png
 pallet_19.png                   ui-icons_cc0000_256x240.png
 pallet_2.png                    ui-icons_ffffff_256x240.png

./z2/spiffy_theme_backend_ent/static/src/js:
SpiffyPageTitle.js    flip_min.js            menu_service.js
SwitchCompanyMenu.js  form_controller.js     pager.js
action_service.js     form_view_renderer.js  pwebapp.js
apps_menu.js          iconpack_load.js       service_worker.js
color_pallet.js       jquery-ui/             split_view/
dialog.js             list_view_renderer.js  user_menu.js
enterprise/           menu.js                widgets/

./z2/spiffy_theme_backend_ent/static/src/js/enterprise:
studio_integration.js

./z2/spiffy_theme_backend_ent/static/src/js/jquery-ui:
jquery-ui.min.css  jquery-ui.min.js

./z2/spiffy_theme_backend_ent/static/src/js/split_view:
split_view_components.js  split_view_form.js
split_view_container.js   split_view_form.xml
split_view_controller.js

./z2/spiffy_theme_backend_ent/static/src/js/widgets:
spiffyDocumentViewer.js    spiffyDocumentViewer.xml
spiffyDocumentViewer.scss

./z2/spiffy_theme_backend_ent/static/src/scss:
activity_view.scss     loader.scss
appdrawer.scss         loginpage.scss
bookmarks.scss         menu_shape_styles.scss
burger_menu.scss       modal.scss
calendear_view.scss    multi_tab.scss
chat_window.scss       notification.scss
checkbox_styles.scss   pivot_view.scss
common_view.scss       popup_styles.scss
controlpannel.scss     radio_styles.scss
custom_varibles.scss   responsive.scss
dashboards.scss        search_modal.scss
datetime_pickers.scss  search_panel.scss
discuss_style.scss     separator_styles.scss
enterprise/            setting_page.scss
enterprise.scss        side_menu.scss
font-family.scss       tab_styles.scss
font_icons.scss        to_do_list.scss
font_sizes/            top_menu_horizontal.scss
form_chatter.scss      top_menu_vertical.scss
form_view.scss         top_menu_vertical_mini.scss
graph_view.scss        tree_form_split_view.scss
kanban_view.scss       website_menu.scss
list_view.scss

./z2/spiffy_theme_backend_ent/static/src/scss/enterprise:
enterprise_form.scss    enterprise_list.scss    variables.scss
enterprise_kanban.scss  enterprise_search.scss

./z2/spiffy_theme_backend_ent/static/src/scss/font_sizes:
font_large.css  font_medium.css  font_small.css

./z2/spiffy_theme_backend_ent/static/src/xml:
base.xml                        list_renderer.xml
bookmark.xml                    menu.xml
fileviewer.xml                  spiffy_app_menu_group.xml
form_statusbar.xml              view_button_icons.xml
inherit_mobile_apps_funnel.xml  web_inherit.xml

./z2/spiffy_theme_backend_ent/views:
backend_configurator_template.xml  pwa_offline.xml
backend_configurator_view.xml      pwa_shortcuts_view.xml
global_search_view.xml             res_config_setting.xml
ir_module_view.xml                 res_users_view.xml
login_page_style.xml               spiffy_app_group_view.xml
manifest.xml                       studio_config_view.xml
menuitems.xml                      templates_inherit.xml
push_notification_menu_view.xml    to_do_list_template.xml

./z3:
acs_certification/  acs_hms_barcode/        acs_hms_consent_form/
acs_commission/     acs_hms_base/           acs_hms_dashboard/
acs_consent_form/   acs_hms_blood_bank/     acs_hms_document/
acs_hms/            acs_hms_certification/  acs_hms_document_preview/
acs_hms_ambulance/  acs_hms_commission/

./z3/acs_certification:
__init__.py      data/  doc/   models/  security/  views/
__manifest__.py  demo/  i18n/  report/  static/

./z3/acs_certification/data:
data.xml  digest_data.xml

./z3/acs_certification/demo:
demo.xml

./z3/acs_certification/doc:
changelog.rst

./z3/acs_certification/i18n:
acs_certification.pot  ar.po  es_EC.po  es_PA.po

./z3/acs_certification/models:
__init__.py  certificate_management.py  digest.py

./z3/acs_certification/report:
certificate_report.xml

./z3/acs_certification/security:
ir.model.access.csv  security.xml

./z3/acs_certification/static:
description/

./z3/acs_certification/static/description:
acs.png
acs_certificate_almightycs_cover.jpg
acs_certificate_form_odoo_almightycs.png
acs_certificate_report_odoo_almightycs.png
acs_certificate_template_odoo_almightycs.png
icon.png
index.html
odoo_certification_almightycs.jpg

./z3/acs_certification/views:
certificate_management_view.xml  digest_view.xml  menu_item.xml

./z3/acs_commission:
__init__.py      data/  i18n/    reports/   static/  wizard/
__manifest__.py  doc/   models/  security/  views/

./z3/acs_commission/data:
data.xml

./z3/acs_commission/doc:
changelog.rst

./z3/acs_commission/i18n:
acs_commission.pot  ar.po  es.po  es_EC.po  es_PA.po

./z3/acs_commission/models:
__init__.py      commission.py        partner.py
account_move.py  commission_sheet.py  res_config_settings.py

./z3/acs_commission/reports:
commission_sheet_report.xml

./z3/acs_commission/security:
ir.model.access.csv  security.xml

./z3/acs_commission/static:
description/

./z3/acs_commission/static/description:
acs.png                              almightycs.png  index.html
acs_commission_almightycs_cover.jpg  icon.png

./z3/acs_commission/views:
account_view.xml           menu_item.xml
commission_sheet_view.xml  partner_view.xml
commission_view.xml        res_config_settings_views.xml

./z3/acs_commission/wizard:
__init__.py                create_commission_bill_views.xml
create_commission_bill.py

./z3/acs_consent_form:
__init__.py      controllers/  demo/  i18n/    report/    static/
__manifest__.py  data/         doc/   models/  security/  views/

./z3/acs_consent_form/controllers:
__init__.py  main.py

./z3/acs_consent_form/data:
data.xml

./z3/acs_consent_form/demo:
demo.xml

./z3/acs_consent_form/doc:
changelog.rst

./z3/acs_consent_form/i18n:
acs_consent_form.pot  es.po  es_PA.po

./z3/acs_consent_form/models:
__init__.py  acs_consent_form.py

./z3/acs_consent_form/report:
consent_document_report.xml

./z3/acs_consent_form/security:
ir.model.access.csv  security.xml

./z3/acs_consent_form/static:
description/

./z3/acs_consent_form/static/description:
acs.png
acs_consent_form_odoo_almightycs.png
acs_consent_form_portal_odoo_almightycs.png
acs_consent_form_portal_sign_odoo_almightycs.png
acs_consent_form_report_odoo_almightycs.png
acs_consent_form_template_odoo_almightycs.png
almightycs.png
icon.png
index.html
odoo_acs_consent_form_almightycs.jpg

./z3/acs_consent_form/views:
consent_form_view.xml  portal_template.xml

./z3/acs_hms:
__init__.py      data/  i18n/    security/  wizard/
__manifest__.py  demo/  models/  static/
controllers/     doc/   report/  views/

./z3/acs_hms/controllers:
__init__.py  acs_hms.py

./z3/acs_hms/data:
digest_data.xml  hms_data.xml  mail_template.xml  sequence.xml

./z3/acs_hms/demo:
appointment_demo.xml  medicament_demo.xml
doctor_demo.xml       patient_demo.xml

./z3/acs_hms/doc:
changelog.rst

./z3/acs_hms/i18n:
acs_hms.pot  ar.po  es.po  es_EC.po  es_MX.po  es_PA.po  fr.po

./z3/acs_hms/models:
__init__.py     diseases.py    physician.py            resource.py
account.py      evaluation.py  prescription.py         treatment.py
appointment.py  hms_base.py    procedure.py
company.py      medicament.py  product_kit.py
digest.py       patient.py     res_config_settings.py

./z3/acs_hms/report:
appointment_report.xml  report_medical_advice.xml
evaluation_report.xml   report_prescription.xml
patient_cardreport.xml  treatment_report.xml
procedure_report.xml

./z3/acs_hms/security:
ir.model.access.csv  security.xml

./z3/acs_hms/static:
description/  src/

./z3/acs_hms/static/description:
acs.png                       hms_menus_odoo_almightycs.png
almightycs-youtube-video.png  icon.png
almightycs.png                index.html
hms_almightycs_cover.gif      youtube.png
hms_almightycs_cover.jpg

./z3/acs_hms/static/src:
img/  js/  scss/

./z3/acs_hms/static/src/img:
appointment.png       patient.png         patient_img_24.jpg
calendar.png          patient_img_0.jpg   patient_img_25.jpg
doc_img_0.jpg         patient_img_1.jpg   patient_img_26.png
doc_img_1.jpg         patient_img_10.jpg  patient_img_27.jpg
doc_img_2.jpg         patient_img_11.jpg  patient_img_28.jpg
doc_img_3.jpg         patient_img_12.jpg  patient_img_29.jpg
doc_img_4.jpg         patient_img_13.jpg  patient_img_3.jpg
doc_img_5.jpg         patient_img_14.jpg  patient_img_30.jpg
doc_sign_0.png        patient_img_15.jpg  patient_img_4.jpg
doc_sign_1.png        patient_img_16.jpg  patient_img_5.jpg
doc_sign_2.png        patient_img_17.jpg  patient_img_6.jpg
doc_sign_3.png        patient_img_18.jpg  patient_img_7.jpg
doc_sign_4.png        patient_img_19.jpg  patient_img_8.jpg
doc_sign_5.png        patient_img_2.jpg   patient_img_9.jpg
hospital.png          patient_img_20.jpg  physician.png
logo.png              patient_img_21.jpg  pills.png
medicines/            patient_img_22.jpg  prescription.png
pain_scale_chart.jpg  patient_img_23.jpg  treatment.png

./z3/acs_hms/static/src/img/medicines:
Tablets2.jpg           drop.png  injection.jpg  vicks.png
blood-transfusion.png  drug.png  tablets.png

./z3/acs_hms/static/src/js:
graph_widget.js

./z3/acs_hms/static/src/scss:
custom.scss

./z3/acs_hms/views:
account_view.xml      patient_view.xml
appointment_view.xml  physician_view.xml
digest_view.xml       prescription_view.xml
diseases_view.xml     procedure_view.xml
evaluation_view.xml   product_kit_view.xml
hms_base_views.xml    res_config_settings_views.xml
medical_alert.xml     resource_cal.xml
medicament_view.xml   template.xml
medication_view.xml   treatment_view.xml
menu_item.xml

./z3/acs_hms/wizard:
__init__.py             pain_level_view.xml
cancel_reason.py        reschedule_appointments.py
cancel_reason_view.xml  reschedule_appointments_view.xml
pain_level.py

./z3/acs_hms_ambulance:
__init__.py      data/  i18n/    security/  views/
__manifest__.py  doc/   models/  static/

./z3/acs_hms_ambulance/data:
data.xml

./z3/acs_hms_ambulance/doc:
changelog.rst

./z3/acs_hms_ambulance/i18n:
acs_hms_ambulance.pot  es.po  es_PA.po

./z3/acs_hms_ambulance/models:
__init__.py  ambulance_service.py  hms_base.py  res_config.py

./z3/acs_hms_ambulance/security:
hms_security.xml  ir.model.access.csv

./z3/acs_hms_ambulance/static:
description/

./z3/acs_hms_ambulance/static/description:
acs.png         hms_almightycs_cover.jpg  index.html
almightycs.png  icon.png

./z3/acs_hms_ambulance/views:
ambulance_views.xml  menu_items.xml
hms_base_view.xml    res_config_view.xml

./z3/acs_hms_barcode:
__init__.py      data/  i18n/   report/    static/  wizard/
__manifest__.py  doc/   model/  security/  views/

./z3/acs_hms_barcode/data:
data.xml

./z3/acs_hms_barcode/doc:
changelog.rst

./z3/acs_hms_barcode/i18n:
acs_hms_barcode.pot  ar.po  es.po  es_EC.po  es_PA.po

./z3/acs_hms_barcode/model:
__init__.py  patient.py

./z3/acs_hms_barcode/report:
__init__.py              paper_format.xml
barcode_report_view.xml  patient_barcode.py

./z3/acs_hms_barcode/security:
ir.model.access.csv

./z3/acs_hms_barcode/static:
description/

./z3/acs_hms_barcode/static/description:
acs.png         hms_almightycs_cover.jpg       icon.png
almightycs.png  hms_menus_odoo_almightycs.png  index.html

./z3/acs_hms_barcode/views:
patient_view.xml

./z3/acs_hms_barcode/wizard:
__init__.py  patient_barcode_wizard.py  patient_barcode_wizard.xml

./z3/acs_hms_base:
__init__.py      data/  doc/   models/  security/  views/
__manifest__.py  demo/  i18n/  report/  static/

./z3/acs_hms_base/data:
company_data.xml  mail_template.xml  sequence.xml

./z3/acs_hms_base/demo:
company_demo.xml

./z3/acs_hms_base/doc:
changelog.rst

./z3/acs_hms_base/i18n:
acs_hms_base.pot  es.po  es_EC.po  es_PA.po  fr.po

./z3/acs_hms_base/models:
__init__.py  drug.py      ir_sequence.py  patient.py    product.py
account.py   hms_base.py  partner.py      physician.py  res_config.py

./z3/acs_hms_base/report:
paper_format.xml  report_invoice.xml  report_layout.xml

./z3/acs_hms_base/security:
ir.model.access.csv  security.xml

./z3/acs_hms_base/static:
description/  src/

./z3/acs_hms_base/static/description:
acs.png         hms_almightycs_cover.jpg  index.html
almightycs.png  icon.png

./z3/acs_hms_base/static/src:
img/  js/  scss/

./z3/acs_hms_base/static/src/img:
birthday1.gif  logo.png     physician.png  services.png
hospital.png   patient.png  pills.png

./z3/acs_hms_base/static/src/js:
acs.js

./z3/acs_hms_base/static/src/scss:
acs.scss  report.scss

./z3/acs_hms_base/views:
account_view.xml    menu_item.xml       product_view.xml
drug_view.xml       patient_view.xml    res_config_settings.xml
hms_base_views.xml  physician_view.xml

./z3/acs_hms_blood_bank:
__init__.py      data/  i18n/    security/  views/
__manifest__.py  doc/   models/  static/

./z3/acs_hms_blood_bank/data:
data.xml

./z3/acs_hms_blood_bank/doc:
changelog.rst

./z3/acs_hms_blood_bank/i18n:
acs_hms_blood_bank.pot  es.po  es_PA.po

./z3/acs_hms_blood_bank/models:
__init__.py  blood_bank.py  hms_base.py  res_config.py

./z3/acs_hms_blood_bank/security:
hms_security.xml  ir.model.access.csv

./z3/acs_hms_blood_bank/static:
description/

./z3/acs_hms_blood_bank/static/description:
acs.png         hms_almightycs_cover.jpg  index.html
almightycs.png  icon.png

./z3/acs_hms_blood_bank/views:
blood_bank_views.xml  partner_view.xml  res_config_view.xml
menu_items.xml        patient_view.xml  stock_view.xml

./z3/acs_hms_certification:
__init__.py      controllers/  i18n/    report/    static/
__manifest__.py  doc/          models/  security/  views/

./z3/acs_hms_certification/controllers:
__init__.py  main.py

./z3/acs_hms_certification/doc:
changelog.rst

./z3/acs_hms_certification/i18n:
acs_hms_certification.pot  ar.po  es.po  es_EC.po  es_PA.po

./z3/acs_hms_certification/models:
__init__.py  certificate_management.py  res_config.py

./z3/acs_hms_certification/report:
certificate_report.xml

./z3/acs_hms_certification/security:
ir.model.access.csv  security.xml

./z3/acs_hms_certification/static:
description/

./z3/acs_hms_certification/static/description:
acs.png
acs_hms_certificate_form_odoo_almightycs.png
acs_hms_certificate_report_odoo_almightycs.png
acs_hms_certificate_template_odoo_almightycs.png
almightycs.png
hms_certificate_almightycs_cover.jpg
icon.png
index.html

./z3/acs_hms_certification/views:
certificate_management_view.xml  res_config_views.xml
portal_template.xml              template.xml

./z3/acs_hms_commission:
__init__.py      doc/   models/    static/  wizard/
__manifest__.py  i18n/  security/  views/

./z3/acs_hms_commission/doc:
changelog.rst

./z3/acs_hms_commission/i18n:
acs_hms_commission.pot  es_EC.po  es_PA.po

./z3/acs_hms_commission/models:
__init__.py  account_move.py  hms_base.py

./z3/acs_hms_commission/security:
ir.model.access.csv  security.xml

./z3/acs_hms_commission/static:
description/

./z3/acs_hms_commission/static/description:
acs.png         hms_commission_almightycs_cover.jpg  icon.png
almightycs.png  hms_menus_odoo_almightycs.png        index.html

./z3/acs_hms_commission/views:
hms_base_view.xml  menu_item.xml

./z3/acs_hms_commission/wizard:
__init__.py  create_commission_bill.py

./z3/acs_hms_consent_form:
__init__.py      doc/   models/    static/
__manifest__.py  i18n/  security/  views/

./z3/acs_hms_consent_form/doc:
changelog.rst

./z3/acs_hms_consent_form/i18n:
acs_hms_consent_form.pot  es.po  es_PA.po

./z3/acs_hms_consent_form/models:
__init__.py  acs_consent_form.py

./z3/acs_hms_consent_form/security:
ir.model.access.csv

./z3/acs_hms_consent_form/static:
description/

./z3/acs_hms_consent_form/static/description:
acs.png
acs_consent_form_odoo_almightycs.png
acs_consent_form_portal_odoo_almightycs.png
acs_consent_form_portal_sign_odoo_almightycs.png
acs_consent_form_report_odoo_almightycs.png
acs_consent_form_template_odoo_almightycs.png
almightycs.png
icon.png
index.html
odoo_acs_consent_form_almightycs.jpg

./z3/acs_hms_consent_form/views:
consent_form_view.xml

./z3/acs_hms_dashboard:
__init__.py      doc/   model/     static/
__manifest__.py  i18n/  security/  views/

./z3/acs_hms_dashboard/doc:
changelog.rst

./z3/acs_hms_dashboard/i18n:
acs_hms_dashboard.pot  ar.po  es.po  es_EC.po  es_PA.po

./z3/acs_hms_dashboard/model:
__init__.py  user_dashboard.py

./z3/acs_hms_dashboard/security:
security.xml

./z3/acs_hms_dashboard/static:
description/  src/

./z3/acs_hms_dashboard/static/description:
acs.png
acs_hms_admin_dashboard_almightycs_odoo.png
acs_hms_dashboard_almightycs_odoo_configuration.png
acs_hms_dashboard_almightycs_odoo_cover.gif
acs_hms_doctor_dashboard_almightycs_odoo.png
acs_hms_receptionist_dashboard_almightycs_odoo.png
almightycs.png
icon.png
index.html

./z3/acs_hms_dashboard/static/src:
scss/

./z3/acs_hms_dashboard/static/src/scss:
acs_dashboard.scss

./z3/acs_hms_dashboard/views:
user_dashboard_view.xml  user_view.xml

./z3/acs_hms_document:
__init__.py      doc/   model/     static/
__manifest__.py  i18n/  security/  view/

./z3/acs_hms_document/doc:
changelog.rst

./z3/acs_hms_document/i18n:
acs_hms_document.pot  ar.po  es.po  es_EC.po  es_PA.po  fr.po

./z3/acs_hms_document/model:
__init__.py  document_management.py

./z3/acs_hms_document/security:
ir.model.access.csv  security.xml

./z3/acs_hms_document/static:
description/

./z3/acs_hms_document/static/description:
acs.png
almightycs.png
hms_document_management_system_almightycs_cover.jpg
hms_menus_odoo_almightycs.png
icon.png
index.html

./z3/acs_hms_document/view:
document_view.xml  menu_item.xml

./z3/acs_hms_document_preview:
__init__.py      controllers/  i18n/    static/
__manifest__.py  doc/          models/  views/

./z3/acs_hms_document_preview/controllers:
__init__.py  main.py

./z3/acs_hms_document_preview/doc:
changelog.rst

./z3/acs_hms_document_preview/i18n:
acs_hms_document_preview.pot  ar.po  es.po  es_EC.po  es_PA.po  fr.po

./z3/acs_hms_document_preview/models:
__init__.py  document_preview.py

./z3/acs_hms_document_preview/static:
description/  src/

./z3/acs_hms_document_preview/static/description:
acs.png                                    icon.png
acs_hms_document_preview_almightycs.png    index.html
almightycs.png                             search.png
hms_document_preview_almightycs_cover.jpg

./z3/acs_hms_document_preview/static/src:
js/  lib/

./z3/acs_hms_document_preview/static/src/js:
custom.js

./z3/acs_hms_document_preview/static/src/lib:
css/  images/  img/  js/  skins/  themes/

./z3/acs_hms_document_preview/static/src/lib/css:
unite-gallery.css

./z3/acs_hms_document_preview/static/src/lib/images:
button-close.png                  loader-black7.gif
cover-grid.png                    loader-white1.gif
icon-link32.png                   loader-white2.gif
icon-play32.png                   loader-white3.gif
icon-zoom32.png                   loader-white4.gif
lightbox-arrow-left.png           loader-white5.gif
lightbox-arrow-right.png          loader-white6.gif
lightbox-icon-close-compact.png   loader-white7.gif
lightbox-icon-close-compact2.png  loader.gif
lightbox-icon-close.png           loader_bright.gif
loader-black1.gif                 loader_skype_trans.gif
loader-black2.gif                 not_loaded.png
loader-black3.gif                 play-button-round.png
loader-black4.gif                 play-button-square.png
loader-black5.gif                 transparent.png
loader-black6.gif

./z3/acs_hms_document_preview/static/src/lib/img:
next-slide.png  prev-slide.png

./z3/acs_hms_document_preview/static/src/lib/js:
jquery-11.0.min.js      ug-lightbox.js       ug-thumbsgrid.js
ug-api.js               ug-loadmore.js       ug-thumbsstrip.js
ug-avia.js              ug-panelsbase.js     ug-tiledesign.js
ug-carousel.js          ug-slider.js         ug-tiles.js
ug-common-libraries.js  ug-sliderassets.js   ug-touchslider.js
ug-functions.js         ug-strippanel.js     ug-touchthumbs.js
ug-gallery.js           ug-tabs.js           ug-video.js
ug-gridpanel.js         ug-thumbsgeneral.js  ug-zoomslider.js

./z3/acs_hms_document_preview/static/src/lib/skins:
alexis/  default/

./z3/acs_hms_document_preview/static/src/lib/skins/alexis:
alexis.css  images/

./z3/acs_hms_document_preview/static/src/lib/skins/alexis/images:
arrows_strip_left.png        grid_arrow_right.png
arrows_strip_right.png       grid_arrow_right_hortype.png
button_fullscreen.png        grid_arrow_up.png
button_playpause.png         grid_handle_down.png
button_zoom_back.png         grid_handle_left.png
button_zoom_minus.png        grid_handle_right.png
button_zoom_plus.png         grid_handle_up.png
grid_arrow_down.png          slider_arrow_left.png
grid_arrow_left.png          slider_arrow_right.png
grid_arrow_left_hortype.png  slider_bullets.png

./z3/acs_hms_document_preview/static/src/lib/skins/default:
arrow_grid_down.png           icon_zoom_back.png
arrow_grid_up.png             icon_zoom_minus.png
arrows_strip_down.png         icon_zoom_plus.png
arrows_strip_left.png         slider_arrow_left.png
arrows_strip_right.png        slider_arrow_right.png
arrows_strip_up.png           slider_bullets.png
button_fullscreen.png         tile_bullets_blue.png
button_playpause.png          tile_bullets_brown.png
grid_arrow_left.png           tile_bullets_gray.png
grid_arrow_right.png          tile_bullets_green.png
grid_handle_black_bottom.png  tile_bullets_red.png
grid_handle_black_left.png    tile_button_left.png
grid_handle_black_right.png   tile_button_play_pause.png
grid_handle_black_top.png     tile_button_right.png

./z3/acs_hms_document_preview/static/src/lib/themes:
carousel/  default/  slider/  tilesgrid/
compact/   grid/     tiles/   video/

./z3/acs_hms_document_preview/static/src/lib/themes/carousel:
ug-theme-carousel.js

./z3/acs_hms_document_preview/static/src/lib/themes/compact:
ug-theme-compact.js

./z3/acs_hms_document_preview/static/src/lib/themes/default:
images/  ug-theme-default.css  ug-theme-default.js

./z3/acs_hms_document_preview/static/src/lib/themes/default/images:
arrow_down_up.png       button_fullscreen_single.png
arrows_strip_left.png   button_playpause.png
arrows_strip_right.png  button_playpause_single.png
button_fullscreen.png

./z3/acs_hms_document_preview/static/src/lib/themes/grid:
ug-theme-grid.js

./z3/acs_hms_document_preview/static/src/lib/themes/slider:
ug-theme-slider.js

./z3/acs_hms_document_preview/static/src/lib/themes/tiles:
ug-theme-tiles.js

./z3/acs_hms_document_preview/static/src/lib/themes/tilesgrid:
ug-theme-tilesgrid.js

./z3/acs_hms_document_preview/static/src/lib/themes/video:
images/                  skin-right-thumb.css
skin-bottom-text.css     skin-right-title-only.css
skin-right-no-thumb.css  ug-theme-video.js

./z3/acs_hms_document_preview/static/src/lib/themes/video/images:
arrow_left.png  arrow_right.png

./z3/acs_hms_document_preview/views:
template.xml

./z4:
acs.zip                             acs_hms_surgery/
acs_hms_hospitalization/            acs_hms_vaccination/
acs_hms_icd10/                      acs_hms_veterinary/
acs_hms_insurance/                  acs_hms_video_call/
acs_hms_insurance_hospitalization/  acs_hms_webcam/
acs_hms_laboratory/                 acs_invoice_split/
acs_hms_medical_representative/     acs_jitsi_meet/
acs_hms_next_patient_screen/        acs_laboratory/
acs_hms_nursing/                    acs_pharmacy/
acs_hms_online_appointment/         acs_product_barcode_generator/
acs_hms_operation_theater/          acs_sms/
acs_hms_pharmacy/                   acs_video_call/
acs_hms_portal/                     acs_webcam/
acs_hms_pro/                        acs_whatsapp/
acs_hms_sms/                        acs_whatsapp_chatapi/
acs_hms_subscription/

./z4/acs_hms_hospitalization:
__init__.py      data/  i18n/    report/    static/  wizard/
__manifest__.py  doc/   models/  security/  views/

./z4/acs_hms_hospitalization/data:
digest_data.xml  hms_data.xml  sequence.xml

./z4/acs_hms_hospitalization/doc:
changelog.rst

./z4/acs_hms_hospitalization/i18n:
acs_hms_hospitalization.pot  ar.po  es.po  es_PA.po  fr.po

./z4/acs_hms_hospitalization/models:
__init__.py        hms_base.py         hospitalization_care.py
death_register.py  hospital_base.py    res_config_settings.py
digest.py          hospitalization.py

./z4/acs_hms_hospitalization/report:
report_hospital_discharge.xml            report_visiting_pass.xml
report_hospitalization_forecast.xml      ward_patient_list_report.xml
report_hospitalization_patient_card.xml

./z4/acs_hms_hospitalization/security:
ir.model.access.csv  security.xml

./z4/acs_hms_hospitalization/static:
description/

./z4/acs_hms_hospitalization/static/description:
acs.png
almightycs.png
hms_hospitalization_almightycs_odoo_cover.jpg
hms_menus_odoo_almightycs.png
icon.png
index.html

./z4/acs_hms_hospitalization/views:
bed_view.xml                 hospitalization_care_views.xml
building_view.xml            hospitalization_view.xml
care_plan_template_view.xml  menu_item.xml
death_register.xml           ot_view.xml
digest_view.xml              res_config_settings_views.xml
hms_base_view.xml            ward_view.xml

./z4/acs_hms_hospitalization/wizard:
__init__.py
acs_hospitalization_forecast.py
acs_hospitalization_forecast_view.xml
transfer_accommodation.py
transfer_accommodation_view.xml

./z4/acs_hms_icd10:
__init__.py  __manifest__.py  data/  doc/  i18n/  models/  static/

./z4/acs_hms_icd10/data:
disease_categories.xml  diseases.xml

./z4/acs_hms_icd10/doc:
changelog.rst

./z4/acs_hms_icd10/i18n:
acs_hms_icd10.pot  ar.po  es.po  es_PA.po

./z4/acs_hms_icd10/models:
__init__.py  hms_base.py

./z4/acs_hms_icd10/static:
description/

./z4/acs_hms_icd10/static/description:
acs.png                             almightycs.png  index.html
acs_hms_icd10_almightycs_cover.jpg  icon.png

./z4/acs_hms_insurance:
__init__.py      controllers/  demo/  i18n/    report/    static/
__manifest__.py  data/         doc/   models/  security/  views/

./z4/acs_hms_insurance/controllers:
__init__.py  main.py

./z4/acs_hms_insurance/data:
data.xml

./z4/acs_hms_insurance/demo:
demo.xml

./z4/acs_hms_insurance/doc:
changelog.rst

./z4/acs_hms_insurance/i18n:
acs_hms_insurance.pot  ar.po  es.po  es_EC.po  es_PA.po

./z4/acs_hms_insurance/models:
__init__.py     hms_base.py   insurance_claim.py
claim_sheet.py  insurance.py  package.py

./z4/acs_hms_insurance/report:
claim_report.xml  claim_sheet_report.xml  package_report.xml

./z4/acs_hms_insurance/security:
ir.model.access.csv  security.xml

./z4/acs_hms_insurance/static:
description/

./z4/acs_hms_insurance/static/description:
acs.png
almightycs.png
hms_insuranceacs_almightycs_odoo_cover.jpg
hms_menus_odoo_almightycs.png
icon.png
index.html

./z4/acs_hms_insurance/views:
claim_sheet_view.xml  insurance_view.xml  portal_template.xml
claim_view.xml        menu_items.xml      tpa_view.xml
hms_base_view.xml     package_view.xml

./z4/acs_hms_insurance_hospitalization:
__init__.py      doc/   models/    static/
__manifest__.py  i18n/  security/  views/

./z4/acs_hms_insurance_hospitalization/doc:
changelog.rst

./z4/acs_hms_insurance_hospitalization/i18n:
acs_hms_insurance_hospitalization.pot  es.po  es_EC.po  es_PA.po

./z4/acs_hms_insurance_hospitalization/models:
__init__.py  hms_base.py  insurance.py

./z4/acs_hms_insurance_hospitalization/security:
ir.model.access.csv

./z4/acs_hms_insurance_hospitalization/static:
description/

./z4/acs_hms_insurance_hospitalization/static/description:
acs.png
almightycs.png
hms_insuranceacs_almightycs_odoo_cover.jpg
hms_menus_odoo_almightycs.png
icon.png
index.html

./z4/acs_hms_insurance_hospitalization/views:
claim_view.xml  hms_base_view.xml

./z4/acs_hms_laboratory:
__init__.py      doc/   models/  security/  views/
__manifest__.py  i18n/  report/  static/

./z4/acs_hms_laboratory/doc:
changelog.rst

./z4/acs_hms_laboratory/i18n:
acs_hms_laboratory.pot  es.po  es_EC.po  es_PA.po  fr.po

./z4/acs_hms_laboratory/models:
__init__.py  hms_base.py  lab_test.py  laboratory.py

./z4/acs_hms_laboratory/report:
discharge_summary_report.xml  report_acs_lab_prescription.xml
lab_report.xml

./z4/acs_hms_laboratory/security:
ir.model.access.csv  security.xml

./z4/acs_hms_laboratory/static:
description/

./z4/acs_hms_laboratory/static/description:
acs.png         hms_laboratory_almightycs_cover.jpg  icon.png
almightycs.png  hms_menus_odoo_almightycs.png        index.html

./z4/acs_hms_laboratory/views:
hms_base_view.xml  laboratory_view.xml

./z4/acs_hms_medical_representative:
__init__.py      data/  i18n/    security/  views/
__manifest__.py  doc/   models/  static/

./z4/acs_hms_medical_representative/data:
sequence.xml

./z4/acs_hms_medical_representative/doc:
changelog.rst

./z4/acs_hms_medical_representative/i18n:
acs_hms_medical_representative.pot  ar.po  es.po  es_PA.po

./z4/acs_hms_medical_representative/models:
__init__.py  medical_representative.py

./z4/acs_hms_medical_representative/security:
ir.model.access.csv

./z4/acs_hms_medical_representative/static:
description/

./z4/acs_hms_medical_representative/static/description:
acs.png
almightycs.png
hms_medical_representative_almightycs_cover.jpg
hms_menus_odoo_almightycs.png
icon.png
index.html

./z4/acs_hms_medical_representative/views:
mr_view.xml

./z4/acs_hms_next_patient_screen:
__init__.py      controllers/  doc/   models/    static/
__manifest__.py  data/         i18n/  security/  views/

./z4/acs_hms_next_patient_screen/controllers:
__init__.py  main.py

./z4/acs_hms_next_patient_screen/data:
data.xml

./z4/acs_hms_next_patient_screen/doc:
changelog.rst

./z4/acs_hms_next_patient_screen/i18n:
acs_hms_next_patient_screen.pot  ar.po  es.po  es_PA.po

./z4/acs_hms_next_patient_screen/models:
__init__.py  waiting_screen.py

./z4/acs_hms_next_patient_screen/security:
ir.model.access.csv  security.xml

./z4/acs_hms_next_patient_screen/static:
description/  src/

./z4/acs_hms_next_patient_screen/static/description:
acs.png         hms_patient_waiting_screen_odoo_almightycs.png
almightycs.png  icon.png
avatar.png      index.html

./z4/acs_hms_next_patient_screen/static/src:
css/  img/

./z4/acs_hms_next_patient_screen/static/src/css:
bootstrap.min.css  hms_next_patient_screen.css

./z4/acs_hms_next_patient_screen/static/src/img:
dummy.jpg

./z4/acs_hms_next_patient_screen/views:
template_view.xml  waiting_screen_view.xml

./z4/acs_hms_nursing:
__init__.py      data/  i18n/    security/  view/
__manifest__.py  doc/   models/  static/

./z4/acs_hms_nursing/data:
data.xml

./z4/acs_hms_nursing/doc:
changelog.rst

./z4/acs_hms_nursing/i18n:
acs_hms_nursing.pot  es.po  es_PA.po

./z4/acs_hms_nursing/models:
__init__.py  acs_hms_nursing.py  hms_base.py

./z4/acs_hms_nursing/security:
ir.model.access.csv

./z4/acs_hms_nursing/static:
description/

./z4/acs_hms_nursing/static/description:
acs.png                               almightycs.png  index.html
acs_hms_nursing_almightycs_cover.jpg  icon.png        nurse.png

./z4/acs_hms_nursing/view:
acs_hms_nursing.xml  hms_base.xml  menu_item.xml

./z4/acs_hms_online_appointment:
__init__.py      controllers/  doc/   models/    static/  wizard/
__manifest__.py  data/         i18n/  security/  views/

./z4/acs_hms_online_appointment/controllers:
__init__.py  main.py

./z4/acs_hms_online_appointment/data:
data.xml  website_page.xml

./z4/acs_hms_online_appointment/doc:
changelog.rst

./z4/acs_hms_online_appointment/i18n:
acs_hms_online_appointment.pot  ar.po  es.po  es_EC.po  es_PA.po

./z4/acs_hms_online_appointment/models:
__init__.py  hms_base.py  res_config_settings.py  schedule.py

./z4/acs_hms_online_appointment/security:
ir.model.access.csv  security.xml

./z4/acs_hms_online_appointment/static:
description/  src/

./z4/acs_hms_online_appointment/static/description:
Appointment_Booked_Thank_You_Message_Odoo_Almightycs.png
acs.png
acs_hms_online_booking_almightycs_cover.jpg
almightycs.png
appointmen_schedule_odoo_almitycs.png
appointment_from_portal_view_odoo_almightycs.png
appointment_online_booking_portal_almightycs.png
appointment_portal_menu_odoo_almightycs.png
icon.png
index.html

./z4/acs_hms_online_appointment/static/src:
js/  scss/

./z4/acs_hms_online_appointment/static/src/js:
hms_portal.js  payment_form.js

./z4/acs_hms_online_appointment/static/src/scss:
custom.scss

./z4/acs_hms_online_appointment/views:
hms_base_view.xml  res_config_settings_views.xml  template.xml
menu_item.xml      schedule_views.xml

./z4/acs_hms_online_appointment/wizard:
__init__.py                       payment_link.py
appointment_scheduler.py          payment_link_views.xml
appointment_scheduler_wizard.xml

./z4/acs_hms_operation_theater:
__init__.py      data/  i18n/    reports/   static/  wizard/
__manifest__.py  doc/   models/  security/  views/

./z4/acs_hms_operation_theater/data:
data.xml

./z4/acs_hms_operation_theater/doc:
changelog.rst

./z4/acs_hms_operation_theater/i18n:
acs_hms_operation_theater.pot  ar.po  es.po  es_EC.po  es_PA.po

./z4/acs_hms_operation_theater/models:
__init__.py  hms_base.py  ot_booking.py

./z4/acs_hms_operation_theater/reports:
__init__.py  ot_report.py  ot_report_template.xml

./z4/acs_hms_operation_theater/security:
ir.model.access.csv

./z4/acs_hms_operation_theater/static:
description/

./z4/acs_hms_operation_theater/static/description:
acs.png                                              almightycs.png
acs_hms_operation_theater_almightycs_odoo.png        icon.png
acs_hms_operation_theater_almightycs_odoo_cover.jpg  index.html

./z4/acs_hms_operation_theater/views:
hms_base.xml  menu_item.xml  ot_view.xml

./z4/acs_hms_operation_theater/wizard:
__init__.py  ot_report.py  ot_report_views.xml

./z4/acs_hms_pharmacy:
__init__.py      doc/   model/     static/
__manifest__.py  i18n/  security/  views/

./z4/acs_hms_pharmacy/doc:
changelog.rst

./z4/acs_hms_pharmacy/i18n:
acs_hms_pharmacy.pot  es.po  es_PA.po

./z4/acs_hms_pharmacy/model:
__init__.py  hms_base.py

./z4/acs_hms_pharmacy/security:
ir.model.access.csv  security.xml

./z4/acs_hms_pharmacy/static:
description/

./z4/acs_hms_pharmacy/static/description:
acs.png         hms_menus_odoo_almightycs.png           icon.png
almightycs.png  hms_pharmacy_almightycs_odoo_cover.jpg  index.html

./z4/acs_hms_pharmacy/views:
hms_base_view.xml  menu_item.xml

./z4/acs_hms_portal:
__init__.py      controllers/  doc/   models/    static/
__manifest__.py  data/         i18n/  security/  views/

./z4/acs_hms_portal/controllers:
__init__.py  main.py

./z4/acs_hms_portal/data:
data.xml  email_template.xml

./z4/acs_hms_portal/doc:
changelog.rst

./z4/acs_hms_portal/i18n:
acs_hms_portal.pot  ar.po  es.mo  es.po  es_EC.po  es_PA.mo  es_PA.po

./z4/acs_hms_portal/models:
__init__.py  patient.py  res_config_settings.py  res_users.py

./z4/acs_hms_portal/security:
ir.model.access.csv  security.xml

./z4/acs_hms_portal/static:
description/  src/

./z4/acs_hms_portal/static/description:
acs.png         hms_menus_odoo_almightycs.png         icon.png
almightycs.png  hms_portal_almightycs_odoo_cover.jpg  index.html

./z4/acs_hms_portal/static/src:
js/

./z4/acs_hms_portal/static/src/js:
portal_chart.js

./z4/acs_hms_portal/views:
patient_view.xml  res_config_settings_views.xml  template.xml

./z4/acs_hms_pro:
__init__.py  __manifest__.py  doc/  i18n/  static/

./z4/acs_hms_pro/doc:
changelog.rst

./z4/acs_hms_pro/i18n:
acs_hms_pro.pot  ar_AA.po

./z4/acs_hms_pro/static:
description/

./z4/acs_hms_pro/static/description:
acs.png         hms_almightycs_cover.jpg       icon.png
almightycs.png  hms_menus_odoo_almightycs.png  index.html

./z4/acs_hms_sms:
__init__.py  __manifest__.py  doc/  i18n/  models/  static/  views/

./z4/acs_hms_sms/doc:
changelog.rst

./z4/acs_hms_sms/i18n:
acs_hms_sms.pot  ar.po  es.po  es_PA.po

./z4/acs_hms_sms/models:
__init__.py  company.py  hms.py

./z4/acs_hms_sms/static:
description/

./z4/acs_hms_sms/static/description:
acs.png                                            almightycs.png
acs_employee_sms_announcement_odoo_almightycs.png  icon.png
acs_hms_sms_almightycs_cover.jpg                   index.html
acs_patient_sms_odoo_almightycs.png

./z4/acs_hms_sms/views:
company_view.xml  hms_base_view.xml

./z4/acs_hms_subscription:
__init__.py      data/  i18n/    report/    static/
__manifest__.py  doc/   models/  security/  views/

./z4/acs_hms_subscription/data:
data.xml

./z4/acs_hms_subscription/doc:
changelog.rst

./z4/acs_hms_subscription/i18n:
acs_hms_subscription.pot  es.po  es_PA.po

./z4/acs_hms_subscription/models:
__init__.py  acs_hms.py  hms_contract.py  hms_subscription.py

./z4/acs_hms_subscription/report:
report_subscription.xml

./z4/acs_hms_subscription/security:
ir.model.access.csv  security.xml

./z4/acs_hms_subscription/static:
description/

./z4/acs_hms_subscription/static/description:
acs.png         hms_subscription_almightycs_cover.jpg  index.html
almightycs.png  icon.png

./z4/acs_hms_subscription/views:
acs_hms_views.xml      hms_subscription_view.xml
hms_contract_view.xml  menu_item.xml

./z4/acs_hms_surgery:
__init__.py      data/  doc/   models/  security/  views/
__manifest__.py  demo/  i18n/  report/  static/

./z4/acs_hms_surgery/data:
data.xml  digest_data.xml

./z4/acs_hms_surgery/demo:
hms_demo.xml

./z4/acs_hms_surgery/doc:
changelog.rst

./z4/acs_hms_surgery/i18n:
acs_hms_surgery.pot  es.po  es_EC.po  es_PA.po  fr.po

./z4/acs_hms_surgery/models:
__init__.py  hms_base.py             surgery.py
digest.py    res_config_settings.py  surgery_base.py

./z4/acs_hms_surgery/report:
surgery_report.xml

./z4/acs_hms_surgery/security:
ir.model.access.csv  security.xml

./z4/acs_hms_surgery/static:
description/

./z4/acs_hms_surgery/static/description:
acs.png         hms_menus_odoo_almightycs.png          icon.png
almightycs.png  hms_surgery_almightycs_odoo_cover.jpg  index.html

./z4/acs_hms_surgery/views:
digest_view.xml    menu_item.xml                  surgery_base.xml
hms_base_view.xml  res_config_settings_views.xml  surgery_view.xml

./z4/acs_hms_vaccination:
__init__.py      data/  doc/   models/  security/  views/
__manifest__.py  demo/  i18n/  report/  static/    wizard/

./z4/acs_hms_vaccination/data:
data.xml

./z4/acs_hms_vaccination/demo:
vaccine_demo.xml

./z4/acs_hms_vaccination/doc:
changelog.rst

./z4/acs_hms_vaccination/i18n:
acs_hms_vaccination.pot  ar.po  es.po  es_PA.po

./z4/acs_hms_vaccination/models:
__init__.py  acs_hms.py  res_config.py  vaccination.py

./z4/acs_hms_vaccination/report:
vaccination_report.xml

./z4/acs_hms_vaccination/security:
ir.model.access.csv  security.xml

./z4/acs_hms_vaccination/static:
description/

./z4/acs_hms_vaccination/static/description:
acs.png         hms_menus_odoo_almightycs.png              icon.png
almightycs.png  hms_vaccination_almightycs_odoo_cover.jpg  index.html

./z4/acs_hms_vaccination/views:
menu_item.xml  res_config.xml  vaccination_view.xml

./z4/acs_hms_vaccination/wizard:
__init__.py  create_vaccination.py  create_vaccination_view.xml

./z4/acs_hms_veterinary:
__init__.py      data/  doc/   models/    static/
__manifest__.py  demo/  i18n/  security/  views/

./z4/acs_hms_veterinary/data:
data.xml

./z4/acs_hms_veterinary/demo:
pet_breed_demo.xml  pet_demo.xml
pet_color_demo.xml  pet_type_demo.xml

./z4/acs_hms_veterinary/doc:
changelog.rst

./z4/acs_hms_veterinary/i18n:
acs_hms_veterinary.pot  es.po  es_PA.po

./z4/acs_hms_veterinary/models:
__init__.py  acs_hms.py  acs_veterinary.py  acs_veterinary_base.py

./z4/acs_hms_veterinary/security:
ir.model.access.csv  security.xml

./z4/acs_hms_veterinary/static:
description/  src/

./z4/acs_hms_veterinary/static/description:
acs.png                                  almightycs.png
acs_hms_almightycs_happy_puppies.jpg     icon.png
acs_hms_almightycs_happy_puppy.jpg       index.html
acs_hms_veterinary_almightycs_cover.jpg

./z4/acs_hms_veterinary/static/src:
img/

./z4/acs_hms_veterinary/static/src/img:
pet_img_0.jpg  pet_img_2.jpg   pet_img_4.jpg   pet_placeholder.jpg
pet_img_1.jpg  pet_img_3.jpeg  pet_img_5.jpeg

./z4/acs_hms_veterinary/views:
acs_hms_views.xml             acs_veterinary_view.xml
acs_veterinary_base_view.xml  menu_item.xml

./z4/acs_hms_video_call:
__init__.py      data/  i18n/    security/  view/
__manifest__.py  doc/   models/  static/

./z4/acs_hms_video_call/data:
mail_template.xml

./z4/acs_hms_video_call/doc:
changelog.rst

./z4/acs_hms_video_call/i18n:
acs_hms_jitsi.pot  acs_hms_video_call.pot  es.po  es_EC.po  es_PA.po

./z4/acs_hms_video_call/models:
__init__.py  acs_hms.py  acs_video_call.py

./z4/acs_hms_video_call/security:
ir.model.access.csv  security.xml

./z4/acs_hms_video_call/static:
description/

./z4/acs_hms_video_call/static/description:
acs.png                                               almightycs.png
acs_almightycs_video_consultation_hms_jitsi_odoo.png  icon.png
acs_jitsi_meet_almightycs_cover.jpg                   index.html

./z4/acs_hms_video_call/view:
acs_hms_views.xml  acs_video_call_view.xml

./z4/acs_hms_webcam:
__init__.py  __manifest__.py  doc/  i18n/  models/  static/  view/

./z4/acs_hms_webcam/doc:
changelog.rst

./z4/acs_hms_webcam/i18n:
acs_hms_webcam.pot  es.po  es_EC.po  es_PA.po

./z4/acs_hms_webcam/models:
__init__.py  acs_webcam.py

./z4/acs_hms_webcam/static:
description/

./z4/acs_hms_webcam/static/description:
acs.png                          almightycs.png  index.html
acs_webcam_almightycs_cover.jpg  icon.png

./z4/acs_hms_webcam/view:
hms_view.xml

./z4/acs_invoice_split:
__init__.py      doc/   models/    static/  wizard/
__manifest__.py  i18n/  security/  views/

./z4/acs_invoice_split/doc:
changelog.rst

./z4/acs_invoice_split/i18n:
acs_invoice_split.pot  es_EC.po  es_PA.po

./z4/acs_invoice_split/models:
__init__.py  account.py

./z4/acs_invoice_split/security:
ir.model.access.csv

./z4/acs_invoice_split/static:
description/

./z4/acs_invoice_split/static/description:
acs.png
acs_invoice_split_odoo_almightycs.png
acs_split_invoice_almightycs_cover.jpg
acs_split_wizard_by_quantity_odoo_almightycs.png
acs_split_wizard_by_unit_price_odoo_almightycs.png
acs_split_wizard_odoo_almightycs.png
icon.png
index.html

./z4/acs_invoice_split/views:
account_view.xml

./z4/acs_invoice_split/wizard:
__init__.py  split_wizard.py  split_wizard_view.xml

./z4/acs_jitsi_meet:
__init__.py      controllers/  doc/   models/    static/
__manifest__.py  data/         i18n/  security/  view/

./z4/acs_jitsi_meet/controllers:
__init__.py  main.py

./z4/acs_jitsi_meet/data:
data.xml

./z4/acs_jitsi_meet/doc:
changelog.rst

./z4/acs_jitsi_meet/i18n:
acs_jitsi_meet.pot  es_EC.po  es_PA.po

./z4/acs_jitsi_meet/models:
__init__.py  acs_video_call.py  res_config.py

./z4/acs_jitsi_meet/security:
ir.model.access.csv

./z4/acs_jitsi_meet/static:
description/  src/

./z4/acs_jitsi_meet/static/description:
acs.png
acs_almightycs_video_call_calendar_jitsi_odoo.png
acs_almightycs_video_call_jitsi_odoo.png
acs_jitsi_meet_almightycs_cover.jpg
icon.png
index.html

./z4/acs_jitsi_meet/static/src:
js/  xml/

./z4/acs_jitsi_meet/static/src/js:
jitsi.js  systray.js

./z4/acs_jitsi_meet/static/src/xml:
systray.xml

./z4/acs_jitsi_meet/view:
res_config_view.xml  templates_view.xml

./z4/acs_laboratory:
__init__.py      controllers/  doc/   models/  security/  views/
__manifest__.py  data/         i18n/  report/  static/

./z4/acs_laboratory/controllers:
__init__.py  main.py

./z4/acs_laboratory/data:
digest_data.xml           lab_uom_data.xml     laboratory_demo.xml
lab_sample_type_data.xml  laboratory_data.xml  mail_template.xml

./z4/acs_laboratory/doc:
changelog.rst

./z4/acs_laboratory/i18n:
acs_laboratory.pot  ar.po  es.po  es_EC.po  es_PA.po  fr.po

./z4/acs_laboratory/models:
__init__.py  hms_base.py     lab_test.py         res_config.py
digest.py    lab_request.py  laboratory_base.py

./z4/acs_laboratory/report:
lab_report.xml           paper_format.xml
lab_request_results.xml  report_acs_lab_prescription.xml
lab_samples_report.xml

./z4/acs_laboratory/security:
ir.model.access.csv  security.xml

./z4/acs_laboratory/static:
description/

./z4/acs_laboratory/static/description:
acs.png                              icon.png
almightycs.png                       index.html
hms_laboratory_almightycs_cover.jpg  radiology_icon.png
hms_menus_odoo_almightycs.png

./z4/acs_laboratory/views:
digest_view.xml                   laboratory_view.xml
hms_base_view.xml                 menu_item.xml
lab_uom_view.xml                  portal_template.xml
laboratory_patient_test_view.xml  radiology_view.xml
laboratory_request_view.xml       res_config.xml
laboratory_sample_view.xml        templates_view.xml
laboratory_test_view.xml

./z4/acs_pharmacy:
__init__.py      data/  i18n/   report/    static/  wizard/
__manifest__.py  doc/   model/  security/  views/

./z4/acs_pharmacy/data:
data.xml

./z4/acs_pharmacy/doc:
changelog.rst

./z4/acs_pharmacy/i18n:
acs_pharmacy.pot  ar.po  es.po  es_PA.po

./z4/acs_pharmacy/model:
__init__.py  invoice.py  product.py  stock.py

./z4/acs_pharmacy/report:
__init__.py                 paper_format.xml
lot_barcode_report.py       picking_barcode_report.py
lot_barcode_report.xml      picking_barcode_report.xml
medicine_expiry_report.xml  report_invoice.xml

./z4/acs_pharmacy/security:
ir.model.access.csv  security.xml

./z4/acs_pharmacy/static:
description/

./z4/acs_pharmacy/static/description:
acs.png         hms_menus_odoo_almightycs.png           icon.png
almightycs.png  hms_pharmacy_almightycs_odoo_cover.jpg  index.html

./z4/acs_pharmacy/views:
invoice_view.xml  menu_item.xml  product_view.xml  stock_view.xml

./z4/acs_pharmacy/wizard:
__init__.py       wiz_lock_lot_view.xml
stock_wizard.py   wiz_medicine_expiry.py
stock_wizard.xml  wiz_medicine_expiry_view.xml
wiz_lock_lot.py

./z4/acs_product_barcode_generator:
__init__.py      data/  i18n/    static/
__manifest__.py  doc/   models/  views/

./z4/acs_product_barcode_generator/data:
data.xml

./z4/acs_product_barcode_generator/doc:
changelog.rst

./z4/acs_product_barcode_generator/i18n:
acs_product_barcode_generator.pot  ar_AA.po  es_PA.po

./z4/acs_product_barcode_generator/models:
__init__.py  product.py

./z4/acs_product_barcode_generator/static:
description/

./z4/acs_product_barcode_generator/static/description:
acs.png
barcode_configuration_company_odoo_almightycs.png
barcode_configuration_product_category_odoo_almightycs.png
barcode_configuration_product_odoo_almightycs.png
barcode_generator_cover_almightycs_odoo.jpg
icon.png
index.html
multiple_product_barcode_generation_odoo_almightycs.png

./z4/acs_product_barcode_generator/views:
product_view.xml

./z4/acs_sms:
__init__.py      data/  i18n/    security/  views/
__manifest__.py  doc/   models/  static/

./z4/acs_sms/data:
data.xml

./z4/acs_sms/doc:
changelog.rst

./z4/acs_sms/i18n:
acs_sms.pot  ar.po

./z4/acs_sms/models:
__init__.py  announcement.py  company.py  partner.py  sms.py

./z4/acs_sms/security:
ir.model.access.csv  security.xml

./z4/acs_sms/static:
description/

./z4/acs_sms/static/description:
acs.png                                            almightycs.png
acs_employee_sms_announcement_odoo_almightycs.png  chat.png
acs_sms_almightycs_cover.jpg                       email.png
acs_sms_configuation_odoo_almightycs.png           icon.png
acs_sms_smart_button_odoo_almightycs.png           index.html
acs_sms_template_odoo_almightycs.png

./z4/acs_sms/views:
announcement_view.xml  menu_item.xml     sms_view.xml
company_view.xml       partner_view.xml

./z4/acs_video_call:
__init__.py      data/  i18n/    security/  view/
__manifest__.py  doc/   models/  static/

./z4/acs_video_call/data:
calendar_mail_template.xml  data.xml  mail_template.xml

./z4/acs_video_call/doc:
changelog.rst

./z4/acs_video_call/i18n:
acs_video_call.pot  es.po  es_EC.po  es_PA.po

./z4/acs_video_call/models:
__init__.py  acs_video_call.py

./z4/acs_video_call/security:
ir.model.access.csv  security.xml

./z4/acs_video_call/static:
description/  src/

./z4/acs_video_call/static/description:
acs.png                                      icon.png
acs_almightycs_video_call_calendar_odoo.png  icon_old.png
acs_almightycs_video_call_odoo.png           index.html
acs_video_call_almightycs_cover.png

./z4/acs_video_call/static/src:
js/  xml/

./z4/acs_video_call/static/src/js:
systray.js

./z4/acs_video_call/static/src/xml:
systray.xml

./z4/acs_video_call/view:
acs_video_call_view.xml  calendar_view.xml

./z4/acs_webcam:
README.md    __manifest__.py  doc/   models/  view/
__init__.py  controllers/     i18n/  static/

./z4/acs_webcam/controllers:
__init__.py  main.py

./z4/acs_webcam/doc:
changelog.rst

./z4/acs_webcam/i18n:
acs_hms_webcam.pot  acs_webcam.pot  es.po  es_EC.po  es_PA.po

./z4/acs_webcam/models:
__init__.py  acs_webcam.py

./z4/acs_webcam/static:
description/  src/

./z4/acs_webcam/static/description:
acs.png
acs_almightycs_webcam_image_form_odoo.png
acs_almightycs_webcam_image_odoo.png
acs_webcam_almightycs_cover.jpg
icon.png
index.html

./z4/acs_webcam/static/src:
css/  js/

./z4/acs_webcam/static/src/css:
bootstrap.min.css  custom.css

./z4/acs_webcam/static/src/js:
button.js  webcam.js

./z4/acs_webcam/view:
acs_webcam_view.xml  templates_view.xml

./z4/acs_whatsapp:
__init__.py      data/  i18n/    security/  views/
__manifest__.py  doc/   models/  static/    wizard/

./z4/acs_whatsapp/data:
data.xml

./z4/acs_whatsapp/doc:
changelog.rst

./z4/acs_whatsapp/i18n:
acs_whatsapp.pot  es.po  es_PA.po

./z4/acs_whatsapp/models:
__init__.py  announcement.py  message.py  partner.py

./z4/acs_whatsapp/security:
ir.model.access.csv  security.xml

./z4/acs_whatsapp/static:
description/  src/

./z4/acs_whatsapp/static/description:
acs.png                                           almightycs.png
acs_contact_whatsapp_options_odoo_almightycs.png  icon.png
acs_odoo_whatsapp_almightycs_cover.jpg            index.html

./z4/acs_whatsapp/static/src:
scss/

./z4/acs_whatsapp/static/src/scss:
custom_backend.scss

./z4/acs_whatsapp/views:
announcement_view.xml  company_view.xml  message_view.xml
assets.xml             menu_item.xml     partner_view.xml

./z4/acs_whatsapp/wizard:
__init__.py                       whatsapp_messages.py
create_whatsapp_message.py        whatsapp_messages_view.xml
create_whatsapp_message_view.xml

./z4/acs_whatsapp_chatapi:
__init__.py  __manifest__.py  doc/  i18n/  models/  static/  views/

./z4/acs_whatsapp_chatapi/doc:
changelog.rst

./z4/acs_whatsapp_chatapi/i18n:
acs_whatsapp_chatapi.pot  es.po  es_PA.po

./z4/acs_whatsapp_chatapi/models:
__init__.py  company.py  message.py

./z4/acs_whatsapp_chatapi/static:
description/

./z4/acs_whatsapp_chatapi/static/description:
acs.png                                           almightycs.png
acs_contact_whatsapp_options_odoo_almightycs.png  icon.png
acs_odoo_whatsapp_almightycs_cover.jpg            index.html

./z4/acs_whatsapp_chatapi/views:
company_view.xml

DELL@DESKTOP-C9C0T1T MINGW64 ~/vetlane/vetlane/acs-addons-upgraded (main)
$

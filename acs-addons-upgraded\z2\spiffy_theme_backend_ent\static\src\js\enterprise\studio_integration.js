/** @odoo-module **/

import { Component, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

export class StudioIntegration extends Component {
    static template = "spiffy_theme_backend_ent.StudioIntegration";

    setup() {
        this.orm = useService("orm");
        this.state = useState({
            studioMode: false,
        });

        onMounted(() => {
            this._bindEvents();
            this._updateEnterpriseFeatures();
        });

        onWillUnmount(() => {
            this._cleanupStudioFeatures();
        });
    }

    _bindEvents() {
        const toggleButton = document.querySelector('.o_studio_mode_toggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', this._onStudioModeToggle.bind(this));
        }
    }

    async _onStudioModeToggle(ev) {
        ev.preventDefault();
        this.state.studioMode = !this.state.studioMode;

        try {
            await this.orm.write('backend.config', [1], {
                studio_mode: this.state.studioMode
            });
            this._updateEnterpriseFeatures();
        } catch (error) {
            console.error('Failed to update studio mode:', error);
        }
    }

    _updateEnterpriseFeatures() {
        const body = document.body;
        if (this.state.studioMode) {
            body.classList.add('o_studio_mode');
            this._initializeStudioFeatures();
        } else {
            body.classList.remove('o_studio_mode');
            this._cleanupStudioFeatures();
        }
    }

    async _initializeStudioFeatures() {
        // Initialize studio-specific features
        try {
            const features = await this.orm.call('backend.config', 'get_studio_features', []);
            this._applyStudioFeatures(features);
        } catch (error) {
            console.error('Failed to initialize studio features:', error);
        }
    }

    _cleanupStudioFeatures() {
        // Cleanup studio-specific features
        this._removeStudioClasses();
    }

    _applyStudioFeatures(features) {
        // Apply studio features to the view
        if (features.customization_enabled) {
            this._enableCustomization();
        }
        if (features.sidebar_mode) {
            this._updateSidebarMode(features.sidebar_mode);
        }
    }

    _enableCustomization() {
        const views = document.querySelectorAll('.o_form_view, .o_list_view, .o_kanban_view');
        views.forEach(view => view.classList.add('o_studio_customizable'));
    }

    _updateSidebarMode(mode) {
        const body = document.body;
        body.classList.remove('o_studio_sidebar_expanded', 'o_studio_sidebar_collapsed');
        body.classList.add(`o_studio_sidebar_${mode}`);
    }

    _removeStudioClasses() {
        const customizableElements = document.querySelectorAll('.o_studio_customizable');
        customizableElements.forEach(el => el.classList.remove('o_studio_customizable'));

        const body = document.body;
        body.classList.remove('o_studio_sidebar_expanded', 'o_studio_sidebar_collapsed');
    }
}

registry.category("services").add("studioIntegration", {
    dependencies: ["orm"],
    start(env, { orm }) {
        return new StudioIntegration();
    },
});
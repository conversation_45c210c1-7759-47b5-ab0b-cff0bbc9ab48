// Enterprise List View Styles
@import 'variables';

.o_list_view {
    .o_list_table {
        thead {
            background-color: $o-enterprise-list-header-bg;
            color: $o-enterprise-list-header-text;
            position: sticky;
            top: 0;
            z-index: 1;

            th {
                height: $o-enterprise-list-row-height;
                padding: 8px 12px;
                border-bottom: none;
                font-weight: 500;
            }
        }

        tbody {
            tr {
                height: $o-enterprise-list-row-height;

                td {
                    padding: 8px 12px;
                    vertical-align: middle;

                    &.o_list_record_selector {
                        width: 40px;
                        padding-left: 16px;
                    }
                }

                &:hover {
                    background-color: rgba(0, 0, 0, 0.02);
                }

                &.o_selected_row {
                    background-color: $o-enterprise-color-main;
                    color: $o-enterprise-color-text;
                }
            }
        }
    }

    // Studio integration
    &.o_studio_list {
        .o_list_table {
            thead {
                background-color: $o-enterprise-studio-bg;
                color: $o-enterprise-studio-text;
            }
        }
    }
} 
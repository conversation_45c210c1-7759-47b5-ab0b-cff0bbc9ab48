// Enterprise Kanban View Styles
@import 'variables';

.o_kanban_view {
    background-color: $o-enterprise-kanban-bg;
    padding: 16px;

    .o_kanban_record {
        margin: $o-enterprise-kanban-card-margin;
        border-radius: $o-enterprise-border-radius;
        box-shadow: $o-enterprise-box-shadow;
        background-color: white;
        
        &:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .o_kanban_record_title {
            color: $o-enterprise-color-text;
            font-weight: 500;
        }

        .o_kanban_record_subtitle {
            color: rgba($o-enterprise-color-text, 0.7);
        }
    }

    .o_kanban_group {
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: $o-enterprise-border-radius;
        padding: 8px;
        margin: 0 4px;
        min-width: $o-enterprise-kanban-column-width;

        .o_kanban_header {
            color: $o-enterprise-color-text;
            font-weight: 500;
            margin-bottom: 8px;
        }
    }

    // Studio integration
    &.o_studio_kanban {
        .o_kanban_record {
            &.o_studio_selected {
                border: 2px solid $o-enterprise-color-main;
            }
        }

        .o_kanban_group {
            &.o_studio_selected {
                background-color: rgba($o-enterprise-color-main, 0.1);
            }
        }
    }
} 
// -*- coding: utf-8 -*-
// Developed by Bizople Solutions Pvt. Ltd.
// See LICENSE file for full copyright and licensing details
body.o_web_client{
    @media (max-width: 767.98px) {
        .o_control_panel .o_cp_bottom_left, .o_control_panel .o_cp_bottom_right{
            flex-shrink: unset !important;
        }
    }
    @media (min-width: 768px) and (max-width: 1199.98px) {
        .o_control_panel .o_cp_top .o_cp_top_left, .o_control_panel .o_cp_top .o_cp_top_right, .o_control_panel .o_cp_bottom .o_cp_bottom_left, .o_control_panel .o_cp_bottom .o_cp_bottom_right{
            width: 100% !important;
            padding-bottom: 10px;
        }
        .o_form_view_container .o_control_panel .o_cp_top{
            display: block !important;
        }
    }
    .o_form_view_container .o_control_panel {
        .o_control_panel_breadcrumbs{
            .o_form_status_indicator{
                .o_form_status_indicator_buttons{
                    button{
                        background-color: transparent !important;
                        color: inherit !important;
                        border-color: transparent !important;
                        box-shadow: unset !important;
                    }
                }
            }
        }
    }
    .o_list_selection_box {
        .o_list_select_domain {
            font-weight: 400 !important;
            color: var(--biz-theme-primary-text-color) !important;

            a {
                color: var(--biz-theme-primary-text-color) !important;
            }
        }
    }
    .o_form_view_container{
        .o_control_panel{
            .o_control_panel_main{
                flex-wrap: nowrap !important;
                align-items: center !important;
            }
            .o_last_breadcrumb_item{
                font-size: 1.2rem !important;
                color: var(--biz-theme-body-text-color) !important;
                .text-truncate{
                    overflow: visible;
                    white-space: break-spaces;
                }
            }
            .o_control_panel_breadcrumbs_actions{
                display: flex !important;
            }
        }
    }
    .o_control_panel{
        .o_control_panel_main{
            align-items: center !important;
        }
        @include media-breakpoint-up(lg) {
            .o_cp_searchview {
                min-width: 250px !important;
            }
        }
        .o_control_panel_breadcrumbs{
            flex-wrap: wrap !important;
        }
        .fa{
            font-size: 16px;
            vertical-align: middle;
            line-height: 24px;
        }

        background-color: var(--biz-theme-body-color) !important;
        color: var(--biz-theme-body-text-color) !important;

        .btn{
            box-shadow: var(--box-shadow-common) !important;
            -moz-box-shadow: var(--box-shadow-common) !important;
            -webkit-box-shadow: var(--box-shadow-common) !important;
        }

        .o_form_buttons_view, .o_form_buttons_edit{
            > button::first-child{
                margin-right: 0.75rem;
            }
        }

        .o_form_statusbar {
            background-color: transparent !important;

            .o_statusbar_buttons{
                .btn{
                    padding: 0.6rem 1rem;

                    &:not(:first-child){
                        margin-left: 8px;
                    }
                }
            }
        }

        .o_form_buttons_edit {
            display: flex;
            align-items: center;
        }
        .o_cp_action_menus{
           .btn{
                color: var(--biz-theme-body-text-color) !important;
           } 
        }
        .breadcrumb {
            background-color: transparent !important;
            align-items: baseline;
            .breadcrumb-item {
                font-size: 1rem;
                a {
                    color: var(--biz-theme-primary-color) !important;
                }
            }
            // .breadcrumb-item.o_back_button {
            //     a {
            //         font-size: 1.5rem;
            //         color: var(--biz-theme-primary-color) !important;
            //     }
            // }
            // li {
            //     max-width: 100% !important;
            // }
        }
        .o_last_breadcrumb_item{
            font-size: 1.2rem !important;
            color: var(--biz-theme-body-text-color) !important;
        }
        .o_panel {
            .o_setting_search {
                background-color: var(--biz-theme-secondary-color) !important;
                box-shadow: var(--box-shadow-common) !important;
                border: 0 !important;
                border-radius: var(--border-radius-md) !important;
                padding: 1px 10px 3px 10px;
                .searchInput{
                    max-width: unset;
                    height: 28px;
                    padding: 0 !important;
                }

                input, .searchIcon {
                    background-color: transparent !important;
                    color: var(--biz-theme-body-text-color) !important;
                    border-color: var(--biz-theme-body-text-color) !important;
                }
                .searchIcon {
                    right: 20px;
                }
            }
        }
        .o_cp_pager{
            .btn-group{
                gap: 8px;
                .btn.o_pager_previous,.btn.o_pager_next{
                    @media (max-width: 767.98px) {
                        border: none !important;
                        background: transparent !important;
                        padding: 0 !important;
                    }
                }
            }
            .active{
                background-color: #008000 !important;
                border-color: #008000 !important;
            }
        }
        .o_cp_switch_buttons {
            .btn {
                // border: none !important;
                i{  
                    line-height: 20px;
                    vertical-align: middle;
                }
                &.active,&:active {
                    background-color: var(--biz-theme-primary-color) !important;
                    color: var(--biz-theme-primary-text-color) !important;
                    border-color: var(--biz-theme-primary-color) !important;
                }
            }
            gap: 8px;
            @include media-breakpoint-down(md){
                .dropdown-menu-right {
                    li {
                        button {
                            padding: 2px 7px !important;
                        }
                        font-size: 12px;
                    }
                }
            }
        }

        .o_cp_searchview {
            .o_searchview {
                background-color: white !important;
                box-shadow: var(--box-shadow-common) !important;
                border: 0 !important;
                border-radius: var(--border-radius-md);
                padding: 1px 10px 3px 10px;
                .o_searchview_input{
                    font-size: 0.9rem !important;
                }
                i {
                    cursor: pointer !important;
                }

                .o_searchview_icon {
                    display: none !important;
                }

                .o_searchview_autocomplete li.o_selection_focus {
                    background-color: var(--biz-theme-primary-color);
                    > a {
                        color: var(--biz-theme-primary-text-color);
                    }
                }

                input {
                    font-size: 1.1rem !important;
                    padding-left: 4px;
                    min-height: calc(var(--input-height) - 8px); // because --input-height is 40px
                }

                .o_searchview_facet {
                    border: 1px solid #cccccc7d !important;
                    background-color: transparent !important;
                    border-radius: var(--border-radius-md) !important;
                    margin: 1px 8px 0 0 !important;
                    overflow: hidden;
                    .position-absolute{
                        line-height: 26px;
                    }
                    .o_facet_remove {
                        color: var(--biz-theme-primary-color) !important;
                    }

                    .o_searchview_facet_label {
                        background-color: var(--biz-theme-primary-color) !important;
                        color: var(--biz-theme-primary-text-color);
                        border-top-left-radius: var(--border-radius-md) !important;
                        border-bottom-left-radius: var(--border-radius-md) !important;
                        border-top-right-radius: 0 !important;
                        border-bottom-right-radius: 0 !important;
                        padding: 2px 5px !important;
                        overflow: hidden;
                        border: none !important;
                        line-height: 1.5rem;
                    }
                    .rounded-2{
                        border-radius: var(--border-radius-md) !important;
                    }
                    .o_facet_values {
                        border: 0;
                        display: flex;
                        align-items: center;
                        background-color: transparent !important;
                        color: var(--biz-theme-secondary-text-color) !important;
                        border-color: var(--biz-theme-primary-color) !important;
                    }
                }
            }
            .dropdown-toggle{
                background-color: var(--biz-theme-secondary-color) !important;
                border-color: var(--biz-theme-secondary-color) !important;
                color: var(--biz-theme-secondary-text-color) !important;
                border-radius: 0 !important;
                border-top-right-radius: var(--border-radius-md) !important;
                border-bottom-right-radius: var(--border-radius-md) !important;
                line-height: 15px;
                padding: 0 10px !important; 
            }
            .btn-group.d-none.d-md-inline-block.float-right {
                margin-right: 0 !important;
            }
        }

        @include media-breakpoint-down(sm){
            //breadcrumb start
            .breadcrumb {
                background-color: transparent !important;
                align-items: baseline;
                .breadcrumb-item {
                    a {
                        font-size: 1.5rem;
                    }
                }
                .breadcrumb-item.active {
                    font-size: 1.5rem;
                }
            }
            //breadcrumb end
            .o_cp_bottom {
                .o_cp_bottom_left {
                    > .o_cp_action_menus {
                        .o_dropdown_title, .fa-chevron-right, .fa-chevron-down {
                            display: none;
                        }
                        padding-left: 0;
                    }
                }
                .o_cp_bottom_right {
                    .o_cp_action_menus {
                        button.dropdown-toggle {
                            padding: 0.6rem 1rem !important;
                            height: 35.5px;
                            margin-right: 0px !important;
                        }
                        margin-top: 5px;
                        display: flex;
                        justify-content: end;
                        gap: 8px;
                    }
                    margin-left: auto;
                    gap: 8px;
                }
                flex-wrap: wrap;
            }
            .o_calendar_buttons {
                .o_calendar_navigation_buttons {
                    margin-bottom: 5px;
                }
                flex-direction: column;
            }
            // calender views end
            
            // pivot views start
            .btn-group[aria-label='Pivot settings'] {
                gap: 8px;
            }
            // pivot views end

            // list views start
            .o_list_buttons .o_button_generate_leads {
                display: none !important;
            }
            // list views end
        }
    }

    &.dark_mode{
        .o_control_panel{
            .o_panel{
                .o_setting_search{
                    background-color: var(--biz-theme-secondary-color) !important;
                }
            }
            .o_cp_searchview {
                .o_searchview {
                    background-color: var(--biz-theme-secondary-color) !important;
                }
        
            }
        } 
    }
}
// Enterprise Search Styles
@import 'variables';

.o_searchview {
    border: none;
    padding: 4px 25px 4px 8px;
    background-color: $o-enterprise-search-bg;
    border-radius: $o-enterprise-border-radius;
    box-shadow: $o-enterprise-box-shadow;

    .o_searchview_input_container {
        .o_searchview_input {
            border: none;
            padding: 4px 0;
            color: $o-enterprise-color-text;
            background: none;

            &::placeholder {
                color: rgba($o-enterprise-color-text, 0.5);
            }
        }

        .o_searchview_facet {
            background-color: $o-enterprise-color-main;
            border: none;
            border-radius: $o-enterprise-border-radius-sm;
            margin: 1px 3px;
            padding: 0 18px 0 6px;

            .o_searchview_facet_label {
                background-color: rgba(255, 255, 255, 0.15);
                padding: 0 6px;
                border-radius: $o-enterprise-border-radius-sm 0 0 $o-enterprise-border-radius-sm;
                color: white;
            }

            .o_facet_values {
                padding: 0 6px;
                color: white;
            }

            .o_facet_remove {
                color: rgba(255, 255, 255, 0.8);
                &:hover { color: white; }
            }
        }
    }

    .o_searchview_autocomplete {
        border: none;
        box-shadow: $o-enterprise-box-shadow;
        border-radius: $o-enterprise-border-radius;
        overflow: hidden;
        margin-top: 4px;

        li {
            padding: 8px 12px;
            color: $o-enterprise-color-text;

            &.o-selection-focus {
                background-color: rgba($o-enterprise-color-main, 0.1);
            }

            a {
                color: inherit;
                &:hover { color: $o-enterprise-color-main; }
            }
        }
    }
}

// Studio integration
.o_studio_mode {
    .o_searchview {
        &.o_studio_selected {
            border: 2px solid $o-enterprise-color-main;
        }
    }
} 